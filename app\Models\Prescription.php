<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use Carbon\Carbon;

class Prescription extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'user_id',
        'clinic_id',
        'patient_id',
        'prescription',
        'doctor_id',
        'doctor_approval',
        'admin_approval',
        'status',
        'prescription_type',
        'repetitions',
        'duration',
        'reject_comment',
        'admin_reject_comment',
        'total',
        'type'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            $prescription->order_id = self::generateUniqueOrderId();
        });
    }

    public static function generateUniqueOrderId()
    {
        do {
            $orderId = strtoupper(Str::random(3)) . rand(100, 999) . strtoupper(Str::random(2));
        } while (self::where('order_id', $orderId)->exists());

        return $orderId;
    }

    // Define the relationship to the PrescriptionInventory model
    public function prescriptionInventories()
    {
        return $this->hasMany(PrescriptionInventory::class);
    }

    // Define the relationship to the Inventory model through PrescriptionInventory
    public function inventories()
    {
        return $this->belongsToMany(Inventory::class, 'prescription_inventories')
            ->withPivot('prescribed_units', 'total_price', 'dosage', 'duration')
            ->withTimestamps();
    }

    // Define the relationship to the doctor
    public function doctor()
    {
        return $this->belongsTo(User::class, 'doctor_id');
    }
    public function prescribedBy()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function clinic()
    {
        return $this->belongsTo(User::class, 'clinic_id');
    }

    // Define the relationship to the patient
    public function patient()
    {
        return $this->belongsTo(User::class, 'patient_id');
    }

    public function payments()
    {
        return $this->hasMany(PrescriptionPayment::class);
    }

    /**
     * Get all prescription payments where today's date is between payment_date and valid_until
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function comingPayments()
    {
        $today = Carbon::today();

        return $this->hasMany(PrescriptionPayment::class)->whereDate('payment_date', '<=', $today)->whereDate('valid_until', '>=', $today);
    }

    // Update the payment status based on the current date
    public function updatePaymentStatus()
    {
        // Check for payments that are still pending and update them to 'paid' if valid
        $this->payments->each(function ($payment) {
            if ($payment->status == 'pending' && Carbon::parse($payment->valid_until)->isPast()) {
                $payment->update(['status' => 'overdue']);
            }
        });
    }
    public function getAdminApprovalTextAttribute()
    {
        $status = [
            0 => 'Pending',
            1 => 'Accepted',
            2 => 'Rejected',
        ];

        // Use $this->attributes['status'] ?? null to prevent undefined property errors
        return $status[$this->attributes['admin_approval'] ?? null] ?? 'Unknown';
    }

    public function invoices()
    {
        return $this->belongsToMany(Invoice::class, 'invoice_prescriptions');
    }
    public function getApprovalStatusAttribute()
    {
        if ($this->admin_approval) {
            return 'Approved by Pharmacy';
        } elseif ($this->doctor_approval) {
            return 'Waiting for Pharmacy';
        } else {
            return 'Waiting for Prescriber';
        }
    }

    /**
     * Get the payment status for the prescription
     *
     * @return array Returns an array with status and badge class
     */
    public function getPaymentStatusAttribute()
    {
        // Default values
        $status = 'Pending';
        $badgeClass = 'inactive';

        // Handle Type 2 prescriptions
        if ($this->type === 'Type 2') {
            return [
                'status' => 'Outsourced',
                'badge_class' => 'inactive'
            ];
        }

        // Handle Type 1 prescriptions
        if ($this->type === 'Type 1') {
            // Check one-time prescription
            if ($this->prescription_type === 'one_time' && $this->status === 1) {
                $status = 'Paid';
                $badgeClass = 'active';
            }
            // Check repeat prescription
            elseif ($this->prescription_type === 'repeat') {
                $isPaid = $this->comingPayments()->orderBy('id', 'DESC')->latest()->first()?->status === 'paid' ||
                    ($this->payments->count() > 0 &&
                        $this->payments->sortByDesc('payment_date')->first()->status === 'paid');

                if ($isPaid) {
                    $status = 'Paid';
                    $badgeClass = 'active';
                }
            }
        }

        return [
            'status' => $status,
            'badge_class' => $badgeClass
        ];
    }

    /**
     * Get the billing status for the prescription
     *
     * @return array Returns an array with label and badge class
     */
    public function getStatusTextAttribute()
    {
        // Define status mapping
        $statusMap = [
            'dispensed' => [
                'label' => 'Dispensed',
                'badge_class' => 'active'
            ],
            'approved' => [
                'label' => 'Approved by Pharmacy',
                'badge_class' => 'active'
            ],
            'rejected' => [
                'label' => 'Rejected by Pharmacy',
                'badge_class' => 'inactive'
            ],
            'pending' => [
                'label' => 'Pending',
                'badge_class' => 'inactive'
            ]
        ];

        // For Type 1 prescriptions, check comingPayments first
        if ($this->type === 'Type 1' && $this->prescription_type === 'repeat') {
            $comingPayment = $this->comingPayments()->orderBy('id', 'DESC')->latest()->first();

            if ($comingPayment && $comingPayment->is_dispensed) {
                return $statusMap['dispensed'];
            }

            switch ($this->admin_approval) {
                case 1:
                    return $statusMap['approved'];
                case 2:
                    return $statusMap['rejected'];
                default:
                    return $statusMap['pending'];
            }
        } else {
            if ($this->is_dispensed) {
                return $statusMap['dispensed'];
            }

            switch ($this->admin_approval) {
                case 1:
                    return $statusMap['approved'];
                case 2:
                    return $statusMap['rejected'];
                default:
                    return $statusMap['pending'];
            }
        }
    }
}
