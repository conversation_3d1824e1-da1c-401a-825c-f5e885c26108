<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class QueueScheduler extends Command
{
    protected $signature = 'app:queue-scheduler';
    protected $description = 'Process queued jobs once';

    public function handle()
    {
        try {
            // Process jobs ONCE, don't run indefinitely
            $output = Artisan::call('queue:work', [
                '--stop-when-empty' => true,  // Stop when no more jobs
                '--tries' => 3,
                '--timeout' => 45,
                '--max-jobs' => 5,           // Limit jobs per run
            ]);

            Log::info('Queue processed successfully');
            return 0;

        } catch (\Exception $e) {
            Log::error('Queue processing failed: ' . $e->getMessage());
            return 1;
        }
    }
}