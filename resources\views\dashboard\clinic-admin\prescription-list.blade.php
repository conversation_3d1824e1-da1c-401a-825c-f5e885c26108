@foreach ($prescriptions as $prescription)
    <tr>
        <!-- <td><input type="checkbox" class="row-select"></td> -->
        <td>
            <a href="{{ route('prescription-details', $prescription->order_id) }}">
                <div class="d-flex gap-2 align-items-center">
                    <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg" alt=""
                        height="40px" width="40px">
                    <div class="d-flex flex-column">
                        <h5 class="fw-500 number-gray-black">
                            {{ $prescription->patient->name ?? '' }}</h5>
                        <span class="input-text-gray fs-14 fw-400">{{ $prescription->patient->email ?? '' }}</span>
                    </div>
                </div>
            </a>
        </td>

        <td>
            @if ($prescription->prescription_type)
                {{ ucfirst(str_replace('_', ' ', $prescription->prescription_type)) }}
            @else
                -
            @endif
        </td>

        <td>
            @if ($prescription->prescribedBy)
                {{ $prescription->prescribedBy->name ?? '' }}
            @else
                -
            @endif
        </td>
        <td>
            @if ($prescription->prescribedBy)
                {{ ucfirst(str_replace('_', ' ', $prescription->prescribedBy->profile->role ?? '')) }}
            @else
                -
            @endif
        </td>
        <td>{{ $prescription->type ?? '' }}</td>

        

        <td>
            @if ($prescription->admin_approval == 1)
                Pharmacy
            @elseif($prescription->doctor_approval == 1)
                Prescriber
            @else
                Pending
            @endif
        </td>
        <td>${{ $prescription->total }}</td>

        <td>{{ $prescription->created_at->format('M d, Y') }}</td>

        <td><span
                class="badge badge-{{ $prescription->status == 0 ? 'inactive' : 'active' }} roboto fs-14 fw-400">{{ $prescription->status == 0 ? 'Pending' : 'Paid' }}</span>
        </td>

        <td>
            <div class="dowpdown">
                <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <i class="fa-solid fa-ellipsis-vertical"></i>
                </a>
                <ul class="dropdown-menu main-submenu" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item Satoshi"
                            href="{{ route('prescription-details', $prescription->order_id) }}">View</a>
                    </li>
                    <li><a class="dropdown-item Satoshi"
                            href="{{ route('generate-prescription-pdf', ['orderId' => $prescription->order_id]) }}">
                          Download PDF
                        </a></li>
                </ul>
            </div>
        </td>
    </tr>
@endforeach
