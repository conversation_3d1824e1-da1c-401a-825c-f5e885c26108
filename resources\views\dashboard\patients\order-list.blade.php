@foreach ($orders as $order)
    <tr>
        <td>
            <a href="{{ route('order-details', $order->order_id) }}">

                <div class="d-flex gap-2 align-items-center">
                    <img src="{{ asset('website') }}/assets/media/images/patients-table-logo.svg"
                        alt="" height="40px" width="40px">
                    <div class="d-flex flex-column">
                        <h5 class="fw-500 number-gray-black">
                            {{ $order->doctor_id ? $order->doctor->name ?? '' : $order->prescribedBy->name ?? '' }}
                        </h5>
                        <span class="input-text-gray fs-14 fw-400">
                            {{ $order->doctor_id ? $order->doctor->email ?? '' : $order->prescribedBy->email ?? '' }}
                        </span>
                    </div>
                </div>
            </a>
        </td>
        <td>
            <span class="fs-14 fw-400">
                {{ strtoupper(str_replace('_', ' ', $order->prescription_type ?? '')) }}
            </span>
        </td>
        <td>{{ $order->created_at->format('d M, Y') ?? '' }}</td>
        <td>£{{ $order->total }}</td>
        <td>
            @php
    $billingStatus = 'Pending';
    $badgeClass = 'inactive';

    if ($order->type === 'Type 1') {
        if ($order->prescription_type === 'one_time') {
            if ($order->status === 1) {
                $billingStatus = 'Paid';
                $badgeClass = 'active';
            }
        } elseif ($order->prescription_type === 'repeat') {
            $comingPayment = $order->comingPayments()->first();
            if ($comingPayment && $comingPayment->status === 'paid') {
                $billingStatus = 'Paid';
                $badgeClass = 'active';
            } elseif (
                !$order->comingPayments()->exists() &&
                $order->payments->count() > 0 &&
                $order->payments->sortByDesc('payment_date')->first()->status === 'paid'
            ) {
                $billingStatus = 'Paid';
                $badgeClass = 'active';
            }
        }
    } else {
        $billingStatus = 'Outsourced';
    }
            @endphp
            <span class="badge badge-{{ $badgeClass }} roboto fs-14 fw-400 cursor-pointer">{{ $billingStatus }}</span>
        </td>
        <td>
            @php
    $statusData = $order->statusText;
    $statusText = $statusData['label'] ?? 'Pending';
    $statusClass = $statusData['badge_class'] ?? 'inactive';
            @endphp
            <span class="badge badge-{{ $statusClass }} roboto fs-14 fw-400 cursor-pointer">
                {{ $statusText }}
            </span>
        </td>


        <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                        href="{{ route('order-details', $order->order_id) }}">View</a>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>


        <!-- <td class="action_btn">
            <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
            </a>
            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                data-kt-menu="true">
                <div class="menu-item px-3">
                    <a class="menu-link px-3 view-prescription"
                        href="{{ route('order-details', $order->order_id) }}">View</a>
                </div>
            </div>
        </td> -->
    </tr>
@endforeach
