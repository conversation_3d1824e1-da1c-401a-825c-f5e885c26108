<?php $__env->startPush('css'); ?>
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
<?php $__env->stopPush(); ?>

<div id="kt_app_content" class="app-content">
    <div id="kt_app_content_container" class="app-container py-5">
        <div class="row">
            <div class="col-lg-12">
                <h4 class="mb-3 heading-gary"> Overview</h4>
            </div>
        </div>
        <div class="row clinic_index">
            <div class="col-md-3">
                <a href="<?php echo e(route('clinic.staff.list')); ?>" class="number-gray-black fs-21 fw-700">
                    <div class="purple-card h-100">
                        <div class="d-flex justify-content-between">
                            <p class="fs-12 fw-600 input-text-gray"> TOTAL STAFF </p><i
                                class="fas fa-chevron-right"></i>
                        </div>

                        <div class="d-flex justify-content-between staff_percent">
                            <div class="fs-21 fw-700" data-kt-countup="true"
                                data-kt-countup-value="<?php echo e($dashboardData['totalStaff']); ?>" data-kt-countup-prefix="">0
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-md-3">
                <a href="<?php echo e(route('clinic.request')); ?>" class="number-gray-black fs-21 fw-700">
                    <div class="purple-card h-100">
                        <div class="d-flex justify-content-between">
                            <p class="fs-12 fw-600 input-text-gray"> TOTAL PATIENTS </p><i
                                class="fas fa-chevron-right"></i>
                        </div>

                        <div class="d-flex justify-content-between ">
                            <div class="fs-21 fw-700" data-kt-countup="true"
                                data-kt-countup-value="<?php echo e($dashboardData['totalPatients']); ?>" data-kt-countup-prefix="">
                                0
                            </div>

                        </div>
                    </div>
                </a>
            </div>

            <div class="col-md-3">
                <a href="<?php echo e(route('clinic.request')); ?>#clinic_receptionist" class="number-gray-black fs-21 fw-700">
                    <div class="purple-card h-100">
                        <div class="d-flex justify-content-between">
                            <p class="fs-12 fw-600 input-text-gray"> TOTAL PRESCRIPTIONS </p><i
                                class="fas fa-chevron-right"></i>
                        </div>

                        <div class="d-flex justify-content-between">
                            <div class="fs-21 fw-700" data-kt-countup="true"
                                data-kt-countup-value="<?php echo e($dashboardData['totalPrescriptions']); ?>"
                                data-kt-countup-prefix="">0</div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-md-3">
                <div class="purple-card light-green-bg h-100">
                    <div class="d-flex flex-column justify-content-center align-items-center add-member-group">
                        <i data-bs-toggle="modal" data-bs-target="#add_new_staff_modal" class="fas fa-plus mb-0"></i>
                        <h2 class="pt-3 mb-0 white-color">Add Staff Member</h2>
                    </div>
                </div>
            </div>
        </div>

        <div class="row my-7 mx-0">
            <div class="col-md-12 custom-dropdown">
                <div class=" roboto d-flex justify-content-between align-items-center py-5">
                    <div>
                        <h4 class=" mb-3 heading-gary"> Recent Patients </h4>

                    </div>
                    <div class=" roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                        <div>
                            <div class="result-container1">
                                <div class="range-options additional-options">
                                    <div
                                        class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group">
                                        <div class="start-range date-field">
                                            <label for="start-range">Start Range:</label>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem;">Please select Start Range
                                                first.</span>
                                            <input id="start-range" type="date" class="start-range-input"
                                                name="start-range" max="<?php echo e(\Carbon\Carbon::today()->toDateString()); ?>">
                                        </div>
                                        <div class="end-range date-field">
                                            <label for="end-range">End Range:</label>
                                            <input id="end-range" type="date" class="end-range-input"
                                                name="end-range" max="<?php echo e(\Carbon\Carbon::today()->toDateString()); ?>">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <a href="<?php echo e(route('clinic.request')); ?>"
                            class="fs-15 fw-500 mt-6  gradient_modal_approve text-white px-10"> VIEW ALL</a>
                    </div>
                </div>
                <div class="table-responsive">
                    <table id="clinicTable" class=" table display data_table gy-5 gs-5 sortTable">
                        <thead>
                            <tr>
                                
                                <th class="min-w-300px">Patient Name</th>
                                <th class="min-w-200px">Created By</th>
                                <th class="min-w-200px">Staff Role</th>
                                <th class="min-w-200px">No. of Prescription</th>
                                <th class="min-w-200px">Date Added</th>
                                <th class="min-w-200px first-and-last">Status</th>

                            </tr>
                        </thead>
                        <tbody class="table-group-divider">
                            <?php $__empty_1 = true; $__currentLoopData = $dashboardData['recentPatients']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    
                                    <td>
                                        <div class="d-flex gap-2 align-items-center">
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/table-featured-icon.svg"
                                                alt="" height="40px" width="40px">
                                            <div class="d-flex flex-column">
                                                <h5 class="fw-500 number-gray-black">
                                                    <?php echo e($item->name ?? ''); ?>

                                                </h5>
                                                <span
                                                    class="input-text-gray fs-14 fw-400"><?php echo e($item->email ?? ''); ?></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo e($item->createdBy->name ?? ''); ?></td>
                                    <td><?php echo e($item->createdBy->profile->role ?? ''); ?></td>
                                    <td><?php echo e($item->patientPrescriptions->count() ?? ''); ?></td>
                                    <td><?php echo e($item->created_at->format('M d, Y') ?? ''); ?></td>
                                    <td><span class="badge badge-<?php echo e($item->status == 1 ? 'active' : 'inactive'); ?> roboto fs-14 fw-400">
                                            <?php echo e($item->status == 1 ? 'Active' : 'In-Active' ?? ''); ?></span></td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center">No record found!</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="row mx-0">
            <div class="col-md-12 custom-dropdown ">
                <div class="roboto d-flex justify-content-between align-items-center py-5   ">
                    <div>
                        <h4 class=" mb-3 heading-gary"> Recent Staff </h4>
                    </div>
                    <div
                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                        <div>
                            <div class="custom-select">
                                <select id="" class="status_filter" data-control="select2"
                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                    <option value="" selected>All Staff</option>
                                    <option value="Receptionist">Receptionist</option>
                                    <option value="Doctor">Doctor</option>
                                </select>
                                <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                    alt="Filter Icon">
                            </div>
                        </div>
                        <a href="<?php echo e(route('clinic.staff.list')); ?>"
                            class="fs-15 fw-500  gradient_modal_approve text-white px-10"> VIEW ALL</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="row py-5" id="staffTableBody">
            <?php $__currentLoopData = $dashboardData['recentRegs']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4 mb-4">
                    <div class="purple-card">
                        <div class="d-flex justify-content-between">
                            <div class="d-flex">
                                <div>
                                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/reinger-icon.svg"
                                        alt="reinger-icon">
                                </div>
                                <div class="ms-2">
                                    <p class="fs-14 fw-500 mb-0"> <?php echo e($item->name ?? ''); ?> </p>
                                    <h6 class="heading-gary mb-0"> <?php echo e($item->email ?? ''); ?> </h6>
                                    <h6 class="heading-gary mb-0 pt-1"><?php echo e($item->created_at->format('m d Y') ?? ''); ?>

                                    </h6>
                                </div>
                            </div>
                            <div>
                                <p class=" <?php echo e($item->status == 1 ? 'light-green' : 'inactive-grey' ?? ''); ?>-badge me-3">
                                    <?php echo e($item->status == 1 ? 'Active' : 'In-Active' ?? ''); ?>

                                </p>
                            </div>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between pb-3">
                            <h6 class="mb-0">
                                <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/user_icon.svg"
                                        alt="user_icon" /></span>
                                Patients
                            </h6>
                            <h6> <?php echo e($item->users()->count() ?? ''); ?> </h6>
                        </div>
                        <div class="d-flex justify-content-between">
                            <h6 class="mb-0">
                                <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/prescriptions.svg"
                                        alt="prescriptions" /></span>
                                Prescriptions
                            </h6>
                            <h6> <?php echo e($item->ownPrescriptions()->count() ?? ''); ?> </h6>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

        </div>
    </div>
</div>

<?php echo $__env->make('dashboard.templates.modals.clinic-modals.new_staff_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script>
        $(document).ready(function() {
            $('#statusFilter').on('change', function() {
                var status = $(this).val();
                $.ajax({
                    url: "<?php echo e(route('clinic.staff.filter')); ?>",
                    type: "GET",
                    data: {
                        status: status,
                    },
                    success: function(response) {
                        $('#staffTableBody').empty(); // Clear existing rows

                        if (response.success && response.results.length > 0) {
                            let rows = '';
                            response.results.forEach(function(item) {
                                rows += `
                                        <div class="col-md-3 mb-4">
                                            <div class="purple-card">
                                                <div class="d-flex justify-content-between">
                                                    <div class="d-flex">
                                                        <div>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/reinger-icon.svg" alt="reinger-icon">
                                                        </div>
                                                        <div class="ms-2">
                                                            <p class="fs-14 fw-500 mb-0">${item.name || ''}</p>
                                                            <h6 class="heading-gary mb-0">${item.email || ''}</h6>
                                                            <h6 class="heading-gary mb-0 pt-1">${new Date(item.created_at).toLocaleDateString('en-GB') || ''}</h6>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <p class="light-green-badge me-3">${item.status === 1 ? 'Active' : 'In-Active'}</p>
                                                    </div>
                                                </div>
                                                <hr>
                                                <div class="d-flex justify-content-between pb-3">
                                                    <h6 class="mb-0">
                                                        <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/user_icon.svg" alt="user_icon" /></span>
                                                        Patients
                                                    </h6>
                                                    <h6>${item.users_count || ''}</h6>
                                                </div>
                                                <div class="d-flex justify-content-between">
                                                    <h6 class="mb-0">
                                                        <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/prescriptions.svg" alt="prescriptions" /></span>
                                                        Prescriptions
                                                    </h6>
                                                    <h6>${item.ownPrescriptions_count || ''}</h6>
                                                </div>
                                            </div>
                                        </div>
                                    `;
                            });

                            $('#staffTableBody').append(rows);
                        } else {
                            $('#staffTableBody').append(
                                '<div class="col-12 text-center">No results found.</div>'
                            );
                        }
                    },
                    complete: function() {
                        KTMenu.createInstances();
                    }
                });

            });
            $('.start-range-input').on('change', function() {
                $('.end-range-input').prop('disabled', false);
                var startDate = $(this).val();
                var endDate = $('.end-range-input').val();
                fetchData(page = 1, startDate, endDate);
            });
            function fetchData(page = 1, startDate = null, endDate = null) {
                $.ajax({
                    url: "<?php echo e(route('clinic.patient.filter')); ?>",
                    type: "GET",
                    data: {
                        page: page,
                        start_date: startDate,
                        end_date: endDate,
                    },
                    success: function(response) {
                        $('.table-group-divider').empty(); // Clear existing rows
                        if (response.success && response.results.length > 0) {
                            let rows = '';
                            response.results.forEach(function(item) {
                                rows += `
                                        <tr>
                                            <td><input type="checkbox" class="row-select"></td>
                                            <td>
                                                <div class="d-flex gap-2 align-items-center">
                                                    <img src="${window.location.origin}/website/assets/media/images/table-featured-icon.svg"
                                                        alt="" height="40px" width="40px">
                                                    <div class="d-flex flex-column">
                                                        <h5 class="fw-500 number-gray-black">${item.name ?? ''}</h5>
                                                        <span class="input-text-gray fs-14 fw-400">${item.email ?? ''}</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>${item.created_by?.name ?? ''}</td>
                                            <td>${item.created_by?.profile?.role ?? ''}</td>
                                            <td>${item.patient_prescriptions?.length ?? 0}</td>
                                            <td>${item.created_at}</td>
                                            <td>
                                                <span class="badge badge-${item.status == 1 ? 'active' : 'inactive'} roboto fs-14 fw-400">
                                                    ${item.status == 1 ? 'Active' : 'In-Active'}
                                                </span>
                                            </td>
                                        </tr>
                                    `;
                            });

                            $('.table-group-divider').append(rows);
                        } else {
                            $('.table-group-divider').append(
                                '<tr><td colspan="7" class="text-center">No results found.</td></tr>'
                            );
                        }
                    },
                    complete: function() {
                        KTMenu.createInstances();
                    }
                });
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\git\rx-direct\resources\views/dashboard/templates/index/clinic_index.blade.php ENDPATH**/ ?>