<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Invoice extends Model
{
    use HasFactory;
    protected $fillable = [
        'invoice_id',
        'user_id',
        'month',
        'type',
        'total_amount',
        'cashback_amount',
        'is_paid',
        'is_acknowledge'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($invoice) {
            $invoice->invoice_id = self::generateUniqueInvoiceId();
        });
    }

    public static function generateUniqueInvoiceId()
    {
        do {
            $randomNumber = rand(100, 999);
            $randomLetters = strtoupper(Str::random(3));
            $randomSuffix = rand(10, 99);
            $invoiceId = 'INV-' . $randomNumber . $randomLetters . $randomSuffix;
        } while (self::where('invoice_id', $invoiceId)->exists());

        return $invoiceId;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function prescriptions()
    {
        return $this->belongsToMany(Prescription::class, 'invoice_prescriptions');
    }
}
