<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Inventory;
use App\Http\Requests\InventoryRequest;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class InventoriesController extends Controller
{
    public function import(Request $request)
    {
        try {
            // ✅ 1️⃣ Validate file input
            $validator = Validator::make($request->all(), [
                'file' => 'required|mimes:csv,txt,xlsx|max:102400', // Allow up to 100MB CSV file
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => 'error', 'message' => 'Invalid CSV file.', 'errors' => $validator->errors()], 400);
            }

            $file = $request->file('file');
            $filePath = $file->getRealPath();

            // if (!$this->validateCsvHeaders($filePath)) {
            //     return response()->json(['status' => 'error', 'message' => 'Invalid CSV headers.'], 400);
            // }

            // ✅ 2️⃣ Read CSV file
            $handle = fopen($filePath, 'r');
            fgetcsv($handle); // Skip header row

            $chunkSize = 1000; // Process 1,000 rows at a time
            $totalRecords = 0;
            $skippedRecords = 0;

            while (!feof($handle)) {
                $chunk = [];
                $count = 0;

                // ✅ 3️⃣ Read CSV in chunks of 1,000
                while ($count < $chunkSize && ($row = fgetcsv($handle)) !== false) {
                    if (count($row) < 5 || empty(trim($row[0]))) {
                        $skippedRecords++; // Skip row if pip_code is empty
                        continue;
                    }

                    // ✅ 4️⃣ Clean & format data
                    // $pack_size = preg_replace('/\D/', '', $row[2]);
                    // $pack_size = is_numeric($pack_size) ? (int) $pack_size : 1;
                    $pack_size = $row[2] ?? '1';

                    $chunk[] = [
                        'pip_code' => trim($row[0]),
                        'name' => trim($row[1]), // Preserve exact name
                        'pack_size' => $pack_size,
                        'cost_price' => (float) str_replace(['£', ','], '', $row[4] / 2),
                        'sale_price' => (float) str_replace(['£', ','], '', $row[4]),
                    ];
                    $count++;
                }

                // ✅ 5️⃣ Insert or update records in database
                if (!empty($chunk)) {
                    $this->insertChunk($chunk);
                    $totalRecords += count($chunk);
                }
            }

            fclose($handle);

            return response()->json([
                'status' => 'success',
                'message' => "Imported $totalRecords records successfully!",
                'skipped' => $skippedRecords,
            ], 200);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'Import failed.', 'error' => $e->getMessage()], 500);
        }
    }


    private function validateCsvHeaders($filePath)
    {
        $handle = fopen($filePath, 'r');
        $header = fgetcsv($handle);
        fclose($handle);

        // ✅ Expected CSV headers
        $expectedHeaders = ['Pip Code', 'Product Description', 'Product Item Size', 'Cost Price', 'Price'];
        $normalizedExpectedHeaders = array_map('strtolower', array_map('trim', $expectedHeaders));
        $normalizedCsvHeaders = array_map('strtolower', array_map('trim', $header));

        return $normalizedCsvHeaders === $normalizedExpectedHeaders;
    }

    private function insertChunk($data)
    {
        try {
            DB::beginTransaction();

            // ✅ Use `upsert()` to update existing records instead of inserting duplicates
            Inventory::upsert(
                $data,
                ['pip_code'], // Unique identifier for checking existing records
                ['name', 'pack_size', 'cost_price', 'sale_price'] // Columns to update if pip_code exists
            );

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error("CSV Import Failed: " . $e->getMessage());
        }
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        // $inventories = Inventory::orderBy('created_at', 'DESC')->paginate(50);
        $search = $request->input('search', '');
        $description = $request->input('description', '');
        $query = Inventory::query();
        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('pip_code', 'like', '%' . $search . '%');
            });
        }

        if ($description) {
            $query->where('description', 'like', '%' . $description . '%');
        }
        $inventories = $query->orderBy('created_at', 'DESC')->paginate(10);
        return view('inventories.index', compact('inventories', 'search'));
    }

    public function searchMedicines(Request $request)
    {
        $query = $request->get('q');
        $excludeIds = $request->get('exclude_ids', []);

        $medicines = Inventory::where(function ($q) use ($query) {
            $q->where('name', 'like', '%' . $query . '%')
                ->orWhere('pip_code', 'like', '%' . $query . '%');
        });

        // Exclude medicines that are already selected
        if (!empty($excludeIds)) {
            $medicines->whereNotIn('id', $excludeIds);
        }

        return $medicines->limit(10)->get();
    }

    public function search(Request $request)
    {
        $search = $request->input('search', '');
        $page = $request->input('page', 1);

        $query = Inventory::query();

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('pip_code', 'like', '%' . $search . '%');
            });
        }

        $inventories = $query->orderBy('created_at', 'DESC')->paginate(10, ['*'], 'page', $page);

        $html = '';
        foreach ($inventories as $item) {
            $html .= '<tr>';
            $html .= '<td>';
            $html .= '<div class="d-flex gap-2 align-items-center">';
            $html .= '<img src="' . asset('website/assets/media/images/table-featured-icon.svg') . '" alt="" height="40px" width="40px">';
            $html .= '<div class="d-flex flex-column">';
            $html .= '<h5 class="fw-500 number-gray-black">' . ($item->name ?? '') . '</h5>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</td>';
            $html .= '<td>' . ($item->pip_code ?? '') . '</td>';
            $html .= '<td>' . ($item->cost_price ?? '') . '</td>';
            $html .= '<td>' . ($item->sale_price ?? '') . '</td>';
            $html .= '<td>' . ($item->pack_size ?? '') . '</td>';

            if (auth()->user()->hasRole('admin')) {
                $html .= '<td class="action_btn text-end">';
                $html .= '<div class="dropdown">';
                $html .= '<a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">';
                $html .= '<i class="fa-solid fa-ellipsis-vertical"></i>';
                $html .= '</a>';
                $html .= '<ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">';
                $html .= '<li><a class="dropdown-item Satoshi" onclick="openModal(' . json_encode($item) . ')">View</a></li>';
                $html .= '</ul>';
                $html .= '</div>';
                $html .= '</td>';
            }

            $html .= '</tr>';
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => (string) $inventories->links(),
            'pagination_info' => "Showing {$inventories->firstItem()} to {$inventories->lastItem()} of {$inventories->total()} entries",
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('inventories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  InventoryRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'pip_code' => 'required|unique:inventories,pip_code,' . ($request->id ?? 'NULL'),
                'name' => ['required', 'string', 'max:20', 'regex:/^[a-zA-Z\s]+$/'],
                'pack_size' => ['required', 'integer', 'between:1,10000000'],
                'cost_price' => 'required|numeric|min:0|max:10000000',
            ], [
                'name.regex' => "Please enter a valid name (letters and spaces only)",
                'name.max' => "The name may not be greater than 20 characters",
                'pack_size.between' => "The pack size must be valid size",
            ]);

            $cost_price = round($request->cost_price, 2);
            $sale_price = round($cost_price * 2, 2); // Sale Price = 2x Cost Price

            Inventory::updateOrCreate(
                ['id' => $request->id],  // Identify by ID, not pip_code
                [
                    'pip_code' => $request->pip_code,
                    'name' => $request->name,
                    'pack_size' => $request->pack_size,
                    'cost_price' => $cost_price,
                    'sale_price' => $sale_price,
                ]
            );

            return response()->json(['message' => 'Inventory saved successfully']);
        } catch (ValidationException $e) {
            return response()->json(['errors' => $e->validator->errors()], 422);
        }
    }

    public function importOLD(Request $request)
    {
        // Validate file input
        $validator = Validator::make($request->all(), [
            'file' => 'required|mimes:csv,txt,xlsx|max:20480', // 20MB limit
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()->first()], 400);
        }

        $file = $request->file('file');
        $data = [];

        // Open CSV File and Read Data
        if (($handle = fopen($file->getPathname(), 'r')) !== false) {
            $header = fgetcsv($handle, 1000, ","); // Get the CSV headers
            if ($header) {
                while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                    if (count($row) < 5) continue; // Skip invalid rows

                    // $data[] = [
                    //     'pip_code' => $row[0],
                    //     'name' => $row[1],
                    //     'pack_size' => $row[2],
                    //     'cost_price' => (float) str_replace('£', '', $row[3]),
                    //     'sale_price' => (float) str_replace('£', '', $row[4]),
                    // ];
                    $data[] = [
                        'pip_code' => $row[0],
                        'name' => mb_convert_encoding($row[1], 'UTF-8', 'auto'), // Ensure UTF-8 encoding
                        'pack_size' => (int) preg_replace('/\D/', '', $row[2]), // Extract only numbers
                        'cost_price' => (float) str_replace(['£', ','], '', $row[3]), // Remove currency symbols & commas
                        'sale_price' => (float) str_replace(['£', ','], '', $row[4]),
                    ];
                }
            }
            fclose($handle);
        }

        if (empty($data)) {
            return response()->json(['error' => 'No valid data found in the file.'], 400);
        }

        try {
            DB::beginTransaction(); // Start transaction

            // Insert or Update in Chunks for Performance
            $chunks = array_chunk($data, 1000); // Process in batches of 1000
            foreach ($chunks as $chunk) {
                Inventory::upsert($chunk, ['pip_code'], ['name', 'pack_size', 'cost_price', 'sale_price']);
            }

            DB::commit(); // Commit transaction

            return response()->json(['success' => 'Inventory imported successfully!']);
        } catch (\Exception $e) {
            DB::rollBack(); // Rollback transaction if error occurs
            return response()->json(['error' => 'Import failed: ' . $e->getMessage()], 500);
        }
    }



    // public function store(InventoryRequest $request)
    // {
    //     $inventory = new Inventory;
    //     $inventory->pip_code = $request->input('pip_code');
    //     $inventory->name = $request->input('name');
    //     $inventory->name = $request->input('pack_size');
    //     $inventory->cost_price = $request->input('cost_price');
    //     $inventory->sale_price = $request->input('sale_price');
    //     $inventory->save();
    //     return to_route('inventories.index');
    // }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $inventory = Inventory::findOrFail($id);
        return view('inventories.show', ['inventory' => $inventory]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $inventory = Inventory::findOrFail($id);
        return view('inventories.edit', ['inventory' => $inventory]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  InventoryRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(InventoryRequest $request, $id)
    {
        $inventory = Inventory::findOrFail($id);
        $inventory->name = $request->input('name');
        $inventory->description = $request->input('description');
        $inventory->type = $request->input('type');
        $inventory->cost_price = $request->input('cost_price');
        $inventory->sale_price = $request->input('sale_price');
        $inventory->stock_quantity = $request->input('stock_quantity');
        $inventory->save();

        return to_route('inventories.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $inventory = Inventory::findOrFail($id);
        $inventory->delete();

        return to_route('inventories.index');
    }

    public function getMedicineDetails($id)
    {
        $medicine = Inventory::find($id);

        if ($medicine) {
            return response()->json([
                'success' => true,
                'data' => [
                    'pack_size' => $medicine->pack_size,
                    'cost_price' => $medicine->cost_price
                ]
            ]);
        }

        return response()->json(['success' => false, 'message' => 'Medicine not found']);
    }
}
