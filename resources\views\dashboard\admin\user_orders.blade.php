@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <h4 class="roboto heading-gary mb-5 ps-4"> Order Requests</h4>
                    <div
                        class="custom-dropdown roboto d-flex justify-content-sm-between justify-content-start align-items-center py-5 flex-wrap">
                        <div>
                            <h3>All Orders
                                <!-- <span class="input-text-gray">({{ $prescriptions->total() }})</span> -->
                            </h3>
                        </div>
                        <div
                            class="custom-dropdown roboto d-flex justify-content-sm-end justify-content-start align-items-center pb-5 gap-2 flex-wrap">
                            {{-- <label for="patient_name">Patient Name</label> --}}

                            <div class="search_box position-relative pre-search">
                                <input type="text" id="patient_name" value="{{ request('patient_name') }}"
                                    name="patient_name" class=" form-control searchMedicines "
                                    placeholder="Search by patient name">
                                <a class="reset-btn position-absolute clearSearchBtn" id="clearSearchBtn"
                                    href="{{ route('orders')}}">
                                    <i class="fa-solid fa-xmark"></i>
                                </a>
                            </div>

                            <button id="select_all" class="button-gradient white-color roboto fs-14 fw-400"
                                type="button">Select</button>
                            <button class="collect_data button-gradient white-color roboto fs-14 fw-400 " data-action="1"
                                type="button">Approve
                                all</button>
                            <button class="collect_data button-gradient white-color roboto fs-14 fw-400 " data-action="2"
                                type="button">Reject
                                all</button>

                        </div>
                    </div>
                    <form method="GET" action="{{ route('orders') }}">
                        <div
                            class="custom-dropdown roboto d-flex align-item-center justify-content-end gap-2 rounded-0 flex-wrap">
                            <!-- Patient Name Filter -->
                            <!-- Status Filter using custom select -->

                            <div class="custom-select">
                                <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                    name="status" data-dropdown-css-class="w-200px">
                                    <option value="" selected>All Payments</option>
                                    <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Paid</option>
                                    <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Pending</option>
                                </select>
                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                            </div>

                            <div class="custom-select">
                                <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                    name="prescription_type" data-dropdown-css-class="w-200px">
                                    <option value="" selected>All Types</option>
                                    <option value="one_time" {{ request('prescription_type') == 'one_time' ? 'selected' : '' }}>One Time</option>
                                    <option value="repeat" {{ request('prescription_type') == 'repeat' ? 'selected' : '' }}>
                                        Repeat</option>

                                </select>
                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                            </div>


                            <div class="custom-select">
                                <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                    name="approval_status" data-dropdown-css-class="w-200px">
                                
                                    <option value="all" {{ request('approval_status') == 'all' ? 'selected' : '' }}>All Status</option>
                                    <option value="admin_approval__0" {{ request('approval_status') == 'admin_approval__0' ? 'selected' : '' }}>Pending
                                    </option>
                                    <option value="admin_approval__1" {{ request('approval_status') == 'admin_approval__1' ? 'selected' : '' }}>Approved
                                        by Pharmacy</option>
                                    <option value="is_dispensed__1" {{ request('approval_status') == 'is_dispensed__1' ? 'selected' : '' }}>Dispensed
                                    </option>
                                    <option value="is_dispensed__2" {{ request('approval_status') == 'is_dispensed__2' ? 'selected' : '' }}>Delivered
                                    </option>
                                    <option value="admin_approval__2" {{ request('approval_status') == 'admin_approval__2' ? 'selected' : '' }}>Rejected
                                    </option>

                                </select>
                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                            </div>

                            <!-- Submit Button -->
                            <div>
                                <button class="button-gradient white-color roboto fs-14 fw-400"
                                    type="submit">Search</button>
                            </div>
                        </div>
                    </form>


                </div>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active custom_tabs rounded-0" id="kt_tab_pane_1" role="tabpanel">
                        <div class="table-responsive">
                            <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                <thead>
                                    <tr>
                                        <th class="checkbox_data first-and-last" style="display: none;"><input
                                                type="checkbox" id="select-all" class="select-all"></th>
                                        <th class="min-w-300px ">Patient Name</th>
                                        <th class="min-w-200px ">Created By</th>
                                        <th class="min-w-200px ">Date Requested</th>
                                        <th class="min-w-200px ">Prescription Type</th>
                                        <th class="min-w-200px ">Billed Amount</th>
                                        <th class="min-w-200px ">Billing Type</th>
                                        <th class="min-w-200px ">Payment Status</th>
                                        <th class="min-w-200px ">Approval Status</th>
                                        <th class="min-w-50px first-and-last"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($prescriptions as $item)
                                        <tr>
                                            <td class="checkbox_data" style="display: none;">
                                                <input {{ $item->is_dispensed == 1 || $item->status == 1 ? 'disabled' : '' }}
                                                    class="checkbox_data" type="checkbox" class="row-select"
                                                    value="{{ $item->id ?? '' }}">
                                            </td>
                                            <td>
                                                <a href="{{ route('prescription-details', $item->order_id) }}">
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <img src="{{ asset('website') }}/assets/media/images/orders-featured-icon.svg"
                                                            alt="" height="40px" width="40px">
                                                        <div class="d-flex flex-column">
                                                            <h5 class="fw-500 number-gray-black">
                                                                {{ $item->patient->name ?? '' }}
                                                            </h5>
                                                            <span
                                                                class="input-text-gray fs-14 fw-400">{{ $item->patient->email ?? '' }}</span>
                                                        </div>
                                                    </div>
                                                </a>
                                            </td>
                                            <td>{{ $item->prescribedBy->name ?? '' }}</td>
                                            <td>{{ $item->created_at->format('Y, M-d') ?? '' }}</td>
                                            <td>{{ ucwords(str_replace('_', ' ', $item->prescription_type ?? '')) }}
                                            </td>
                                            <td>£{{ $item->total ?? '' }}</td>
                                            <td>{{ $item->type ?? '' }}</td>
                                            <td>
                                                <span
                                                    class="badge {{ $item->payment_status['badge_class'] }}-badge badge-{{ $item->payment_status['badge_class'] }} roboto fs-14 fw-400">
                                                    {{ $item->payment_status['status'] }}
                                                </span>
                                            </td>
                                            <td class="status">
                                                <span
                                                    class="badge {{ $item->status_text['badge_class'] ?? '' }}-badge badge-{{ $item->status_text['badge_class'] ?? '' }} roboto fs-14 fw-400">
                                                    {{ $item->status_text['label'] ?? '' }}
                                                </span>
                                            </td>

                                            <td class="action_btn">
                                                <div class="dropdown">
                                                    <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                        data-bs-toggle="dropdown" aria-expanded="true">
                                                        <i class="fa-solid fa-ellipsis-vertical"></i>
                                                    </a>
                                                    <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                        <li>
                                                            <a class="dropdown-item Satoshi  px-3"
                                                                href="{{ route('prescription-details', $item->order_id) }}">View</a>
                                                        </li>
                                                        <li> <a class=" dropdown-item Satoshi  px-3"
                                                                href="{{ route('generate-prescription-pdf', $item->order_id) }}"
                                                                target="_blank">
                                                                Download PDF
                                                            </a>
                                                         </li>
                                                        @if ($item->is_dispensed == 0 && $item->admin_approval == 1 && $item->prescription_type == 'one_time')
                                                            <li>
                                                                 <button class="btn btn-primary btn-sm create-shipment-btn"
                                                                    data-order-id="{{ $item->order_id }}">
                                                                    Print
                                                                </button>
                                                            </li>
                                                        @endif
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-between align-items-center mt-5">
                                <p>
                                    Showing {{ $prescriptions->firstItem() }} to {{ $prescriptions->lastItem() }} of
                                    {{ $prescriptions->total() }}
                                    entries
                                </p>
                                <div class="pagination">
                                    {{ $prescriptions->links() }}
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    @include('dashboard.templates.modals.admin-modals.prescription_edit_modal')

    </section>
@endsection
@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->
    <script>
        $(document).ready(function () {
            // Initially hide the approve/reject buttons
            $('.collect_data').hide();

            $('#select_all').on('click', function () {
                $('.checkbox_data').show();
            });

            // Handle select all checkbox
            $('.select-all').on('click', function () {
                const isChecked = $(this).prop('checked');
                $('.checkbox_data:not(:disabled)').prop('checked', isChecked);
                if (isChecked) {
                    $('.collect_data').show();
                } else {
                    $('.collect_data').hide();
                }
            });

            // Handle individual checkboxes
            $(document).on('change', '.checkbox_data', function () {
                const anyChecked = $('.checkbox_data:checked').length > 0;
                if (anyChecked) {
                    $('.collect_data').show();
                } else {
                    $('.collect_data').hide();
                }

                // Update select all checkbox state
                const allChecked = $('.checkbox_data:checked').length === $('.checkbox_data').length;
                $('.select-all').prop('checked', allChecked);
            });

            const table = $('.data_table').DataTable({
                paging: false,
                pageLength: 5,
                lengthChange: false,
                searching: false,
                info: false,
                ordering: false,
                columnDefs: [{
                    orderable: false,
                    targets: 0
                }],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>'
                    }
                }
            });

            $('.collect_data').on('click', function () {
                var values = [];
                var action = $(this).data('action');
                $('.checkbox_data:checked').each(function () {
                    values.push($(this).val());
                });

                if (values.length === 0) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'No items selected',
                        text: 'Please select at least one checkbox before proceeding.'
                    });
                } else {
                    $.ajax({
                        method: 'POST',
                        url: "{{ route('approve_orders') }}",
                        data: {
                            values: values,
                            action: action,
                            _token: "{{ csrf_token() }}"
                        },
                        success: function (response) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Approved',
                                text: 'All orders marked as approved!'
                            }).then(() => {
                                location.reload();
                            });
                        },
                        error: function (xhr) {
                            console.error(xhr.responseText);
                        }
                    });
                }
            });

            $(document).on('click', '.create-shipment-btn', function () {
                var button = $(this);
                var orderId = button.data('order-id');

                Swal.fire({
                    title: 'Confirm Action',
                    text: "Are you sure you want to create shipment and print the label for this prescription?",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, proceed!',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (!result.isConfirmed) {
                        return;
                    }

                    $.ajax({
                        url: '{{ route('shipment.create', ':orderId') }}'.replace(':orderId', orderId),
                        type: 'POST',
                        data: {
                            orderId: orderId,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function (response) {
                            if (response.message) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: response.message
                                });
                            } else {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: 'Shipment created and label printed successfully.'
                                });
                            }

                            var row = button.closest('tr');
                            row.find('.create-shipment-btn').remove();

                            var checkbox = row.find('.checkbox_data input');
                            checkbox.prop('checked', false).prop('disabled', true);

                            var statusColumn = row.find('.status');
                            statusColumn.empty();
                            statusColumn.append(`
                                                                            <span class="badge deliverd-badge badge-Despensed roboto fs-14 fw-400">
                                                                                Dispensed
                                                                            </span>
                                                                        `);
                        },
                        error: function (xhr, status, error) {
                            let errorMessage = 'Failed to create shipment. Please try again.';

                            if (xhr.responseJSON) {
                                if (xhr.responseJSON.error) {
                                    errorMessage = xhr.responseJSON.error;
                                }

                                if (xhr.responseJSON.details && xhr.responseJSON.details.length > 0) {
                                    errorMessage += '\n\nDetails:\n' + xhr.responseJSON.details.join('\n');
                                }
                            }

                            Swal.fire({
                                icon: 'error',
                                title: 'Shipment Creation Failed',
                                text: errorMessage,
                                width: '600px'
                            });
                        }
                    });
                });
            });

            // Add auto-submit for status filter
            $('.status_filter').on('change', function() {
                const form = $(this).closest('form');
                if ($(this).val() === 'all') {
                    // Remove the approval_status parameter if "All Status" is selected
                    const url = new URL(form.attr('action'), window.location.origin);
                    url.searchParams.delete('approval_status');
                    window.location.href = url.toString();
                } else {
                    form.submit();
                }
            });
        });
    </script>
@endpush