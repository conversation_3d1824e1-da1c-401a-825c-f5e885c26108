<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class Profile extends Model
{
    protected $guarded = [];

    protected $fillable = [
        'user_id',
        'professional_type',
        'reg_no',
        'mobile_number',
        'clinic_address',
        'id_document',
        'idemnity_certificate',
        'bio',
        'gender',
        'dob',
        'age',
        'pic',
        'country',
        'state',
        'city',
        'address',
        'postal',
        'title',
        'role',
        'delivery_address',
        'delivery_postal',
        'delivery_city',
        'delivery_country'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function user_roles()
    {
        return $this->belongsTo(Role::class);
    }
}
