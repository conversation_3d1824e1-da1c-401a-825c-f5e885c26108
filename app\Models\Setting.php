<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;
    use SoftDeletes;
    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'settings';

    /**
     * The database primary key value.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Attributes that should be mass-assignable.
     *
     * @var array
     */
    protected $fillable = ['title', 'description', 'favicon', 'logo', 'facebook', 'twitter', 'youtube', 'instagram', 'whatsapp', 'stripe_publishable', 'stripe_secret', 'footer_text', 'timezone', 'linkdin', 'github', 'tel_no', 'minimum_charge', 'email', 'address', 'website_logo', 'footer_logo'];
}
