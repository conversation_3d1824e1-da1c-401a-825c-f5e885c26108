<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Credit Note</title>
  <style>
    * {
      box-sizing: border-box;
    }

    html, body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      font-family: Arial, sans-serif;
      color: #000;
    }

    .fs-20 { font-size: 20px; }
    .fs-15 { font-size: 15px; }
    .fs-45 { font-size: 30px; }

    .wrapper {
    padding: 40px;
    padding-top: 3rem;}

    .header {
      margin-bottom: 40px;
    }

    .bill-to-month {
      width: 100%;
      margin-bottom: 20px;
    }

    .bill-to-month td {
      vertical-align: top;
      padding-bottom: 20px;
    }
   .bill-to-month td, .bill-to-month th{
        border: none !important;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 14px;
    }

    th, td {
      border: 1px solid #000;
      padding: 8px;
      text-align: left;
      font-size: 14px;
    }

    th {
      background-color: #f0f0f0;
    }
    .table-section th{border: 1px solid black; background-color: #24A562 !important ;color: white;}

    .totals {
    margin-top: 20px;
    width: 350px;
    margin-left: auto;
    }

     .totals.cashback{width: 300px}
    .totals td{color: white}

    .highlight {
      background-color:  #24A562 ;;
      color: #fff;
    }

    .signature-wrapper {
      margin-top: 30px;
      text-align: right;
    }
    .footer{
        text-align: right;
        margin-right: 40px;
    }


    .signature {
    font-size: 14px;
    margin-bottom: 5px;
    margin-right: 13rem;}

    .signature-bottom {
      width: 200px;
      border-bottom: 2px solid black;
      margin-left: auto;
    }
    img{height: 70px;}
     .footer-text{text-align: center; padding-top: 20px; margin-inline: 20px; line-height: 1.5;    font-size: 14px;}
  </style>
</head>
<body>
  <div class="wrapper">
    <div class="header">
      <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('website/assets/media/images/rx logo.png'))) }}" alt="img">
    </div>

   <table class="bill-to-month">
  <tr>
    <td>
      <div class="fs-20" style="margin-bottom: 10px;"><strong>BILL TO</strong></div>
      <div class="fs-15" style="margin-bottom: 6px;"><strong>Name:</strong> {{ $user->name ?? '' }}</div>
      <div class="fs-15"><strong>Email address:</strong> {{ $user->email ?? '' }}</div>
    </td>
    <td style="text-align: right;">
      <strong class="fs-15">Month:</strong> {{ $month }}
    </td>
  </tr>
</table>


    <div class="table-section">
      <table>
        <thead>
          <tr class="head">
            <th>Medicines</th>
            <th>Used</th>
            <th>Price</th>
          </tr>
        </thead>
        <tbody>
          @forelse ($medicines as $medicine)
          <tr>
            <td>{{ $medicine->name }}</td>
            <td>{{ $medicine->prescribed_units }}</td>
            <td>£{{ number_format($medicine->total_price, 2) }}</td>
          </tr>
          @empty
          <tr>
            <td colspan="3" class="text-center">No medicines found</td>
          </tr>
          @endforelse
        </tbody>
      </table>
    </div>

    <div class="totals">
      <table>
        <tr class="highlight">
          <td><strong>TOTAL DUE (GBP)</strong></td>
          <td><strong>£{{ number_format($totalAmount, 2) }}</strong></td>
        </tr>
      </table>
    </div>

     <div class="totals cashback">
      <table>
        <tr class="highlight">
          <td><strong>Cashback Amount</strong></td>
          <td><strong>£{{ number_format($totalAmount, 2) }}</strong></td>
        </tr>
      </table>
    </div>

    <div class="signature-wrapper">
      <div class="signature">Signature:</div>
      <div class="signature-bottom"></div>
    </div>
  </div>
   {{-- <div class="footer">
        <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('website/assets/media/images/rx logo.png'))) }}" alt="img">
    </div> --}}
    <div class="footer-text">
        <p>For e-signature verification or queries Contact: <EMAIL> or +44 (0) 330 113 7894</p>
    </div>
</body>
</html>
