@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <button id="backButton" class="button-gradient white-color roboto fs-14 fw-400 my-5 ps-5">Back</button>
            <div class="row my-5">
                <div class="col-lg-9">
                    <div class="d-flex justify-content-between">
                        <h4 class="roboto heading-gary fs-20 fw-500">{{ $user->name ?? '' }}</h4>
                    </div>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#patients">Patients</a>
                        </li>
                        <li class="nav-item roboto fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#prescriptions">Prescriptions</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="patients" role="tabpanel">
                            <div class="custom-dropdown">
                                <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                    <div>
                                        <h3>Patients
                                            <!-- <span class="input-text-gray"> ({{ $patients->total() ?? '' }})</span> -->
                                        </h3>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                        <div class="custom-select search-bar">
                                            <input type="search" class="search form-control" value="" name="search"
                                                placeholder="Search by name...">
                                        </div>



                                        <div class="custom-select">
                                            <select data-control="select2" class="statusFilter" data-hide-search="true"
                                                data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Patients</option>
                                                <option value="1">Active</option>
                                                <option value="0">Inactive</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>


                                        <!-- <div class="custom-select">
                                        <select id="statusFilter" class="search_filter">
                                            <option value="">All Patients</option>
                                            <option value="1">Active</option>
                                            <option value="0">Inactive</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                    </div> -->
                                    </div>
                                </div>
                                <div class="table-responsive" id="dataTable">
                                    <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <!-- <th class="w-20px ps-5"><input type="checkbox" id="select-all"
                                                        class="select-all ">
                                                </th> -->
                                                <th class="w-600px">Patient Name</th>
                                                <th class="w-200px">Date Added</th>
                                                <th class="w-200px">Status</th>
                                                <th class="w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($patients as $patient)
                                                <tr>
                                                    <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                    <td>
                                                        <div class="d-flex gap-2 align-items-center">
                                                            <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                alt="" height="40px" width="40px">
                                                            <div class="d-flex flex-column">
                                                                <h5 class="fw-500 number-gray-black">
                                                                    {{ $patient->name ?? '' }}
                                                                </h5>
                                                                <span
                                                                    class="input-text-gray fs-14 fw-400">{{ $patient->email ?? '' }}</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $patient->created_at->format('m d Y') ?? '' }}</td>
                                                    <td><span
                                                            class="badge badge-{{ $patient->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $patient->status_text ?? '' }}</span>
                                                    </td>

                                                    <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                        href="{{ route('patients-view-details', $patient->id) }}">View</a>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>



                                                    <!-- <td class="action_btn">
                                                        <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                            data-kt-menu="true">
                                                            <div class="menu-item px-3">
                                                                <a class="menu-link px-3"
                                                                    href="{{ route('patients-view-details', $patient->id) }}">View</a>
                                                            </div>
                                                        </div>
                                                    </td> -->
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $patients->firstItem() }} to {{ $patients->lastItem() }} of
                                            {{ $patients->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $patients->links() }}
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="prescriptions" role="tabpanel">
                            <div class="custom-dropdown">
                                <div class=" roboto d-flex justify-content-between align-items-center py-5">

                                    <div>
                                        <h3> Prescriptions
                                            <!-- <span class="input-text-gray">({{ $prescriptions->total() ?? '' }})</span> -->
                                        </h3>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                        <div class="custom-select search-bar">
                                            <input type="search" class="search form-control" value="" name="search"
                                                placeholder="Search by name or email...">
                                        </div>


                                        <div class="custom-select">
                                            <select data-control="select2" class="statusFilter" data-hide-search="true"
                                                data-dropdown-css-class="w-200px">
                                                <option value="" selected >All Types</option>
                                                <option value="one_time">Onetime</option>
                                                <option value="repeat">Recurring</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>



                                        <!-- <div class="custom-select">
                                        <select id="statusFilter" class="type_filter">
                                            <option value="">All Types</option>
                                            <option value="one_time">Onetime</option>
                                            <option value="repeat">Recurring</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                    </div> -->

                                        <div class="custom-select">
                                            <select data-control="select2" class="statusFilter" data-hide-search="true"
                                                data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Prescriptions</option>
                                                <option value="1">Paid</option>
                                                <option value="0">Pending</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>


                                        <!--
                                    <div class="custom-select">
                                        <select id="statusFilter" class="search_filter">
                                            <option value="">All Prescriptions</option>
                                            <option value="1">Paid</option>
                                            <option value="0">Pending</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                    </div> -->
                                    </div>
                                </div>
                                <div class="table-responsive" id="dataTable">
                                    <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <!-- <th class="min-w-20px ps-5"><input type="checkbox" id="select-all"
                                                        class="select-all ">
                                                </th> -->
                                                <th class="min-w-300px">Patient Name</th>
                                                <th class="min-w-200px">Date Added</th>
                                                <th class="min-w-200px">Type</th>
                                                <th class="min-w-200px">Billed Amount</th>
                                                <th class="min-w-200px">Payment Status</th>
                                                <th class="min-w-200px">Approval Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($prescriptions as $perscription)
                                                <tr>
                                                    <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                    <td>
                                                        <div class="d-flex gap-2 align-items-center">
                                                            <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                alt="" height="40px" width="40px">
                                                            <div class="d-flex flex-column">
                                                                <h5 class="fw-500 number-gray-black">
                                                                    {{ $perscription->patient->name ?? '' }}
                                                                </h5>
                                                                <span
                                                                    class="input-text-gray fs-14 fw-400">{{ $perscription->patient->email ?? '' }}</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $perscription->created_at->format('m d Y') }}</td>
                                                    <td>{{ $perscription->type ?? 'Type 1' }}</td>
                                                    <td>£{{ $perscription->total ?? '' }}</td>
                                                    <td><span
                                                            class="badge badge-{{ $perscription->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $perscription->status == 1 ? 'Paid' : 'Pending' }}</span>
                                                    </td>
                                                    <td><span
                                                            class="badge {{ $perscription->admin_approval == 1 ? 'deliverd' : 'pending' }}-badge badge-{{ $perscription->admin_approval == 1 ? 'Paid' : 'Pending' }} roboto fs-14 fw-400">{{ $perscription->admin_approval == 1 ? 'Approved' : 'Pending' }}</span>
                                                    </td>


                                                    <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                        href="{{ route('prescription-details', $perscription->order_id) }}">View</a>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>



                                                    <!-- <td class="action_btn">
                                                        <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                            data-kt-menu="true">
                                                            <div class="menu-item px-3">
                                                                <a href="{{ route('prescription-details', $perscription->order_id) }}"
                                                                    class="menu-link px-3 view-prescription">View</a>
                                                            </div>
                                                        </div>
                                                    </td> -->
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $prescriptions->firstItem() }} to {{ $prescriptions->lastItem() }} of
                                            {{ $prescriptions->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $prescriptions->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 pt-12 mt-10">
                    <div class="purple-card">
                        <div class="d-flex justify-content-between">
                            <div class="d-flex">
                                <div>
                                    <img src="{{ asset('website') }}/assets/media/images/reinger-icon.svg"
                                        alt="reinger-icon">
                                </div>
                                <div class="ms-2">
                                    <p class="fs-14 fw-500 mb-0"> {{ $user->name ?? '' }} </p>
                                    <h6 class="heading-gary mb-0"> {{ $user->email ?? '' }} </h6>
                                </div>
                            </div>
                            <div>
                                <p class="{{ $user->status == 1 ? 'light-green' : 'light-gray' }}-badge user-status"> {{ $user->status == 1 ? 'Active' : 'Inactive' }}
                                </p>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between pb-3">
                            <h6 class="mb-0">
                                <span><img src="{{ asset('website') }}/assets/media/images/user_icon.svg"
                                        alt="user_icon" /></span>
                                Patients
                            </h6>
                            <h6> {{ $patientsCount ?? 0 }} </h6>
                        </div>

                        <div class="d-flex justify-content-between">
                            <h6 class="mb-0">
                                <span><img src="{{ asset('website') }}/assets/media/images/prescriptions.svg"
                                        alt="prescriptions" /></span>
                                Prescriptions
                            </h6>
                            <h6> {{ $prescriptionsCount ?? 0 }} </h6>
                        </div>


                        <div class="d-flex flex-end pt-5">
                            <a type="button" class="gradient_modal_approve white-color roboto fs-14 fw-400 mt-5 modal-save"
                                id="toggle-status"
                                data-slug="{{ $user->id ?? '' }}">{{ $user->status == 1 ? 'Deactivate' : 'Activate' }}</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <script>
        $(document).ready(function () {
            let user_id = "{{ $user->id ?? '' }}";
            let Timer;

            // Search input handler
            $(document).on('keyup', '.search', function () {
                clearTimeout(Timer);
                Timer = setTimeout(function () {
                    fetchData();
                }, 300);
            });

            // Status filter handler
            $(document).on('change', '.search_filter', function () {
                fetchData();
            });
            $(document).on('change', '.type_filter', function () {
                fetchData();
            });

            // Pagination handler
            $(document).on('click', '.pagination a', function (e) {
                e.preventDefault();
                let page = $(this).attr('href').split('page=')[1];
                fetchData(page);
            });

            function fetchData(page = 1) {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                // Get search value from the active tab's search input
                var searchValue = $(activeTab + ' .search').val();
                // Get status filter from the active tab's status filter
                var statusValue = $(activeTab + ' #statusFilter').val();
                // Get type filter from the active tab's type filter
                var typeValue = $(activeTab + ' .type_filter').val();

                $.ajax({
                    url: "{{ route('search.clinic.staff') }}",
                    type: 'GET',
                    data: {
                        search: searchValue,
                        status: statusValue,
                        type: typeValue,
                        tab: activeTab,
                        page: page,
                        user_id: user_id
                    },
                    success: function (data) {
                        // Update the dataTable in the active tab
                        $(activeTab + ' #dataTable').html(data);

                        // Update the count in the active tab's header
                        if (activeTab === '#patients') {
                            $(activeTab + ' .input-text-gray').text('(' + $(data).find('.pagination-container').data('total') + ')');
                        } else if (activeTab === '#prescriptions') {
                            $(activeTab + ' .input-text-gray').text('(' + $(data).find('.pagination-container').data('total') + ')');
                        }
                    },
                    error: function (xhr) {
                        console.error('Search failed:', xhr);
                    }
                });
            }
            const table = $('.data_table').DataTable({
                paging: false,
                pageLength: 5,
                lengthChange: false,
                searching: false,
                // ordering: true,
                info: false,
                ordering: false,
                columnDefs: [{
                    orderable: false,
                    targets: 0
                }],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>'
                    }
                }
            });

            $('#statusFilter').on('change', function () {
                const status = $(this).val();
                table.column(7).search(status, true, false).draw();
            });

            $('.select-all').on('click', function () {
                const isChecked = $(this).prop('checked');
                $('.row-select').prop('checked', isChecked);
            });

            $('.row-select').on('click', function () {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });
            $('#toggle-status').on('click', function () {
                let button = $(this);
                let userId = button.data('slug');
                performStatusChange(userId);
            });

            function performStatusChange(userId) {
                let row = $(`#prescription-${userId}`);

                $.ajax({
                    url: "{{ url('clinic-staff') }}" + `/${userId}/toggle-status`,
                    type: 'GET',
                    data: {
                        _token: '{{ csrf_token() }}',
                    },
                    success: function (response) {
                        if (response.status === 'success') {
                            let newStatus = response.new_status;
                            let statusLabel = $('.user-status');

                            statusLabel
                                .text(newStatus === 1 ? 'Active' : 'Inactive')
                                .removeClass('')
                                .addClass(newStatus === 1 ? '' : '');
                            $('#toggle-status').text(newStatus === 1 ? 'Deactivate' : 'Activate');

                            Swal.fire({
                                title: 'Success!',
                                text: `User ${newStatus === 1 ? 'Activated' : 'Deactivated'} successfully!`,
                                icon: 'success',
                                confirmButtonText: 'OK',
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update status. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                            });
                        }
                    },
                    error: function () {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    }
                });
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            $('#backButton').on('click', function () {
                window.history.back();
            });
        });
    </script>
@endpush