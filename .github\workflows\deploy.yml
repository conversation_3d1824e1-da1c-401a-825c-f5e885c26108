name: <PERSON><PERSON> CI/CD

on:
  push:
    branches:
      - main  # Adjust if your default branch is different

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Code
      uses: actions/checkout@v3

    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.5.3
      with:
        ssh-private-key: ${{ secrets.CPANEL_PRIVATE_KEY }}

    - name: Set Permissions
      run: |
        ssh -o StrictHostKeyChecking=no democustom@${{ secrets.DEMOCUST_IP }} << 'EOF'
        EOF

    - name: Sync Project to Server
      run: |
        rsync -avz \
        --exclude '.git/' \
        --exclude '.gitignore' \
        --exclude '.env' \
        --exclude 'storage/' \
        --exclude 'bootstrap/cache/' \
        -e "ssh -o StrictHostKeyChecking=no" \
        ./ democustom@${{ secrets.DEMOCUST_IP }}:/home2/democustom/public_html/shoaib/rx-direct
        