@extends('theme.layout.master')
@section('breadcrumb')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Orders and Terms</h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ url('home') }}" class="text-muted text-hover-primary">Cms</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">Orders and Terms</li>
                </ul>
            </div>
        </div>
    </div>
@endsection
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container-xxl">
            <div class="card">
                <div class="card-header border-0 pt-6">
                    <div class="card-title">
                        <div class="d-flex align-items-center position-relative my-1">
                        </div>
                    </div>
                </div>
                <div class="card-body pt-0">
                    <form method="POST" action="{{ route('complaints.store') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Title</label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" name="title" value="{{ old('title', $order_terms->title ?? '') }}">
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">Description</label>
                                    <textarea class="editor" name="description" id="">{{ old('description', $order_terms->description ?? '') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('plugins/components/toast-master/js/jquery.toast.js') }}"></script>
    <script src="{{ asset('plugins/components/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script src="{{ asset('website/assets/js/tinymce/tinymce.min.js') }}"></script>
    <script>
        function initializeTinyMCE() {
            $('textarea').each(function() {
                if (!$(this).hasClass('tinymce-editor')) {
                    $(this).addClass('tinymce-editor');
                    tinymce.init({
                        selector: `textarea`,
                        plugins: 'image link code lists',
                        toolbar: 'undo redo | bold italic underline | alignleft aligncenter alignright | bullist numlist | link image code',
                        images_file_types: 'jpg,svg,webp',
                        file_picker_types: 'file image media',
                        automatic_uploads: true,
                        menubar: true,
                        height: 550,
                        formats: {
                            bold: {
                                inline: 'b'
                            },
                            italic: {
                                inline: 'i'
                            },
                            underline: {
                                inline: 'u'
                            },
                            h1: {
                                block: 'h1'
                            },
                            h2: {
                                block: 'h2'
                            },
                            h3: {
                                block: 'h3'
                            },
                            p: {
                                block: 'p'
                            }
                        },
                    });
                }
            });
        }
        initializeTinyMCE();
    </script>
    <script>
        $(document).ready(function() {
            initializeTinyMCE();
            @if (\Session::has('message'))
                $.toast({
                    heading: 'Success!',
                    position: 'top-center',
                    text: '{{ session()->get('message') }}',
                    loaderBg: '#ff6849',
                    icon: 'success',
                    hideAfter: 3000,
                    stack: 6
                });
            @endif
        });
    </script>
@endpush
