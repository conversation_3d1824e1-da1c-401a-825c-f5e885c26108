@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush

@section('content')

    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <button id="backButton" class="button-gradient white-color roboto fs-14 fw-400 my-5">Back</button>
            <!-- {{--            <a href="{{ route('admin.users')}}" class="button-gradient white-color roboto fs-14 fw-400 my-5">Back</a> --}} -->
            <div class="row my-5">
                <div class="col-lg-9">
                    <div class="d-flex justify-content-between">
                        <h4 class="roboto heading-gary">{{ $user->name ?? '' }}</h4>
                    </div>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-6">
                        <li class="nav-item roboto fs-13 fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#patients">Patients</a>
                        </li>
                        <li class="nav-item roboto fs-13 fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#prescriptions">Prescriptions</a>
                        </li>
                        @if ($user->hasRole('clinic_admin'))
                            <li class="nav-item roboto fs-13 fw-600">
                                <a class="nav-link" data-bs-toggle="tab" href="#prescriber">Prescribers</a>
                            </li>
                            <li class="nav-item roboto fs-13 fw-600">
                                <a class="nav-link" data-bs-toggle="tab" href="#staff">Receptionist Staffs</a>
                            </li>
                        @endif
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="patients" role="tabpanel">
                            <div class="custom-dropdown">
                                <div>
                                    <div class=" roboto d-flex justify-content-between align-items-center py-5">
                                        <div>
                                            <h3>Patients
                                                <!-- <span class="input-text-gray">{{ $patients->total() ?? '' }}</span>   -->
                                            </h3>
                                        </div>
                                        <div class="d-flex justify-content-end gap-2">
                                            <div class="search_box position-relative">
                                                <input type="text" value="" name="search_staffs"
                                                    class="searchMedicines form-control search_filter"
                                                    placeholder="Search by name...">
                                                <a class="reset-btn position-absolute reset_button clearSearchBtn" href="#">
                                                    <i class="fa-solid fa-xmark"></i>
                                                </a>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                <!-- <a class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 reset_button">Reset</a> -->
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-end pb-5 gap-3 flex-wrap rounded-0">
                                        <div class="result-container1">
                                            <div class="range-options additional-options">
                                                <div
                                                    class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                    <div class="start-range date-field">
                                                        <label for="adminStartRange">Start Range:</label>
                                                        <span class="error-message text-danger"
                                                            style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                            select Start Range
                                                            first.</span>
                                                        <input type="date" class="start_range-input start-range-input"
                                                            name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                    </div>

                                                    <div class="end-range date-field">
                                                        <label for="adminEndRange">End Range:</label>
                                                        <input type="date" class="end-range-input end-range-input"
                                                            name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected disabled>All Patients</option>
                                                <option value="1">Active</option>
                                                <option value="0">Deactive</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive table_data">
                                    <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-300px">Name</th>
                                                <th class="min-w-200px">Date Added</th>
                                                <th class="min-w-200px">Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($patients as $patient)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex gap-2 align-items-center">
                                                            <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                alt="featured-icon" height="40px" width="40px">
                                                            <div class="d-flex flex-column">
                                                                <h5 class="fw-500 number-gray-black"> {{ $patient->name ?? '' }}
                                                                </h5>
                                                                <span
                                                                    class="input-text-gray fs-14 fw-400">{{ $patient->email ?? '' }}</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $patient->created_at->format('m d Y') ?? '' }}</td>
                                                    <td><span
                                                            class="badge badge-{{ $patient->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $patient->status_text ?? '' }}</span>
                                                    </td>
                                                    <td>
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                data-bs-toggle="dropdown" aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi"
                                                                        href="{{ route('patients-view-details', ['id' => $patient->id]) }}">View</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $patients->firstItem() }} to {{ $patients->lastItem() }} of
                                            {{ $patients->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $patients->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="prescriptions" role="tabpanel">
                            <div class="custom-dropdown">
                                <div>
                                    <div class=" roboto d-flex justify-content-between align-items-center py-5">
                                        <div>
                                            <h3> Prescriptions
                                                <!-- <span class="input-text-gray">{{ $prescriptions->total() ?? '' }}</span> -->
                                            </h3>
                                        </div>
                                        <div class=" d-flex d-flex justify-content-end gap-2 ">
                                            <div class="search_box position-relative">
                                                <input type="text" id="" value="" name="search_staffs"
                                                    class="searchMedicines form-control search_filter"
                                                    placeholder="Search by name...">
                                                <a class="reset-btn position-absolute reset_button clearSearchBtn" href="#">
                                                    <i class="fa-solid fa-xmark"></i>
                                                </a>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-end gap-2">
                                                <!-- <a  class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 reset_button">Reset</a> -->
                                            </div>
                                        </div>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-end pb-5 gap-3 flex-wrap rounded-0">
                                        <div class="result-container1">
                                            <div class="range-options additional-options">
                                                <div
                                                    class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                    <div class="start-range date-field">
                                                        <label for="adminStartRange">Start Range:</label>
                                                        <span class="error-message text-danger"
                                                            style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                            select Start Range
                                                            first.</span>
                                                        <input type="date" class="start_range-input start-range-input"
                                                            name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                    </div>

                                                    <div class="end-range date-field">
                                                        <label for="adminEndRange">End Range:</label>
                                                        <input type="date" class="end-range-input end-range-input"
                                                            name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div>

                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Status</option>
                                                    <option value="1">Paid</option>
                                                    <option value="0">Not Paid</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>


                                            <!-- <div class="custom-select">
                                                                                <select id="statusFilter" class="status_filter">
                                                                                    <option value="">Status</option>
                                                                                    <option value="1">Paid</option>
                                                                                    <option value="0">Not Paid</option>
                                                                                </select>
                                                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                                                    alt="Filter Icon">
                                                                            </div> -->

                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Type</option>
                                                    <option value="one_time">One Time</option>
                                                    <option value="repeat">Repeat</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>


                                            <!-- <div class="custom-select">
                                                                                <select id="statusFilter" class="type_filter">
                                                                                    <option value="">Type</option>
                                                                                    <option value="one_time">One Time</option>
                                                                                    <option value="repeat">Repeat</option>
                                                                                </select>
                                                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                                                    alt="Filter Icon">
                                                                            </div> -->
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive table_data">
                                    <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-300px">Name</th>
                                                <th class="min-w-200px">Date Added</th>
                                                @if ($user->hasRole('clinic_admin'))
                                                    <th class="min-w-200px">Prescribed By</th>
                                                @endif
                                                <th class="min-w-200px">Approval Status</th>
                                                <th class="min-w-200px">Payment Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($prescriptions as $perscription)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex gap-2 align-items-center">
                                                            <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                alt="" height="40px" width="40px">
                                                            <div class="d-flex flex-column">
                                                                <h5 class="fw-500 number-gray-black">
                                                                    {{ $perscription->patient->name ?? '' }}
                                                                </h5>
                                                                <span
                                                                    class="input-text-gray fs-14 fw-400">{{ $perscription->patient->email ?? '' }}</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>{{ $perscription->created_at->format('m d Y') }}</td>
                                                    @if ($user->hasRole('clinic_admin'))
                                                        <td>{{ $perscription->prescribedBy->name ?? '' }}</td>
                                                    @endif
                                                    <td><span
                                                            class="badge badge-{{ $perscription->admin_approval == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $perscription->approval_status }}</span>
                                                    </td>
                                                    <td><span
                                                            class="badge badge-{{ $perscription->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $perscription->status == 1 ? 'Paid' : 'Unpaid' }}</span>
                                                    </td>
                                                    <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                data-bs-toggle="dropdown" aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a href="{{ route('prescription-details',['id' => $perscription->order_id]) }}" class="dropdown-item Satoshi">View</a>
                                                                </li>
                                                            </ul>
                                                        </div>

                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $prescriptions->firstItem() }} to {{ $prescriptions->lastItem() }} of
                                            {{ $prescriptions->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $prescriptions->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if ($user->hasRole('clinic_admin'))
                            <div class="tab-pane fade" id="prescriber" role="tabpanel">
                                <div class="custom-dropdown">
                                    <div>
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                            <div>
                                                <h3> Prescribers
                                                    <!-- <span class="input-text-gray">{{ $doctors->total() ?? '' }}</span> -->
                                                </h3>
                                            </div>
                                            <div class=" d-flex d-flex justify-content-end gap-2 ">
                                                <div class="search_box position-relative">
                                                    <input type="text" value="" name="search_staffs"
                                                        class=" searchMedicines form-control search_filter"
                                                        placeholder="Search by name...">
                                                    <a class="reset-btn position-absolute reset_button clearSearchBtn" href="#">
                                                        <i class="fa-solid fa-xmark"></i>
                                                    </a>
                                                </div>
                                                <!-- <a class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 reset_button">Reset</a> -->
                                            </div>
                                        </div>
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-end align-items-end pb-5 gap-3 flex-wrap rounded-0">
                                            <div class="result-container1">
                                                <div class="range-options additional-options">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                        <div class="start-range date-field">
                                                            <label for="adminStartRange">Start Range:</label>
                                                            <span class="error-message text-danger"
                                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                                select Start Range
                                                                first.</span>
                                                            <input type="date" class="start_range-input start-range-input"
                                                                name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>

                                                        <div class="end-range date-field">
                                                            <label for="adminEndRange">End Range:</label>
                                                            <input type="date" class="end-range-input end-range-input"
                                                                name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                            <div>
                                                <!-- <div class="custom-select">
                                                                                                                        <select id="statusFilter" class="status_filter">
                                                                                                                            <option value="">All Prescribers</option>
                                                                                                                            <option value="1">Active</option>
                                                                                                                            <option value="0">Inactive</option>
                                                                                                                        </select>
                                                                                                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                                                                                            alt="Filter Icon">
                                                                                                                    </div> -->

                                                <div class="custom-select">
                                                    <select id="" class="status_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="" selected disabled>All Prescribers</option>
                                                        <option value="1">Active</option>
                                                        <option value="0">Inactive</option>
                                                    </select>
                                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center justify-content-end gap-2"></div>
                                        </div>
                                    </div>
                                    <div class="table-responsive table_data">
                                        <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <!-- <th class="min-w-20px first-and-last ps-5">
                                                                                            <input type="checkbox" id="select-all" class="select-all ">
                                                                                        </th> -->
                                                    <th class="min-w-300px">Name</th>
                                                    <th class="min-w-200px">Date Added</th>
                                                    <th class="min-w-200px">Status</th>
                                                    <th class="min-w-50px first-and-last"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($doctors as $doctor)
                                                    <tr>
                                                        <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $doctor->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $doctor->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $doctor->created_at->format('m d Y') ?? '' }}</td>
                                                        <td><span
                                                                class="badge badge-{{ $doctor->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $patient->status_text ?? '' }}</span>
                                                        </td>
                                                        <td class="action_btn">
                                                            <div class="dropdown">
                                                                <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                    data-bs-toggle="dropdown" aria-expanded="true">
                                                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                    <li><a class="dropdown-item Satoshi">View</a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $doctors->firstItem() }} to {{ $doctors->lastItem() }} of
                                                {{ $doctors->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $doctors->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="staff" role="tabpanel">
                                <div class="custom-dropdown">
                                    <div>
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                            <div>
                                                <h3> Staffs
                                                    <!-- <span class="input-text-gray">{{ $staffs->total() ?? '' }}</span>  -->
                                                </h3>
                                            </div>
                                            <div class="d-flex justify-content-end gap-2">
                                                <div class="search_box position-relative">
                                                    <input type="text" value="" name="search_staffs"
                                                        class=" searchMedicines form-control search_filter"
                                                        placeholder="Search by name...">
                                                    <a class="reset-btn position-absolute reset_button clearSearchBtn" href="#">
                                                        <i class="fa-solid fa-xmark"></i>
                                                    </a>
                                                </div>
                                                <div class="d-flex align-items-center justify-content-end gap-2">
                                                    <!-- <a class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 reset_button">Reset</a> -->
                                                </div>
                                            </div>
                                        </div>
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-end align-items-end pb-5 gap-3 flex-wrap rounded-0">
                                            <div class="result-container1">
                                                <div class="range-options additional-options">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                        <div class="start-range date-field">
                                                            <label for="adminStartRange">Start Range:</label>
                                                            <span class="error-message text-danger"
                                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                                select Start Range
                                                                first.</span>
                                                            <input type="date" class="start_range-input start-range-input"
                                                                name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>

                                                        <div class="end-range date-field">
                                                            <label for="adminEndRange">End Range:</label>
                                                            <input type="date" class="end-range-input end-range-input"
                                                                name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>All Staffs</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Inactive</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>


                                        </div>
                                    </div>
                                    <div class="table-responsive table_data">
                                        <table id="clinicTable" class="table display data_table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <!-- <th class="min-w-20px first-and-last ps-5">
                                                                                            <input type="checkbox" id="select-all" class="select-all ">
                                                                                        </th> -->
                                                    <th class="min-w-300px">Name</th>
                                                    <th class="min-w-200px">Date Added</th>
                                                    <th class="min-w-200px">Status</th>
                                                    <th class="min-w-50px first-and-last"></th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($staffs as $staff)
                                                    <tr>
                                                        <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $staff->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $staff->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>{{ $staff->created_at->format('m d Y') ?? '' }}</td>
                                                        <td><span
                                                                class="badge badge-{{ $staff->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $staff->status_text ?? '' }}</span>
                                                        </td>
                                                        <td>
                                                            <div class="dropdown">
                                                                <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                    data-bs-toggle="dropdown" aria-expanded="true">
                                                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                    <li><a class="dropdown-item Satoshi">View</a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $staffs->firstItem() }} to {{ $staffs->lastItem() }} of
                                                {{ $staffs->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $staffs->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 pt-12 mt-10">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-body p-0">

                            <!-- Header -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center py-3 ">
                                    @include('svg.prescriber-name')
                                    <span class="inter fs-12 fw-400 ps-2 deep-charcoal-blue">{{ $user->name ?? '' }}</span>
                                </div>
                                <span class="roboto fs-12 fw-400 medicines-status">{{ $user->status_text ?? '' }}</span>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $user->email ?? '' }}</h6>
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">Current Type assigned</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">{{ $user->latestUserType->type ?? '' }}</p>
                            </div>
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">No. of Patients</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">{{ $user->clinicPatinets()->count() ?? '' }}
                                </p>
                            </div>
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">No. of Prescriptions</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">
                                    {{ $user->clinicPrescriptions()->count() ?? '' }}
                                </p>
                            </div>
                            @if ($user->hasRole('clinic_admin'))
                                <div class="d-flex justify-content-between">
                                    <p class="fs-12 fw-500 table-gary-text mt-3">No. of Prescribers</p>
                                    <p class="fs-12 fw-500 table-gary-text mt-3">
                                        {{ $user->clinicStaff()->StaffDoctors()->count() ?? '' }}
                                    </p>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <p class="fs-12 fw-500 table-gary-text mt-3">No. of Staffs</p>
                                    <p class="fs-12 fw-500 table-gary-text mt-3">
                                        {{ $user->clinicStaff()->StaffReceptionists()->count() ?? '' }}
                                    </p>
                                </div>
                            @endif

                            <div>
                                <p class="fs-18 fw-500 table-gary-text mt-3 border_bottom">Previous Record</p>
                                @forelse($user->userTypes->sortByDesc('created_at')->skip(1) as $userType)
                                    <div class="d-flex justify-content-between">
                                        <p class="fs-12 fw-500 table-gary-text mt-3">Previous assigned Payment type</p>
                                        <p class="fs-12 fw-500 table-gary-text mt-3">{{ $userType->type }}</p>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <p class="fs-12 fw-500 table-gary-text mt-3">Duration</p>
                                        <p class="fs-12 fw-500 table-gary-text mt-3">{{ $userType->created_at->format('M Y') }}<span class="px-2">to</span>{{ $userType->created_at->addMonths(6)->format('M Y') }}</p>
                                    </div>
                                    <div class="d-flex justify-content-between border-bottom">
                                        <p class="fs-12 fw-500 table-gary-text mt-3">Status</p>
                                        <p class="fs-12 fw-500 table-gary-text mt-3">Changed by Admin</p>
                                    </div>
                                @empty
                                    <div class="d-flex justify-content-between border-bottom">
                                        <p class="fs-12 fw-500 table-gary-text mt-3">No previous records found</p>
                                    </div>
                                @endforelse
                            </div>
                            <div class="d-flex mt-4 gap-5 flex-wrap justify-content-center">
                                <a href="{{ route('change_status', ['user_id' => $user->id, 'status' => '1']) }}"
                                    class="{{ $user->status == 1 ? 'gradient_modal_approve white-color' : 'modal_grey_reject_btn deep-forest-green' }} roboto fs-14 fw-400 modal-save toggle-status"
                                    data-status="1">Activate</a>
                                <a href="{{ route('change_status', ['user_id' => $user->id, 'status' => '2']) }}"
                                    class="{{ $user->status == 2 ? 'gradient_modal_approve white-color' : 'modal_grey_reject_btn deep-forest-green' }} roboto fs-14 fw-400 toggle-status"
                                    data-status="0">Deactivate</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.templates.modals.clinic-modals.prescription_modal')
    @include('dashboard.templates.modals.clinic-modals.new_doctor_modal')
@endsection

@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->
    <script>
        $(document).on('click', '.reset_button', function () {
            location.reload();
        });
        $(document).ready(function () {
            $('.start-range-input').on('change', function () {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href'); // e.g., "#patients"
                $(activeTab).find('.end-range-input').prop('disabled', false);
                var startDate = $(this).val();
                var endDate = $(activeTab).find('.end-range-input').val();
                fetchData(page = 1, startDate, endDate);
            });
            var user_id = @json($user->id);
            let Timer = null;
            $('.search_filter').on('keyup', function () {
                clearTimeout(Timer);
                const searchValue = $(this).val().toLowerCase();
                fetchData();
                Timer = setTimeout(function () { }, 500);
            });
            $('.end-range-input').on('change', function () {
                fetchData();
            });
            $('.status_filter').on('change', function () {
                fetchData()
            });
            $('.type_filter').on('change', function () {
                fetchData()
            });

            function fetchData(page = 1, startDate = null, endDate = null) {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                var statusFilter = $(activeTab).find('.status_filter').val();
                var typeFilter = $(activeTab).find('.type_filter').val();
                var search = $(activeTab).find('.search_filter').val();
                $.ajax({
                    url: "{{ route('user.details.filter') }}",
                    type: "GET",
                    data: {
                        filter: statusFilter,
                        type_filter: typeFilter,
                        tab: activeTab,
                        page: page,
                        start_date: startDate,
                        end_date: endDate,
                        search: search,
                        user_id: user_id
                    },
                    success: function (data) {

                        $(activeTab).find('.table_data').html(data);
                    },
                    complete: function () {
                        KTMenu.createInstances();
                    }
                });
            }

            // Handle pagination click
            $(document).on('click', '.pagination a', function (e) {
                e.preventDefault();
                var page = $(this).attr('href').split('page=')[1];
                // Get the current date range values
                const activeTab = $('.nav-tabs .nav-link.active').attr('href'); // like #patients
                const startDate = $(activeTab).find('.start-range-input').val();
                let endDate = $(activeTab).find('.end-range-input').val();
                if (!endDate) {
                    endDate = new Date().toISOString().split('T')[0];
                }
                console.log('End Date:', endDate);
                console.log('Start Date:', startDate);
                // Check if the month options are visible and get the selected month if so
                fetchData(page, startDate, endDate);
            });
            const table = $('.data_table').DataTable({
                paging: false,
                pageLength: 5,
                lengthChange: false,
                searching: false,
                // ordering: true,
                info: false,
                ordering: false,
                columnDefs: [{
                    orderable: false,
                    targets: 0
                }],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>'
                    }
                }
            });

            $('#statusFilter').on('change', function () {
                const status = $(this).val();
                table.column(7).search(status, true, false).draw();
            });

            $('.select-all').on('click', function () {
                const isChecked = $(this).prop('checked');
                $('.row-select').prop('checked', isChecked);
            });

            $('.row-select').on('click', function () {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const hash = window.location.hash;
            if (hash) {
                const tabTrigger = document.querySelector(`a[href="${hash}"]`);
                if (tabTrigger) {
                    const tab = new bootstrap.Tab(tabTrigger);
                    tab.show();
                }
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const startRangeInput = document.querySelector('.start-range-input');
            const endRangeInput = document.querySelector('.end-range-input');
            const errorMessage = document.querySelector('.error-message');

            endRangeInput.addEventListener('focus', function () {
                if (!startRangeInput.value) {
                    errorMessage.style.display = 'block';
                    endRangeInput.blur(); // Calendar band karne ke liye
                    startRangeInput.focus();
                } else {
                    errorMessage.style.display = 'none';
                }
            });

            // Jab start range fill ki jaye to error hata dein
            startRangeInput.addEventListener('input', function () {
                if (startRangeInput.value) {
                    errorMessage.style.display = 'none';
                }
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const startRangeInput = document.querySelector('.start-range-input');
            const endRangeInput = document.querySelector('.end-range-input');

            startRangeInput.addEventListener('click', function () {
                if (startRangeInput.showPicker) {
                    startRangeInput.showPicker();
                }
            });
            endRangeInput.addEventListener('click', function () {
                if (endRangeInput.showPicker) {
                    endRangeInput.showPicker();
                }
            });
        });
    </script>
@endpush