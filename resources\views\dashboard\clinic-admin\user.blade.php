@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
 <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row clinic_index">
                <div class="col-lg-12">
                    <div class="d-flex justify-content-between ps-5">
                        <div>
                            <h4 class="roboto heading-gary fs-20 fw-500"> My Staff</h4>
                        </div>
                        {{-- <a href="#!" data-bs-toggle="modal" data-bs-target="#add_new_doctor_modal" class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save"> Add Prescriber</a> --}}
                        <div class="col-md-3">
                            <div class="purple-card light-green-bg h-100">
                                <div class="d-flex flex-column justify-content-center align-items-center add-member-group">
                                    <i data-bs-toggle="modal" data-bs-target="#add_new_staff_modal"
                                        class="fas fa-plus mb-0"></i>
                                    <h2 class="pt-3 mb-0 white-color">Add Staff Member</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto  fw-500">
                            <a class="nav-link active" data-bs-toggle="tab" href="#clinic_doctors">Prescribers</a>
                        </li>
                        <li class="nav-item roboto fw-500">
                            <a class="nav-link" data-bs-toggle="tab" href="#clinic_receptionist">Receptionist</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="clinic_doctors" role="tabpanel">
                            <div class="custom-dropdown">
                            <div class=" roboto d-flex justify-content-between align-items-center py-5">
                                <div>
                                <h3>Prescribers
                                    <!-- <span class="input-text-gray">({{ $countDoctors ?? 0 }})</span> -->
                                </h3>
                                </div>
                                <div class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                <div class="custom-select search-bar">
                                    <input type="search" class="search form-control" value="" name="search"
                                        placeholder="Search by name...">
                                </div>
                                <div class="custom-select">
                                    <select data-control="select2"  class="statusFilter" data-hide-search="true"  data-dropdown-css-class="w-200px" >
                                        <option value="" selected>All Prescribers</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                </div>
                            </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table display custom-table gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            {{-- <th class=" ps-5 min-w-20px first-and-last"><input type="checkbox" id="select-all" class="select-all ">
                                            </th> --}}
                                            <th class="min-w-300px">Prescriber Name</th>
                                            <th class="min-w-200px">Date Added</th>
                                            <th class="min-w-200px">Total Patients</th>
                                            <th class="min-w-200px">Total Prescriptions</th>
                                            <th class="min-w-200px">Status</th>
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="doctorTableBody">
                                        <!-- Data will be loaded here via AJAX -->
                                    </tbody>
                                </table>
                                <div id="doctorPaginationLinks">
                                    <!-- Pagination will be loaded here via AJAX -->
                                </div>
                            </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="clinic_receptionist" role="tabpanel">
                              <div class="custom-dropdown">
                            <div class=" roboto d-flex justify-content-between align-items-center py-5">
                                <div>
                                <h3>Receptionists
                                    <!-- <span class="input-text-gray">({{ $countReceptionists ?? 0 }})</span> -->
                                </h3>
                                </div>
                                <div class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                <div class=" custom-select search-bar">
                                    <input type="search" class="search form-control" value="" name="search"
                                        placeholder="Search by name...">
                                </div>
                                <div class="custom-select">
                                    <select class="statusFilter" data-control="select2"  data-hide-search="true"  data-dropdown-css-class="w-200px" >
                                        <option value="" selected>All Receptionist</option>
                                        <option value="active">Active</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                </div>
                            </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table display custom-table gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <!-- <th class="ps-5 min-w-20px first-and-last"><input type="checkbox" id="select-all" class="select-all ">
                                            </th> -->
                                            <th class="min-w-300px">Receptionist Name</th>
                                            <th class="min-w-200px">Date Added</th>
                                            <th class="min-w-200px">Total Patients</th>
                                            <th class="min-w-200px">Total Prescriptions</th>
                                            <th class="min-w-200px">Status</th>
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="receptionistTableBody">
                                        <!-- Data will be loaded here via AJAX -->
                                    </tbody>
                                </table>
                                <div id="receptionistPaginationLinks">
                                    <!-- Pagination will be loaded here via AJAX -->
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.templates.modals.clinic-modals.new_staff_modal')
@endsection

@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
         <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->

    <script>
        $(document).ready(function() {
            $('.statusFilter').on('change', function() {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                if (activeTab == '#clinic_doctors') {
                    loadStaff(null, 'doctor', null);
                }
                if (activeTab == '#clinic_receptionist') {
                    loadStaff(null, 'receptionist', null);
                }
                // const status = $(this).val();
                // table.column(7).search(status, true, false).draw();
            });
            let typingTimer;
            const doneTypingInterval = 500;
            $('.search').on('keyup', function() {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(() =>
                {
                    var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                    if (activeTab == '#clinic_doctors') {
                        loadStaff(null, 'doctor', null);
                    }
                    if (activeTab == '#clinic_receptionist') {
                        loadStaff(null, 'receptionist', null);
                    }
                }, doneTypingInterval);
            });

            // Handle search clear button (X) click
            $('.search').on('search', function() {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                if (activeTab == '#clinic_doctors') {
                    loadStaff(null, 'doctor', null);
                }
                if (activeTab == '#clinic_receptionist') {
                    loadStaff(null, 'receptionist', null);
                }
            });

            function loadStaff(page = 1, role = '', perPage = 10) {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                var StatusFilter = $(activeTab).find('.statusFilter').val();
                var search = $(activeTab).find('.search').val();
                $.ajax({
                    // url: "/staff/list?page=" + page,
                    url: "{{ route('staff.list') }}",
                    type: "GET",
                    dataType: "json",
                    data: {
                        page: page,
                        role: role,
                        per_page: perPage,
                        status: StatusFilter,
                        search: search,
                    },
                    beforeSend: function() {
                        // console.log("Loading page:", page);
                    },
                    success: function(data) {
                        let tableBody, paginationLinks;
                        if (role === 'doctor') {
                            tableBody = $('#doctorTableBody');
                            paginationLinks = $('#doctorPaginationLinks');
                        } else if (role === 'receptionist') {
                            tableBody = $('#receptionistTableBody');
                            paginationLinks = $('#receptionistPaginationLinks');
                        }

                        if (tableBody && paginationLinks) {
                            if (!data.rows.trim()) {
                                tableBody.html(
                                    `<tr><td colspan="7" class="text-center">No Record Found</td></tr>`
                                );
                                paginationLinks.html(""); // Clear pagination if no records exist
                            } else {
                                tableBody.html(data.rows);
                                paginationLinks.html(data.pagination);
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error: ", xhr.responseText);
                    }
                });
            }
            // Load staff on page load
            loadStaff(null, 'doctor');
            loadStaff(null, 'receptionist');

            // Handle pagination click
            $(document).on('click', '.custom-pagination-link', function(e) {
                e.preventDefault();
                let page = $(this).data('page');
                let role = $(this).data('type');
                if (page) {
                    loadStaff(page, role);
                }
            });
        });
    </script>
    <script>
         setTimeout(() => {
                            KTMenu.createInstances();
                        }, 100);

    </script>
@endpush
