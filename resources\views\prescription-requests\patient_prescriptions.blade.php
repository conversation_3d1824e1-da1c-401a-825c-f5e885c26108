@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <button id="backButton" class="button-gradient white-color roboto fs-14 fw-400 my-5">Back</button>
            <div class="row my-5">
                <div class="col-lg-9">
                    <div class="d-flex justify-content-between">
                        <h4 class="roboto heading-gary fs-20 fw-500">{{ $user->name ?? '' }}</h4>
                    </div>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#prescriptions">Prescriptions</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade active show" id="prescriptions" role="tabpanel">
                            <div>
                                <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                    <div>
                                        <h3> Prescriptions
                                            <!-- <span class="input-text-gray">{{ $prescriptions->total() ?? '' }}</span> -->
                                        </h3>
                                    </div>
                                    <div class="d-flex justify-content-end gap-2">
                                        <div class="search_box position-relative">
                                            <input type="search" id="searchMedicines" value="" name="search_staffs"
                                                class="form-control search_filter" placeholder="Search by name...">
                                            <!-- <a class="reset-btn position-absolute"  id="clearSearchBtn">
                                                               <i class="fa-solid fa-xmark"></i>
                                                               </a> -->
                                        </div>
                                        <!-- <div class="d-flex align-items-center justify-content-end gap-2">
                                                        <a
                                                            class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 reset_button">Reset</a>
                                                    </div> -->

                                    </div>
                                </div>
                                <div
                                    class="custom-dropdown roboto d-flex justify-content-end align-items-end pb-5 gap-3 flex-wrap rounded-0">
                                    <!-- <div class="result-container1">
                                                    <div class="range-options additional-options">
                                                        <div
                                                            class="range-container d-flex justify-content-between align-items-end gap-3 date-range-group">

                                                            <div class="start-range date-field">
                                                                <label for="start-range">Start Range:</label>
                                                                <input type="date" class="start-range-input" name="start-range" max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                            <div class="end-range date-field">
                                                                <label for="end-range">End Range:</label>
                                                                <input type="date" class="end-range-input" name="end-range" disabled max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div> -->

                                    <div class="result-container1">
                                        <div class="range-options additional-options">
                                            <div
                                                class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                <div class="start-range date-field">
                                                    <label for="docStartRange">Start Range:</label>
                                                    <input type="date" class="start-range-input" name="docStartRange"
                                                        value="{{ $docStartRange ?? '' }}">
                                                    <span class="error-message text-danger"
                                                        style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                        select Start Range
                                                        first.</span>
                                                </div>
                                                <div class="end-range date-field">
                                                    <label for="docEndRange">End Range:</label>
                                                    <input type="date" class="end-range-input" name="docEndRange"
                                                        value="{{ $docEndRange ?? '' }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <div>
                                        <!-- <div class="custom-select">
                                                        <select id="statusFilter" class="status_filter">
                                                            <option value="">Status</option>
                                                            <option value="1">Paid</option>
                                                            <option value="0">Not Paid</option>
                                                        </select>
                                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                            alt="Filter Icon">
                                                    </div> -->
                                        <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected disabled>Status</option>
                                                <option value="1">Paid</option>
                                                <option value="0">Not Paid</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                        @if (!auth()->user()->hasRole('staff'))


                                            <div class="custom-select">
                                                <select id="" class="status_filter type_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Type</option>
                                                    <option value="one_time">One Time</option>
                                                    <option value="repeat">Repeat</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>



                                            <!-- 
                                                        <div class="custom-select">
                                                            <select id="statusFilter" class="type_filter">
                                                                <option value="">Type</option>
                                                                <option value="one_time">One Time</option>
                                                                <option value="repeat">Repeat</option>
                                                            </select>
                                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                                alt="Filter Icon">
                                                        </div> -->
                                        @endif
                                    </div>
                                    <!-- {{-- <form method="GET"
                                                    action="{{ route('users_details', ['id' => $user->id]) }}#prescriptions">
                                                    <div class="d-flex align-items-center justify-content-end gap-2">
                                                        <button class="button-gradient white-color roboto fs-14 fw-400"
                                                            type="submit">Search</button>
                                                        <a href="{{ route('users_details', ['id' => $user->id]) }}#prescriptions">
                                                            <button class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                                                                type="button">Reset</button>
                                                        </a>
                                                    </div>
                                                </form> --}} -->

                                </div>

                            </div>
                            <div class="table-responsive table_data">
                                <table id="clinicTable" class="table display data_table gy-5 gs-5">
                                    <thead>
                                        <tr>
                                            <th class="w-600px">Name</th>
                                            <th class="w-200px">Date Added</th>
                                            @if ($user->hasRole('clinic_admin'))
                                                <th class="w-200px">Prescribed By</th>
                                            @endif
                                            <th class="w-200px">Approval Status</th>
                                            <th class="w-200px">Payment Status</th>
                                            <th class="w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($prescriptions as $perscription)
                                            <tr>
                                                <td>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                            alt="" height="40px" width="40px">
                                                        <div class="d-flex flex-column">
                                                            <h5 class="fw-500 number-gray-black">
                                                                {{ $perscription->patient->name ?? '' }}
                                                            </h5>
                                                            <span
                                                                class="input-text-gray fs-14 fw-400">{{ $perscription->patient->email ?? '' }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ $perscription->created_at->format('m d Y') }}</td>
                                                @if ($user->hasRole('clinic_admin'))
                                                    <td>{{ $perscription->prescribedBy->name ?? '' }}</td>
                                                @endif
                                                <td><span
                                                        class="badge badge-{{ $perscription->admin_approval == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $perscription->approval_status }}</span>
                                                </td>
                                                <td><span
                                                        class="badge badge-{{ $perscription->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $perscription->status == 1 ? 'Paid' : 'Unpaid' }}</span>
                                                </td>
                                                <td class="action_btn">
                                                    <!-- <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                        <i class="fa-solid fa-ellipsis-vertical"></i>
                                                    </a>
                                                    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                        data-kt-menu="true">
                                                        <div class="menu-item px-3">
                                                            <a href="{{ route('prescription-details', $perscription->order_id) }}"
                                                                class="menu-link px-3 view-prescription">View</a>
                                                        </div>
                                                    </div> -->
                                                    <div class="dropdown">
                                                        <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                            data-bs-toggle="dropdown" aria-expanded="true">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                            <li><a class="dropdown-item Satoshi"
                                                                href="{{ route('prescription-details', $perscription->order_id) }}">View</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                {{ $prescriptions->links() }}
                                <div class="d-flex justify-content-between align-items-center mt-5">
                                    <p>
                                        Showing {{ $prescriptions->firstItem() }} to {{ $prescriptions->lastItem() }} of
                                        {{ $prescriptions->total() }}
                                        entries
                                    </p>
                                    <div class="pagination">
                                        {{ $prescriptions->links() }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 pt-12 mt-10">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-body p-0">

                            <!-- Header -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center py-3 ">
                                    @include('svg.prescriber-name')
                                    <span class="inter fs-12 fw-400 ps-2 deep-charcoal-blue">{{ $user->name ?? '' }}</span>
                                </div>
                                <span class="roboto fs-12 fw-400 medicines-status">{{ $user->status_text ?? '' }}</span>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $user->email ?? '' }}</h6>
                            <!-- {{-- <div class="d-flex justify-content-between">
                                            <p class="fs-12 fw-500 table-gary-text mt-3">No. of Patients</p>
                                            <p class="fs-12 fw-500 table-gary-text mt-3">{{ $user->clinicPatinets()->count() ?? '' }}</p>
                                        </div> --}} -->
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">No. of Prescriptions</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">
                                    {{ $user->clinicPrescriptions()->count() ?? '' }}
                                </p>
                            </div>
                            <!-- {{-- <div class="d-flex justify-content-between">
                                            <p class="fs-12 fw-500 table-gary-text mt-3">No. of Prescribers</p>
                                            <p class="fs-12 fw-500 table-gary-text mt-3">{{ $user->clinicStaff()->StaffDoctors()->count() ?? '' }}</p>
                                        </div> --}} -->
                            <!-- {{-- <div class="d-flex justify-content-between">
                                            <p class="fs-12 fw-500 table-gary-text mt-3">No. of Staffs</p>
                                            <p class="fs-12 fw-500 table-gary-text mt-3">{{ $user->clinicStaff()->StaffReceptionists()->count() ?? '' }}</p>
                                        </div> --}} -->
                            @if (!auth()->user()->hasRole('staff'))
                                <div class="d-flex mt-4 gap-5 flex-wrap">
                                    <a href="{{ route('change_status', ['user_id' => $user->id, 'status' => '1']) }}"
                                        class="{{ $user->status == 1 ? 'gradient_modal_approve white-color' : 'modal_grey_reject_btn deep-forest-green' }} roboto fs-14 fw-400 modal-save toggle-status"
                                        data-status="1">Activate</a>
                                    <a href="{{ route('change_status', ['user_id' => $user->id, 'status' => '0']) }}"
                                        class="{{ $user->status == 0 ? 'gradient_modal_approve white-color' : 'modal_grey_reject_btn deep-forest-green' }} roboto fs-14 fw-400 toggle-status"
                                        data-status="0">Deactivate</a>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.templates.modals.clinic-modals.prescription_modal')
    @include('dashboard.templates.modals.clinic-modals.new_doctor_modal')
@endsection

@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->
    <script>
        $(document).on('click', '.reset_button', function () {
            location.reload();
        });
        $(document).ready(function () {
            $('.start-range-input').on('change', function () {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href'); // e.g., "#patients"
                $(activeTab).find('.end-range-input').prop('disabled', false);
                var startDate = $(this).val();
                var endDate = $(activeTab).find('.end-range-input').val();
                fetchData(page = 1, startDate, endDate);
            });
            var user_id = @json($user->id);
            let Timer = null;
            $('.search_filter').on('keyup', function () {
                clearTimeout(Timer);
                const searchValue = $(this).val().toLowerCase();
                fetchData();
                Timer = setTimeout(function () { }, 500);
            });
            $('.end-range-input').on('change', function () {
                fetchData();
            });
            $('.status_filter').on('change', function () {
                fetchData()
            });
            $('.type_filter').on('change', function () {
                fetchData()
            });

            function fetchData(page = 1, startDate = null, endDate = null) {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                var statusFilter = $(activeTab).find('.status_filter').val();
                var typeFilter = $(activeTab).find('.type_filter').val();
                var search = $(activeTab).find('.search_filter').val();
                $.ajax({
                    url: "{{ route('patient.prescription.filter') }}",
                    type: "GET",
                    data: {
                        filter: statusFilter,
                        type_filter: typeFilter,
                        tab: activeTab,
                        page: page,
                        start_date: startDate,
                        end_date: endDate,
                        search: search,
                        user_id: user_id
                    },
                    success: function (data) {

                        $(activeTab).find('.table_data').html(data);
                    },
                    complete: function () {
                        KTMenu.createInstances();
                    }
                });
            }

            // Handle pagination click
            $(document).on('click', '.pagination a', function (e) {
                e.preventDefault();
                var page = $(this).attr('href').split('page=')[1];
                // Get the current date range values
                const activeTab = $('.nav-tabs .nav-link.active').attr('href'); // like #patients
                const startDate = $(activeTab).find('.start-range-input').val();
                let endDate = $(activeTab).find('.end-range-input').val();
                if (!endDate) {
                    endDate = new Date().toISOString().split('T')[0];
                }
                console.log('End Date:', endDate);
                console.log('Start Date:', startDate);
                // Check if the month options are visible and get the selected month if so
                fetchData(page, startDate, endDate);
            });
            const table = $('.data_table').DataTable({
                paging: false,
                pageLength: 5,
                lengthChange: false,
                searching: false,
                // ordering: true,
                info: false,
                ordering: false,
                columnDefs: [{
                    orderable: false,
                    targets: 0
                }],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>'
                    }
                }
            });

            $('#statusFilter').on('change', function () {
                const status = $(this).val();
                table.column(7).search(status, true, false).draw();
            });

            $('.select-all').on('click', function () {
                const isChecked = $(this).prop('checked');
                $('.row-select').prop('checked', isChecked);
            });

            $('.row-select').on('click', function () {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });
        });
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const hash = window.location.hash;
            if (hash) {
                const tabTrigger = document.querySelector(`a[href="${hash}"]`);
                if (tabTrigger) {
                    const tab = new bootstrap.Tab(tabTrigger);
                    tab.show();
                }
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            $('#backButton').on('click', function () {
                window.history.back();
            });
        });
    </script>
@endpush