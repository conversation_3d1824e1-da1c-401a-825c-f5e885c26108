<?php

namespace App\Http\Controllers;

use App\Models\Prescription;
use App\Models\PrescriptionPayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\PaymentIntent;
use Stripe\Account;
use App\Models\User;
use Stripe\AccountLink;
use Stripe\StripeClient;
use Stripe\Transfer;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class StripeController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $this->stripe = new StripeClient(env('STRIPE_SECRET'));
    }
    public function createCheckoutSessionOLD(Request $request)
    {
        // Get the authenticated user.
        $user = auth()->user();

        // Define the basic parameters for the Checkout Session.
        $sessionParams = [
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency'     => 'gbp',
                        'unit_amount'  => 5000, // $50.00 in cents
                        'product_data' => [
                            'name' => 'Consultation Fee',
                        ],
                    ],
                    'quantity' => 1,
                ],
            ],
            'mode'         => 'payment',
            'success_url'  => route('checkout.success'),
            'cancel_url'   => route('checkout.cancel'),
        ];

        // Check user type.
        if ($user->clinic->type === 'Type 2') {
            // If user is Type 2, we expect them to have a connected account.
            // if (empty($user->stripe_account_id)) {
            //     return redirect()->back()->with('error', 'Your connected Stripe account is not set up.');
            // }
            // Add transfer_data so that funds are routed to the connected account.
            $sessionParams['payment_intent_data'] = [
                'transfer_data' => [
                    'destination' => $user->clinic->stripe_account_id,
                ],

                'on_behalf_of' => $user->clinic->stripe_account_id,
            ];
        }
        // For type1, no extra parameters are added—the payment stays on your platform account.

        // Create the Stripe Checkout Session.
        $session = Session::create($sessionParams);

        // Redirect the user to Stripe's hosted Checkout page.
        return redirect($session->url);
    }
    public function createCheckoutSession(Request $request)
    {
        // Validate the prescription id exists in the request.
        $request->validate([
            // 'prescription_id' => 'required|exists:prescriptions,order_id',
            'prescription_id' => 'required',
        ]);
        if($request->flag == 'repeat'){
            $prescription = PrescriptionPayment::where('order_id', $request->prescription_id)->firstOrFail();
        }
        else{
            $prescription = Prescription::where('order_id', $request->prescription_id)->firstOrFail();
        }
        // Get the authenticated user.
        $user = auth()->user();

        // Convert the prescription amount to the smallest currency unit (pence for GBP).
        $unitAmount = $prescription->total * 100;

        // Build the basic parameters for the Checkout Session.
        $sessionParams = [
            'client_reference_id' => $prescription->order_id,  // Pass the prescription id
            'payment_method_types' => ['card'],
            'line_items' => [
                [
                    'price_data' => [
                        'currency'     => 'gbp',
                        'unit_amount'  => $unitAmount,
                        'product_data' => [
                            'name' => 'Prescription Payment',
                        ],
                    ],
                    'quantity' => 1,
                ],
            ],
            'mode'         => 'payment',
            // 'success_url'  => route('checkout.success') . '?session_id={CHECKOUT_SESSION_ID}',
            'success_url'  => route('checkout.success'),
            'cancel_url'   => route('order-details', $prescription->order_id),
        ];
        // if (empty($user->clinic) || empty($user->clinic->stripe_account_id)) {
        //     return redirect()->back()->with(['type' => 'warning', 'title' => "Something Went Wrong.", 'message' => 'Please Contact Admin']);
        // }
        // Check user type and, for Type 2, add transfer parameters.
        // if ($user->clinic->type === 'Type 2') {
        if ($prescription->type === 'Type 2') {
            // Ensure the user has an associated doctor with a connected Stripe account.

            $sessionParams['payment_intent_data'] = [
                'transfer_data' => [
                    'destination' => $user->clinic->stripe_account_id,
                ],
                'on_behalf_of' => $user->clinic->stripe_account_id,
            ];
        }

        // Create the Stripe Checkout Session.
        $session = Session::create($sessionParams);
        session()->put([
            'stripe_session' => $session,
            'order_id' => $prescription->order_id,
            'flag' => $request->flag,
        ]);

        // Redirect the patient to Stripe's hosted Checkout page.
        return redirect($session->url);
    }

    public function checkoutSuccess(Request $request)
    {
        // $sessionId = $request->session_id;
        // $session = Session::retrieve($sessionId);

        // $paymentIntentId = $session->payment_intent;
        // $paymentIntent = \Stripe\PaymentIntent::retrieve($paymentIntentId, [
        //     'expand' => ['charges'],
        // ]);

        // if (!empty($paymentIntent->charges) && !empty($paymentIntent->charges->data)) {
        //     $charge = $paymentIntent->charges->data[0];
        //     $chargeId = $charge->id;
        //     $receiptUrl = $charge->receipt_url;
        // }
        // $prescriptionId = $session->client_reference_id;
        // $prescription = Prescription::where('order_id', $prescriptionId)->firstOrFail();
        if(session('flag') == 'repeat'){
            $prescription = PrescriptionPayment::where('order_id', session('order_id'))->firstOrFail();
            $prescription->status = 'paid';
            $prescription->save();
        }
        else{
            $prescription = Prescription::where('order_id', session('order_id'))->firstOrFail();
             $prescription->status = 1;
             $prescription->save();
        }
        return redirect()->route('orders.patient')->with(['message' => 'Paid Successfully', 'type' => 'success']);
    }

    public function checkoutCancel()
    {
        return redirect()->route('orders.patient')->with(['message' => 'Payment Failed', 'type' => 'error']);
        // return view('checkout.cancel');   // Create this Blade view for cancellation.
    }





    public function createOnboardingLink(Request $request)
    {
        $user = auth()->user();

        try {
            $account = Account::create([
                'type' => 'standard',
                'country' => 'GB',
                'email' => $user->email,
            ]);

            $user->stripe_account_id = $account->id;
            $user->save();

            $accountLink = AccountLink::create([
                'account' => $account->id,
                'refresh_url' => route('stripe.onboarding.cancel'),
                'return_url' => route('stripe.onboarding.complete'),
                'type' => 'account_onboarding',
            ]);

            return redirect($accountLink->url);
        } catch (\Exception $e) {
            return back()->with('error', 'Error creating onboarding link: ' . $e->getMessage());
        }
    }

    public function onboardingComplete(Request $request)
    {
        $user = auth()->user();

        try {
            $account = \Stripe\Account::retrieve($user->stripe_account_id);

            if ($account->charges_enabled && $account->payouts_enabled) {
                $user->onboarding_status = true;
                $user->save();

                return redirect()->route('profile_setting')->with([
                    'type' => 'success',
                    'title' => 'Onboarding Complete',
                    'message' => 'You are now ready to receive payments!',
                ]);
            } else {
                return redirect()->route('profile_setting')->with([
                    'message' => 'You are not yet ready to receive payments. Please complete the onboarding process.',
                    'title' => 'Onboarding Incomplete',
                    'type' => 'error'
                ]);
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Error verifying onboarding: ' . $e->getMessage());
        }
    }

    public function onboardingCancel(Request $request)
    {
        return redirect()->route('profile_setting')->with([
            'type' => 'error',
            'title' => 'Onboarding Cancelled',
            'message' => 'The onboarding process was cancelled. Please try again.',
        ]);
    }





    // Redirect the doctor to Stripe’s OAuth page.
    public function redirectToStripe()
    {
        $client_id    = config('services.stripe.client_id');
        $redirect_uri = route('doctor.stripe.callback'); // Must be registered in Stripe settings

        $url = "https://connect.stripe.com/oauth/authorize?response_type=code"
            . "&client_id={$client_id}"
            . "&scope=read_write"
            . "&redirect_uri=" . urlencode($redirect_uri);

        return redirect($url);
    }

    // Handle the OAuth callback from Stripe.
    public function handleStripeCallback(Request $request)
    {
        if ($request->has('error')) {
            return redirect()->route('dashboard')
                ->with('error', 'Stripe connection failed: ' . $request->error_description);
        }

        $code = $request->code;
        $response = Http::asForm()->post('https://connect.stripe.com/oauth/token', [
            'client_secret' => config('services.stripe.secret'),
            'code'          => $code,
            'grant_type'    => 'authorization_code'
        ]);

        $stripeData = $response->json();

        if (isset($stripeData['stripe_user_id'])) {
            // Save the connected account ID on the authenticated user record.
            $doctor = auth()->user();
            $doctor->stripe_account_id = $stripeData['stripe_user_id'];
            $doctor->save();

            return redirect()->route('dashboard')
                ->with('success', 'Your Stripe account has been connected successfully.');
        } else {
            return redirect()->route('dashboard')
                ->with('error', 'An error occurred while connecting your Stripe account.');
        }
    }
}
