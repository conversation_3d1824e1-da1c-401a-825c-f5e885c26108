@extends('website.layout.master')
@section('content')
    <section class="hero-section">
        <div class="container">
            <div class="row ">
                <div class="col-xl-6 col-12">
                    <div class="banner-text">
                    {!! $home_cms->title ?? '' !!}
                    </div>
                    <div class="started-btn justify-content-xl-start justify-content-center d-flex">
                        <a href="{{ $home_cms->btn_link ?? 'login' }}"
                            class="button_started py-3">{{ auth()->guest() ? ($home_cms->btn_title ?? '') : 'Dashboard' }}</a>
                    </div>
                    <div class="hero-logos-parent d-flex column-gap-12 pt-10 justify-content-xl-start justify-content-center flex-wrap">
                        <div class="hero-logo">
                            <img src="{{ asset('website') }}/assets/media/images/npa-img.png" alt="npa-img">
                        </div>
                        <div class="hero-logo ico-hero-logo">
                            <img src="{{ asset('website') }}/assets/media/images/ico-img.png" alt="ico-img">
                        </div>
                        <div class="hero-logo">
                            <img src="{{ asset('website') }}/assets/media/images/network-img.png" alt="network-img">
                        </div>
                        <div class="hero-logo">
                            <img src="{{ asset('website') }}/assets/media/images/council-img.png" alt="council-img">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 position-relative ">
                    <div class="hero-banner-image position-absolute">
                        <img src="{{ asset('website/' . $home_cms->banner_image) }}" alt="dashboard-img">

                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="patients-data-sec py-20">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-xl-8 offset-xl-2 col-12 text-center position-relative">
                    {!! $home_cms->section_one_title ?? '' !!}
                </div>
                @foreach ($section_one as $section)
                    <div class="col-lg-3  col-sm-6">
                        <h5 class="roboto Deep-Ocean-Blue patients-data-card-head ps-5 ">{!! $section->title ?? '' !!}</h5>
                        <p class="fs-14 normal text-black ps-5 pt-5">{!! $section->description ?? '' !!}</p>
                    </div>
                @endforeach

            </div>
        </div>
    </section>
    <section class="streamline-patient-sec py-20">
        <div class="container">
            <div class="row">
                <div class="col-xl-6 col-12 offset-xl-3 text-center">
                    {!! $home_cms->section_two_content ?? '' !!}
                    {{-- <h2 class="roboto light-green pb-10"> Simplify, Secure,<span class="white-color">And</span>  Streamline <span class="white-color">Your Patient Care</span></h2>
                        <p class="fs-20 normal ice-green roboto ">Our electronic private prescription service allows clinicians to send paperless, secure and completely compliant prescriptions direct to our central dispensary for next day delivery. </p> --}}
                </div>
                <div class="col-lg-12">
                    <div class="row row-gap-5 justify-content-center">
                        @foreach ($section_two as $section)
                            <div class="col-lg-3  col-sm-6">
                                <div class="patient-care-logo-parent">
                                    <img src="{{ asset('website/' . $section->image) }}" alt="start-logo">
                                </div>
                                <p class="fs-14 normal white-color ps-xl-5 pt-xl-4 ps-0 pt-0 streamline-patient-text">
                                    {!! $section->description ?? '' !!}</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container my-20">
            <div class=" row mb-15">
                <div class="col-md-6">
                    <p class="Blue-Violet Roboto">{{ $home_cms->section_three_title ?? '' }}</p>
                    {{-- <h2>Complete <span class="Blue-Violet"> Solution </span> To Electronic Prescribing </h2> --}}
                    {!! $home_cms->section_three_subtitle ?? '' !!}
                </div>

                <div class="col-md-6">
                    {{-- <p>Generate and send prescriptions in seconds with just a few clicks. No handwriting errors, no misinterpretations—just clear, accurate
                        digital scripts.Generate and send prescriptions in seconds with just a few clicks. No handwriting errors, no misinterpretations—just clear, accurate digital scripts.</p>

                    <p> Generate and send prescriptions in seconds with just a few clicks. No handwriting errors, no misinterpretations.</p> --}}
                    {!! $home_cms->section_three_description ?? '' !!}
                </div>
            </div>

            <div class="row row-gap-5">
                @foreach ($section_tree as $section)
                    <div class="col-lg-4 col-md-6 col-12">
                        <div class="card home-cards-electronic border-0">
                            <div class="card-header border-bottom-0">
                                <div class="purple-box">
                                    <img src="{{ asset('website/' . $section->image) }}" alt="start-logo">
                                </div>
                            </div>
                            <div class="card-body">

                                <h5 class="mb-4">{{ $section->title ?? '' }}</h5>

                                {!! $section->description ?? '' !!}

                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
    <section class="custom-background doctor-choose-us">
        <div class="container">
            <div class="row">
                <div class="col-lg-5 m-auto">
                    {{-- <h2 class="text-center pb-5">Best <span class="green-color"> Service, </span>To Improve Your<span
                                class="Blue-Violet"> Customer </span> Satisfaction</h2> --}}
                    {!! $home_cms->section_four_title ?? '' !!}
                </div>
                <div class="row row-gap-5">
                    @foreach ($section_four as $section)
                        <div class="col-md-6">
                            <div class="card card-doctor  position-relative card-bg1">
                                <div class="text-end p-7 image-border">
                                    <img src="{{ asset('website/' . $section->image) }}" alt="start-logo"
                                        class="img-fluid customer-service">
                                </div>

                                <div class="p-9">
                                    <h5 class="bold mb-5">{{ $section->title ?? '' }}</h5>

                                    <p>{!! $section->description ?? '' !!}</p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    <section class="prescribing-workflow">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class=" content-heading d-flex  flex-column align-items-center">
                        {{-- <h2 class="text-center pb-5 text-white w-xl-50 w-100">Ready to Upgrade Your <span class="green-color">Prescribing Workflow? </span></h2> --}}
                       <h2 class="text-center pb-5 text-white w-xl-50 w-100"> {!! $home_cms->section_five_title ?? '' !!}</h2>
                        <h6 class="w-md-75 w-100 ice-green text-center"> {!! $home_cms->section_five_description ?? '' !!}</h6>

                        <a href="{{ $home_cms->section_five_btn_link ?? 'login' }}"
                            class="button_started py-3 mt-5">{{ $home_cms->section_five_btn_title ?? '' }}</a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="custom-background doctor-choose-us">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    {!! $home_cms->section_six_title ?? '' !!}
                </div>
                <div class="row row-gap-5">
                    @foreach ($testimonials as $testimonial)
                        <div class="col-lg-4">
                            <div class="card card-doctor h-100">
                                <div class="card-header border-0 pt-7 px-5">
                                    <div class="ratings">
                                        @for ($stars = 0; $stars < $testimonial->rating; $stars++)
                                            @include('svg.ratings')
                                        @endfor
                                    </div>
                                </div>
                                <div class="card-body py-5 px-5 ">
                                    {!! $testimonial->description ?? '' !!}
                                </div>
                                <div class="card-footer border-0 d-flex gap-5 pb-5 px-5">
                                    <div class="image-user">
                                        <img src="{{ asset('website/' . $testimonial->image) }}" alt="user-img"
                                            class="rounded-pill">
                                    </div>
                                    <div class="user-details">
                                        <h5 class="m-0">{{ $testimonial->name ?? '' }}</h5>
                                        <p class="Majesticpurple">{{ $testimonial->designation ?? '' }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    <section class="benefit-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h2 class="green-text text-center">
                        {!! $home_cms->section_seven_title ?? 'Benefits of Using Direct RX' !!}
                    </h2>
                </div>
            </div>
            <div class="row row-gap-5">
                @foreach ($section_seven as $feature)
                    <div class="col-xl-3 col-sm-6 col-12">
                        <div class="card benefit-card">
                            <div class="card-header border-0 d-flex align-items-center justify-content-center flex-column">
                                <div class="patient-care-logo-parent even mb-5">
                                    <img src="{{ asset('website/' . $feature->image) }}"
                                         class="top-rated-image h-75 w-75"
                                         alt="{{ $feature->title }}">
                                </div>
                                <h5 class="text-center ms-3">{{ $feature->title }}</h5>
                            </div>
                            <div class="card-body">
                                <p class="streamline-patient-text">
                                    {!! $feature->description !!}
                                </p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endsection
