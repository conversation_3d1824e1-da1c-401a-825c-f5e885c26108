<div id="kt_app_content" class="app-content">
    <div id="kt_app_content_container" class="app-container">
        <section class="overview-sec">
            <div class="row">
                <div class="col-lg-12">
                    <h4 class="mb-3 heading-gary roboto"> Overview</h4>
                </div>
            </div>
            <div class="row clinic_index row-gap-2">
                <div class="col-xl-3 col-sm-6 col-12">
                    <a href="<?php echo e(route('user.requests')); ?>#admins" class="number-gray-black fs-21 fw-700">
                        <div class="purple-card h-100">
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-600 input-text-gray"> TOTAL CLINIC </p><i
                                    class="fas fa-chevron-right"></i>
                            </div>
                            <div class="d-flex justify-content-between staff_percent">
                                <div class="fs-21 fw-700" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dashboardData['totalClinic']); ?>"
                                    data-kt-countup-prefix="">0
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-sm-6 col-12">
                    <a href="<?php echo e(route('user.requests')); ?>#staff_doctors"class="number-gray-black fs-21 fw-700">
                        <div class="purple-card h-100">
                            <div class="d-flex justify-content-between">

                                <p class="fs-12 fw-600 input-text-gray"> TOTAL STAFF </p><i
                                    class="fas fa-chevron-right"></i>

                            </div>

                            <div class="d-flex justify-content-between ">
                                <div class="fs-21 fw-700" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dashboardData['totalStaff']); ?>"
                                    data-kt-countup-prefix="">0
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-sm-6 col-12">
                    <a href="<?php echo e(route('user.requests')); ?>#doctors"class="number-gray-black fs-21 fw-700">
                        <div class="purple-card h-100">
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-600 input-text-gray"> TOTAL INDIVIDUALS </p><i
                                    class="fas fa-chevron-right"></i>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div class="fs-21 fw-700" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dashboardData['totalIndividuals'] ?? ''); ?>"
                                    data-kt-countup-prefix="">
                                    0
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-sm-6 col-12">
                    <a href="<?php echo e(route('orders')); ?>" class="number-gray-black fs-21 fw-700">
                        <div class="purple-card h-100">
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-600 input-text-gray"> TOTAL PRESCRIPTIONS </p><i
                                    class="fas fa-chevron-right"></i>
                            </div>

                            <div class="d-flex justify-content-between">
                                <div class="fs-21 fw-700" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($dashboardData['totalPrescriptions']); ?>"
                                    data-kt-countup-prefix="">0
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row pt-5 row-gap-5">
                <div class="col-lg-12">
                    <h4 class="mb-3 heading-gary roboto"> Analytics</h4>
                </div>
                <div class="col-xl-8 col-12">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="purple-card">
                                <div class="graph-section-main d-flex justify-content-between px-7 flex-wrap">
                                    <p class="fs-16 fw-700 mb-3 heading-gary roboto pt-2"> Sales Report</p>
                                    <div>
                                        <ul
                                            class="Sales-report nav nav-tabs nav-line-tabs mb-5 fs-6 justify-content-end">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-bs-toggle="tab" href="#months12">12
                                                    Months</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-bs-toggle="tab" href="#months6">6 Months</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-bs-toggle="tab" href="#days30">30 Days</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" data-bs-toggle="tab" href="#days7">7 Days</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active" id="months12" role="tabpanel">
                                        <div class="pt-5">
                                            <canvas id="months12-chart" class="mh-400px"> </canvas>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="months6" role="tabpanel">
                                        <div class="pt-5">
                                            <canvas id="months6-chart" class="mh-400px"> </canvas>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="days30" role="tabpanel">
                                        <div class="pt-5">
                                            <canvas id="day30-chart" class="mh-400px"> </canvas>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="days7" role="tabpanel">
                                        <div class="pt-5">
                                            <canvas id="day7-chart" class="mh-400px"> </canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4 col-12 mb-4">
                    <div class="purple-card">
                        <div class="d-flex justify-content-between flex-wrap gap-3">
                            <h4 class=" m-0 heading-gary border_bottom pb-2 w-100"> Recent Registrations Requests </h4>

                            <?php $__empty_1 = true; $__currentLoopData = $dashboardData['recenRegsUnapproved']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <div class="d-flex justify-content-between mb-4 w-100">
                                    <div class="fw-500 roboto d-flex gap-2">
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/images/reinger-icon.svg"
                                            alt="user_icon" />
                                        <div class="">
                                            <!-- <div class="d-flex gap-3"> -->
                                            <h5 class="number-gray-black m-0"><?php echo e($item->email ?? ''); ?></h5>
                                            <!-- <h5 class="number-gray-black badge-active-small m-0">active</h5> -->
                                            <!-- </div> -->
                                            <h6 class="input-text-gray m-0"><?php echo e($item->name ?? ''); ?></h6>
                                        </div>
                                    </div>
                                    <div class="d-flex gap-3 align-items-center">

                                        <h5 class="number-gray-black badge-<?php echo e($item->status == 0 ? 'inactive' : 'active'); ?>-small m-0 align-content-center">
                                            <?php echo e($item->status == 0 ? 'Pending' : 'Active'); ?></h5>
                                        <h6 class="input-text-gray m-0"><?php echo e($item->created_at->format('m d Y') ?? ''); ?>

                                        </h6>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <p class="text-center">No registrations requests found</p>
                            <?php endif; ?>
                            <div class="w-100 d-flex justify-content-center">
                                <a href="<?php echo e(route('user.requests')); ?>"
                                    class="fs-14 fw-500 m-0 gradient_modal_approve text-white px-10"> VIEW
                                    ALL</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row pt-3 pb-5">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between mt-5">
                        <h4 class=" mb-3 heading-gary"> Recent Registrations </h4>
                        <a href="<?php echo e(route('admin.users')); ?>"
                            class="fs-14 fw-500 mb-3 gradient_modal_approve text-white px-10
 "> VIEW ALL</a>
                    </div>
                </div>
            </div>

            <div class="row">
                <?php $__empty_1 = true; $__currentLoopData = $dashboardData['recenRegsApproved']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-md-3 mb-4">
                        <div class="purple-card">
                            <div class="d-flex justify-content-between">
                                <div class="d-flex">
                                    <div>
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/images/reinger-icon.svg"
                                            alt="reinger-icon">
                                    </div>
                                    <div class="ms-2">
                                        <p class="fs-14 fw-500 mb-0"> <?php echo e($item->name ?? ''); ?> </p>
                                        <p class="fs-12 fw-500 mb-0 assigned-badge mt-2">
                                            <?php echo e($item->getRoleText() ?? ''); ?> </p>
                                        <p class="heading-gary mb-0 mt-2"> <?php echo e($item->role_name ?? ''); ?> </p>
                                        <h6 class="heading-gary mb-0"> <?php echo e($item->email ?? ''); ?> </h6>
                                        <h6 class="heading-gary mb-0 pt-1">
                                            <?php echo e($item->created_at->format('m d Y') ?? ''); ?></h6>
                                    </div>
                                </div>
                                <div>
                                    <p class="light-green-badge">
                                        <?php echo e($item->status == 1 ? 'Active' : 'In-Actives' ?? ''); ?>

                                    </p>
                                </div>
                            </div>

                            <hr>

                            <div class="d-flex justify-content-between pb-3">
                                <h6 class="mb-0">
                                    <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/user_icon.svg"
                                            alt="user_icon" /></span>
                                    Patients
                                </h6>
                                <h6> <?php echo e($item->clinicPatinets->count()); ?> </h6>
                            </div>

                            <div class="d-flex justify-content-between">
                                <h6 class="mb-0">
                                    <span><img src="<?php echo e(asset('website')); ?>/assets/media/images/prescriptions.svg"
                                            alt="prescriptions" /></span>
                                    Prescriptions
                                </h6>
                                <h6> <?php echo e($item->clinicPrescriptions->count()); ?> </h6>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <p class="text-center">No registrations requests found</p>
                <?php endif; ?>
            </div>

        </section>
    </div>

</div>


<?php $__env->startPush('js'); ?>
    <script>
        var ctx3 = document.getElementById('months12-chart');
        var purple = '#8C3EE4';
        var transparent = '#e8d8fa';
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

        // Get last 12 months labels
        const months = [];
        for (let i = 11; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            months.push(date.toLocaleString('default', {
                month: 'short'
            }));
        }

        // Chart data
        const data3 = {
            labels: months,
            datasets: [{
                label: 'Monthly Sales',
                data: <?php echo json_encode($dashboardData['twelveMonthsData'], 15, 512) ?>,
                borderColor: purple,
                backgroundColor: transparent,
                fill: true,
                pointRadius: 0,
                pointHoverRadius: 2,
                lineTension: 0.5
            }]
        };

        // Chart config
        const config3 = {
            type: 'line',
            data: data3,
            options: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `£${value.toLocaleString()}`;
                            }
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false,
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                        },
                    },
                    y: {
                        grid: {
                            display: true,
                        },
                        ticks: {
                            display: false,
                        }
                    }
                }
            },
            defaults: {
                global: {
                    defaultFont: fontFamily
                }
            }
        };

        var myChart3 = new Chart(ctx3, config3);
    </script>

    <script>
        var ctx4 = document.getElementById('months6-chart');
        var purple = '#8C3EE4';
        var transparent = '#e8d8fa';
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

        // Get last 6 months labels
        const months6 = [];
        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            months6.push(date.toLocaleString('default', {
                month: 'short'
            }));
        }

        const data4 = {
            labels: months6,
            datasets: [{
                label: 'Monthly Sales',
                data: <?php echo json_encode($dashboardData['sixMonthsData'], 15, 512) ?>,
                borderColor: purple,
                backgroundColor: transparent,
                fill: true,
                pointRadius: 0,
                pointHoverRadius: 2,
                lineTension: 0.5
            }]
        };

        const config4 = {
            type: 'line',
            data: data4,
            options: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `£${value.toLocaleString()}`;
                            }
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false,
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                        },
                    },
                    y: {
                        grid: {
                            display: true,
                        },
                        ticks: {
                            display: false,
                        }
                    }
                }
            },
            defaults: {
                global: {
                    defaultFont: fontFamily
                }
            }
        };

        var myChart4 = new Chart(ctx4, config4);
    </script>

    <script>
        var ctx5 = document.getElementById('day30-chart');
        var purple = '#8C3EE4';
        var transparent = '#e8d8fa';
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

        // Get last 4 weeks labels
        const weeks = [];
        for (let i = 3; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - (i * 7));
            const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
            const weekEnd = new Date(date.setDate(date.getDate() + 6));
            weeks.push(
                `${weekStart.toLocaleDateString('default', { day: 'numeric', month: 'short' })} - ${weekEnd.toLocaleDateString('default', { day: 'numeric', month: 'short' })}`
                );
        }

        const data5 = {
            labels: weeks,
            datasets: [{
                label: 'Weekly Sales',
                data: <?php echo json_encode($dashboardData['thirtyDaysData'], 15, 512) ?>,
                borderColor: purple,
                backgroundColor: transparent,
                fill: true,
                pointRadius: 0,
                pointHoverRadius: 2,
                lineTension: 0.5
            }]
        };

        const config5 = {
            type: 'line',
            data: data5,
            options: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `£${value.toLocaleString()}`;
                            }
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false,
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                        },
                    },
                    y: {
                        grid: {
                            display: true,
                        },
                        ticks: {
                            display: false,
                        }
                    }
                }
            },
            defaults: {
                global: {
                    defaultFont: fontFamily
                }
            }
        };

        var myChart5 = new Chart(ctx5, config5);
    </script>

    <script>
        var ctx6 = document.getElementById('day7-chart');
        var purple = '#8C3EE4';
        var transparent = '#e8d8fa';
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

        // Get last 7 days labels
        const days = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            days.push(date.toLocaleDateString('default', {
                weekday: 'short',
                day: 'numeric'
            }));
        }

        const data6 = {
            labels: days,
            datasets: [{
                label: 'Daily Sales',
                data: <?php echo json_encode($dashboardData['sevenDaysData'], 15, 512) ?>,
                borderColor: purple,
                backgroundColor: transparent,
                fill: true,
                pointRadius: 0,
                pointHoverRadius: 2,
                lineTension: 0.5
            }]
        };

        const config6 = {
            type: 'line',
            data: data6,
            options: {
                plugins: {
                    legend: {
                        display: false,
                    },
                    title: {
                        display: false,
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                const value = context.raw;
                                return `£${value.toLocaleString()}`;
                            }
                        }
                    }
                },
                responsive: true,
                interaction: {
                    intersect: false,
                },
                scales: {
                    x: {
                        grid: {
                            display: false,
                        },
                    },
                    y: {
                        grid: {
                            display: true,
                        },
                        ticks: {
                            display: false,
                        }
                    }
                }
            },
            defaults: {
                global: {
                    defaultFont: fontFamily
                }
            }
        };

        var myChart6 = new Chart(ctx6, config6);
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\git\rx-direct\resources\views/dashboard/templates/index/admin_index.blade.php ENDPATH**/ ?>