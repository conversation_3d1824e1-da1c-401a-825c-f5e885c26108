@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css"/> -->
    <style>
        /* .select2-container--bootstrap-5 .select2-selection {
                        z-index: 1055;
                    }

                    .select2-dropdown {
                        z-index: 1055;
                    }

                    .select2-container--bootstrap-5 {
                        position: relative;
                        z-index: 1055;
                    }

                    .select2-container {
                        width: 100% !important;
                    }


            .select2-container--default .select2-search--dropdown .select2-search__field {
                border: 1px solid black;
                border-radius: 20px;
            }

            span.select2-dropdown.select2-dropdown--below {
                background: white;
                border-radius: 10px;
                color: black;
                box-shadow: 4px 4px 12px -4.5px black;
                margin-top: 7px;
            } */
    </style>
@endpush
<div class="modal fade" tabindex="-1" id="new-prescription-modal">
    <div class="modal-dialog new-prescription modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header ">
                <h4 id="modal-title" class="modal-title white-color inter w-100 text-center">Create new Prescription
                </h4>
            </div>
            <form id="prescription-form" method="POST" action="{{ route('prescriptions.store') }}">
                @csrf
                <input type="hidden" id="put_input" name="_method" value="PUT">
                <div class="modal-body prescription-modal pb-0">
                    <div class="row">
                        @if (auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Receptionist')
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Doctor</h5>
                                    <select data-control="select2" data-dropdown-css-class="w-200px" class="form-select"
                                        id="doctor_id" name="doctor_id" required>
                                        <option value="">Select Doctor</option>
                                        @foreach ($doctors ?? [] as $doctor)
                                            <option value="{{ $doctor->id }}">{{ $doctor->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        @endif
                        <div class="col-lg-12">
                            <div class="patients-name mb-7">
                                <label for="patient_id"
                                    class=" form-field-title fs-16 pt-5 my-3  fs-18 fw-500">Patient</label>
                                <select data-control="select2" name="patient_id" id="patient_id"
                                    class="form-select patient-select">
                                    <!-- Options will be loaded here dynamically -->
                                </select>
                            </div>
                        </div>
                        <div id="patient-details" class="col-lg-12 accordion mb-3" style="display:none;">
                            <div class="accordion-item">
                                <h2 class="accordion-header fs-18 fw-500" id="headingOne">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                        Patient Details
                                    </button>
                                </h2>
                                <div id="collapseOne" class="accordion-collapse collapse show"
                                    aria-labelledby="headingOne">
                                    <div class="accordion-body" id="patient-info-content">
                                        <!-- Populated via JS -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="medicines-container">
                        {{-- <div class="medicine-entry">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Medicine</h5>
                                        <select data-control="select2" data-dropdown-css-class="w-200px"
                                            class="form-select px-0 pt-0 select2Medicine"
                                            name="medicines[0][inventory_id]" required>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5">Dosage Instruction</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5">
                                            <input type="text" placeholder="Type Dosage" name="medicines[0][dosage]"
                                                required class="w-100 quantity-value fs-18 black-color" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 pack-size-display" style="display: none;">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                        <div class="pack-size-info p-3 bg-light rounded">
                                            <span class="pack-size-text fs-16 fw-500"></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5 border-bottom-0">
                                            <button type="button" class="quantity-btn decrease">−</button>
                                            <input type="text" placeholder="Add Quantity for the item"
                                                name="medicines[0][prescribed_units]" min="1" required
                                                value="1"
                                                class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                            <button type="button" class="quantity-btn increase">+</button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button"
                                    class="remove-medicine-btn mb-3 text-danger border-0 bg-transparent p-0"
                                    title="Remove"><i class="fas fa-trash-alt"></i></button>
                            </div>
                        </div> --}}
                    </div>
                    <div class="justify-content-center gap-3 border-0 pt-0 float-end">
                        <button type="button" class="gradient_modal_approve white-color roboto fs-14 fw-400"
                            id="add-medicine">Add Medicine</button>
                    </div>



                    <div class="prescription-type mb-7 mt-3">
                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Prescription Type</h5>
                        <select data-control="select2" data-hide-search="true" class="form-select px-0 pt-0"
                            name="prescription_type" id="prescription_type">
                            <option value="one_time" selected>One time</option>
                            <option value="repeat">Repeat</option>
                        </select>
                    </div>
                   
                    <div class="prescription-duration mb-7" id="prescriptionDuration" style="display: none;">
                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Prescription Duration</h5>

                        <select class="form-select p-2" style="width: 500px;" data-control="select2" name="repetitions"
                            id="duration" data-placeholder="Select an option">
                            @for ($i = 1; $i <= 12; $i++)
                                <option value="{{ $i }}">
                                    {{ $i }} Month</option>
                            @endfor
                        </select>


                        <!-- <select class="form-select p-2" name="repetitions" id="duration" required>
                        <option disabled selected>Select Duration</option>
                        <option value="1">1 Month</option>
                        <option value="2">2 Month</option>
                        <option value="3">3 Month</option>
                        <option value="4">4 Month</option>
                        <option value="5">5 Month</option>
                        <option value="6">6 Month</option>
                        <option value="7">7 Month</option>
                        <option value="8">8 Month</option>
                        <option value="9">9 Month</option>
                        <option value="10">10 Month</option>
                        <option value="11">11 Month</option>
                        <option value="12">12 Month</option>
                    </select> -->
                    </div>
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Prescription Notes<span
                                    class="ms-2 input-gary">(optional)</span></h5>
                            <input type="text" id="prescription" name="prescription"
                                placeholder="Type Instructions" class="w-100  fs-18 black-color" />
                        </div>
                    </div>
                    <div class="col-lg-12 clinic-name mb-7">
                        <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Billed Amount</h5>
                        <div class="cashback input-group mb-5">
                            <div class="w-100 d-flex flex-row">
                                <span class="input-group-text" id="basic-addon1">£</span>
                                <input type="text" class="form-control" id="totalPrice"
                                    placeholder="Enter the percentage you want to pay the prescriber"
                                    aria-label="Username" aria-describedby="basic-addon1" disabled />
                            </div>

                        </div>
                    </div>

                </div>
                <div class="modal-footer justify-content-center gap-3 border-0 pt-0">
                    <button type="button" class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="gradient_modal_approve white-color roboto fs-14 fw-400"
                        id="save_prescription">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('js')
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->

    <script>
        $(document).ready(function() {
            // Attach the click event to the submit button
            $('#save_prescription').click(function(event) {
                event.preventDefault(); // Prevent the default form submission
                var $button = $(this); // Reference to the button
                $button.prop('disabled', true); // Disable the button

                var formData = $('#prescription-form').serialize(); // Serialize the form data

                // Make the AJAX request
                $.ajax({
                    url: $('#prescription-form').attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Redirect to prescription summary page
                            window.location.href = "{{ route('add_prescription') }}/" + response
                                .order_id;
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message ||
                                    'Something went wrong. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(xhr) {
                        // If validation errors are returned, show them in the modal and in SweetAlert
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            showValidationErrors(
                                errors); // Show errors below the input fields in the form
                            showSweetAlertErrors(errors); // Show errors in SweetAlert
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    complete: function() {
                        $button.prop('disabled',
                            false); // Re-enable the button after request completes
                    }
                });
            });

            // Function to display validation errors in the form
            function showValidationErrors(errors) {
                // Clear any previous error messages
                $('.form-group .alert').remove();

                // Loop through the errors and display them in the form
                $.each(errors, function(field, messages) {
                    // Find the corresponding input field and show the error message
                    var inputField = $('[name="' + field + '"]');
                    var errorMessage = '<div class="alert alert-danger">' + messages.join('<br>') +
                        '</div>';

                    // Show the error message below the input field
                    inputField.closest('.form-group').append(errorMessage);
                });
            }

            // Function to display validation errors in SweetAlert
            function showSweetAlertErrors(errors) {
                var errorMessages = [];

                // Loop through the errors and create a list of error messages for SweetAlert
                $.each(errors, function(field, messages) {
                    errorMessages.push(messages.join('<br>'));
                });

                // Show SweetAlert with the error messages
                Swal.fire({
                    title: 'Validation Errors',
                    icon: 'error',
                    html: errorMessages.join('<br><br>'),
                    confirmButtonText: 'OK'
                });
            }
        });
    </script>
    <script>
        // Function to display validation errors in the modal
        function showValidationErrors(errors) {
            // Loop through the errors and display them
            $.each(errors, function(field, messages) {
                // Find the corresponding input field and show the error message
                var inputField = $('[name="' + field + '"]');
                var errorMessage = '<div class="alert alert-danger">' + messages.join('<br>') + '</div>';

                // Show the error message below the input field
                inputField.closest('.form-group').append(errorMessage);
            });
        }

        function sanitizeInput(event) {
            const input = event.target;
            let value = parseInt(input.value, 10);

            if (isNaN(value) || value < 0) {
                value = 0;
            }

            input.value = value;
        }

        function increaseQuantity(button) {

            const input = $(button).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;

            input.val(value + 1).trigger('change');
            updateTotalPrice();
        }

        function decreaseQuantity(button) {
            const input = $(button).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;
            input.val(value > 1 ? value - 1 : 1).trigger('change');
            updateTotalPrice();
        }

        document.addEventListener("DOMContentLoaded", function() {
            const inputs = document.querySelectorAll(".quantityInput");
            inputs.forEach(input => {
                input.addEventListener("input", sanitizeInput);
            });

            // Update initial medicine entry buttons
            const initialMedicineEntry = document.querySelector('.medicine-entry');
            if (initialMedicineEntry) {
                const quantityContainer = initialMedicineEntry.querySelector('.quantity-container');
                if (quantityContainer) {
                    quantityContainer.innerHTML = `
                        <button type="button" class="quantity-btn decrease">−</button>
                        <input type="text" placeholder="Add Quantity for the item"
                            name="medicines[0][prescribed_units]" min="1" required
                            value="1"
                            class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                        <button type="button" class="quantity-btn increase">+</button>
                    `;
                }
            }
        });
    </script>
    <script>
        // let medicineCount = document.getElementById('medicines-container').children.length + 1;
        let medicineCount = 1000;

        document.getElementById('add-medicine').addEventListener('click', function() {
            const medicineContainer = document.getElementById('medicines-container');
            const newMedicineEntry = document.createElement('div');
            newMedicineEntry.classList.add('medicine-entry');

            newMedicineEntry.innerHTML = `
                <div class="row">
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <div class="d-flex justify-content-between mx-3">
                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Medicine</h5>
                                <button type="button" class="remove-medicine-btn mb-3 text-danger border-0 bg-transparent p-0" title="Remove"><i class="fas fa-trash-alt" style="color:red;"></i></button>
                            </div>
                            <select class="form-select px-0 pt-0 select2Medicine" name="medicines[${medicineCount}][inventory_id]" required>
                                <option value="">Select Medicine</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Dosage Instruction</h5>
                            <div class="quantity-container d-flex align-items-center gap-5">
                                <input type="text" placeholder="Type Dosage"
                                    name="medicines[${medicineCount}][dosage]" required
                                    class="w-100 quantity-value fs-18 black-color" />
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 pack-size-display" style="display: none;">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                            <div class="pack-size-info p-3 bg-light rounded">
                                <span class="pack-size-text fs-16 fw-500"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                            <div class="quantity-container d-flex align-items-center gap-5 border-bottom-0">
                                <button type="button" class="quantity-btn decrease">−</button>
                                <input type="text" placeholder="Add Quantity for the item" value='1'
                                    name="medicines[${medicineCount}][prescribed_units]" min="1" required
                                    class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                <button type="button" class="quantity-btn increase">+</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            medicineContainer.appendChild(newMedicineEntry);
            medicineCount++;

            // Initialize select2 after adding to DOM
            setTimeout(function() {
                initializeMedicineSelect();
            }, 0);
        });

        function initializeMedicineSelect(index, medicine) {
            const selectElement = $('.select2Medicine').last();

            // Function to get all currently selected medicine IDs except the current select
            function getSelectedMedicineIds() {
                const selectedIds = [];
                $('.select2Medicine').each(function() {
                    const $select = $(this);
                    // Skip the current select element
                    if ($select[0] === selectElement[0]) return;

                    if ($select.data('select2')) {
                        const selectedData = $select.select2('data');
                        if (selectedData && selectedData.length > 0 && selectedData[0].id) {
                            selectedIds.push(selectedData[0].id);
                        }
                    }
                });
                return selectedIds;
            }

            // Initialize select2
            selectElement.select2({
                dropdownParent: $('#new-prescription-modal'),
                placeholder: 'Search for medicines',
                minimumInputLength: 0,
                focusInput: false,
                ajax: {
                    url: "{{ route('medicines.search') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        // Get all currently selected medicine IDs except the current select
                        const selectedIds = getSelectedMedicineIds();

                        return {
                            q: params.term, // search term
                            exclude_ids: selectedIds // send selected IDs to exclude
                        };
                    },
                    processResults: function(data) {
                        // Double check on client side to ensure no duplicates
                        const selectedIds = getSelectedMedicineIds();
                        const filteredData = data.filter(medicine => !selectedIds.includes(medicine.id));

                        return {
                            results: filteredData.map(medicine => ({
                                id: medicine.id,
                                text: medicine.name,
                                name: medicine.name,
                                sale_price: medicine.sale_price,
                                cost_price: medicine.cost_price,
                                pack_size: medicine.pack_size,
                                pip_code: medicine.pip_code,
                            }))
                        };
                    },
                    cache: true
                }
            });

            // Handle selection
            selectElement.on('select2:select', function(e) {
                const selectedId = e.params.data.id;
                const selectedIds = getSelectedMedicineIds();

                // Check if this medicine is already selected in another dropdown
                if (selectedIds.includes(selectedId)) {
                    e.preventDefault();
                    selectElement.val(null).trigger('change');
                    Swal.fire({
                        title: 'Error!',
                        text: 'This medicine is already selected in another entry.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Show pack size information
                const medicineEntry = selectElement.closest('.medicine-entry');
                const packSizeDisplay = medicineEntry.find('.pack-size-display');
                const packSizeText = medicineEntry.find('.pack-size-text');

                if (e.params.data.pack_size) {
                    packSizeText.text(`${e.params.data.pack_size}`);
                    packSizeDisplay.show();
                } else {
                    packSizeDisplay.hide();
                }

                updateTotalPrice();
            });

            // Handle unselect
            selectElement.on('select2:unselect', function() {
                // Hide pack size information
                const medicineEntry = selectElement.closest('.medicine-entry');
                const packSizeDisplay = medicineEntry.find('.pack-size-display');
                packSizeDisplay.hide();

                updateTotalPrice();
            });

            // Prevent programmatic value setting
            const originalVal = selectElement.val;
            selectElement.val = function(value) {
                if (value) {
                    const selectedIds = getSelectedMedicineIds();
                    if (selectedIds.includes(value)) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'This medicine is already selected in another entry.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        return selectElement;
                    }
                }
                return originalVal.apply(this, arguments);
            };
        }

        function updateTotalPrice() {
            let totalPrice = 0;
            let processedCount = 0;
            const totalMedicines = $('.select2Medicine').length;
            const minimumCharge = @json(App\Models\Setting::first()->minimum_charge ?? 14.95);

            // If no medicines exist, set total to zero
            if (totalMedicines === 0) {
                $('#totalPrice').val('0.00');
                return;
            }

            $('.select2Medicine').each(function() {
                const $select = $(this);

                // Check if select2 is initialized
                if (!$select.data('select2') || !$select.hasClass('select2-hidden-accessible')) {
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if medicines exist and have valid selections
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                    return;
                }

                try {
                    const selectedData = $select.select2('data')[0];
                    if (selectedData && selectedData.id) {
                        const quantity = parseInt($select.closest('.medicine-entry').find(
                            'input[name$="[prescribed_units]"]').val(), 10) || 1;
                        const cost_price = parseFloat(selectedData.cost_price) || 0;

                        // Calculate price per pack (cost_price * 2 for markup)
                        const sale_price_per_pack = cost_price * 2;
                        const medicinePrice = sale_price_per_pack * quantity;

                        totalPrice += medicinePrice;
                    }

                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if there are actual medicine selections with prices
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                } catch (error) {
                    console.warn('Select2 data access error:', error);
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if there are actual medicine selections with prices
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                }
            });
        }
        // Event listener for removing a medicine row
        $(document).on('click', '.remove-medicine-btn', function() {
            const medicineEntry = $(this).closest('.medicine-entry');
            const selectElement = medicineEntry.find('.select2Medicine');

            // Clear the selection before removing
            if (selectElement.data('select2')) {
                selectElement.val(null).trigger('change');
            }

            medicineEntry.remove();
            updateTotalPrice();
        });
    </script>
    <script>
        $(document).ready(function() {
            // Prescription type and duration handling
            const $typeSelect = $('#prescription_type');
            const $durationDiv = $('#prescriptionDuration');
            const $durationInput = $('#duration');

            function toggleDuration() {
                if ($typeSelect.val() === "repeat") {
                    $durationDiv.show();
                    $durationInput.prop('disabled', false);
                } else {
                    $durationDiv.hide();
                    $durationInput.prop('disabled', true);
                }
            }

            $typeSelect.on('change', toggleDuration);

            // Run once on load in case default is recurring
            toggleDuration();
            // Open the modal in Create Mode
            function openCreateModal() {
                $('#modal-title').text('Create new Prescription');
                $('#prescription-form').attr('action', '{{ route('prescriptions.store') }}'); // Create action route
                // $('#prescription-form')[0].reset(); // Reset the form for new prescription
                clearMedicineRows();
                $('.patient-select').empty();
                populatePatientDropdown();
                $('#totalPrice').val('00.0');
                $('#put_input').prop('disabled', true);

                // Add initial medicine entry
                // addInitialMedicineEntry();

                $('#new-prescription-modal').modal('show');
            }

            // Function to add initial medicine entry
            function addInitialMedicineEntry() {
                const medicineRow = `
                    <div class="medicine-entry">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Medicine</h5>
                                    <select data-control="select2" data-dropdown-css-class="w-200px"
                                        class="form-select px-0 pt-0 select2Medicine"
                                        name="medicines[0][inventory_id]" required>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Dosage Instruction</h5>
                                    <div class="quantity-container d-flex align-items-center gap-5">
                                        <input type="text" placeholder="Type Dosage" name="medicines[0][dosage]"
                                            required class="w-100 quantity-value fs-18 black-color" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 pack-size-display" style="display: none;">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                    <div class="pack-size-info p-3 bg-light rounded">
                                        <span class="pack-size-text fs-16 fw-500"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                    <div class="quantity-container d-flex align-items-center gap-5 border-bottom-0">
                                        <button type="button" class="quantity-btn decrease">−</button>
                                        <input type="text" placeholder="Add Quantity for the item"
                                            name="medicines[0][prescribed_units]" min="1" required
                                            value="1"
                                            class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                        <button type="button" class="quantity-btn increase">+</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button"
                                class="remove-medicine-btn mb-3 text-danger border-0 bg-transparent p-0"
                                title="Remove"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                `;
                $('#medicines-container').append(medicineRow);

                // Initialize select2 for the initial medicine entry
                setTimeout(function() {
                    initializeMedicineSelect();
                }, 0);
            }

            // Open the modal in Edit Mode and populate with existing data
            function openEditModal(prescriptionData) {
                clearMedicineRows();
                $('#modal-title').text('Edit Prescription');
                $('#prescription-form').attr('action', '{{ route('prescriptions.update', '') }}/' + prescriptionData
                    .id); // Edit action route
                $('#prescription').val(prescriptionData.prescription); // Example field for prescription notes
                const selectedOption =
                    `<option value="${prescriptionData.patient_id}" selected="selected">${prescriptionData.patient_name} (${prescriptionData.patient_email})</option>`;

                // Append the selected option to the dropdown
                $('.patient-select').append(selectedOption);
                populatePatientDropdown(); // Pass the patient_id to pre-select the patient
                $('#doctor_id').val(prescriptionData.doctor_id).trigger('change'); // Example doctor selection

                $('#prescription_type').val(prescriptionData.prescription_type).trigger('change');

                if (prescriptionData.prescription_type == 'repeat') {
                    $('#prescriptionDuration').show();
                    $('#duration').prop('disabled', false).val(prescriptionData.repetitions).trigger(
                        'change'); // Example doctor selection
                }
                populateMedicineRows(prescriptionData.medicines); // Populate medicines if in edit mode
                $('#put_input').prop('disabled', false);
                $('#new-prescription-modal').modal('show');
                updateTotalPrice();
            }

            function populatePatientDropdown() {
                setTimeout(function() {
                    $('.patient-select').select2({
                        dropdownParent: $('#new-prescription-modal'),
                        placeholder: 'Search for patients', // Placeholder text
                        minimumInputLength: 0, // Minimum input length to start searching
                        focusInput: false, // Disable focus to prevent aria-hidden issues
                        ajax: {
                            url: "{{ route('patients.search') }}", // URL to fetch data
                            dataType: 'json', // Expected data format
                            delay: 250, // Delay before requesting the data
                            data: function(params) {
                                return {
                                    q: params.term // Send search term to the backend
                                };
                            },
                            processResults: function(data) {
                                return {
                                    results: data.map(patient => ({
                                        id: patient.id,
                                        text: `${patient.name} (${patient.email})`, // Show name and email in dropdown
                                        name: patient.name,
                                        email: patient.email,
                                        age: patient.age,
                                        gender: patient.gender,
                                        phone: patient.phone,
                                        address: patient.address
                                    }))
                                };
                            },
                            cache: true // Cache results for better performance
                        }
                    }); // Ensure the selected patient is set and reflected
                }, 100);
            }

            $('#patient_id').on('select2:select', function(e) {
                const patient = e.params.data; // Get the selected patient's data
                $('#patient-info-content').html(`
                    <p><strong>Name:</strong> ${patient.name}</p>
                    <p><strong>Email:</strong> ${patient.email}</p>
                    <p><strong>Age:</strong> ${patient.age ?? 'Outsourced'}</p>
                    <p><strong>Gender:</strong> ${patient.gender ?? 'Outsourced'}</p>
                    <p><strong>Phone:</strong> ${patient.phone ?? 'Outsourced'}</p>
                    <p><strong>Address:</strong> ${patient.address ?? 'Outsourced'}</p>
                `);
                $('#patient-details').show(); // Show patient details section
            });

            // Function to clear the medicine rows
            function clearMedicineRows() {
                $('#medicines-container').empty(); // Clear all medicine rows
            }

            // Function to populate the medicine rows for editing
            function populateMedicineRows(medicines) {
                medicines.forEach((medicine, index) => {
                    const medicineRow = `
                        <div class="medicine-entry">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5">Medicine</h5>
                                        <select class="form-select px-0 pt-0 select2Medicine" name="medicines[${index}][inventory_id]" required>
                                            <option value="${medicine.inventory_id}" selected="selected">${medicine.name}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5">Dosage Instruction</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5">
                                            <input type="text" placeholder="Type Dosage"
                                                name="medicines[${index}][dosage]" value="${medicine.dosage}" required
                                                class="w-100 quantity-value fs-18 black-color" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 pack-size-display">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                        <div class="pack-size-info p-3 bg-light rounded">
                                            <span class="pack-size-text fs-16 fw-500">${medicine.pack_size}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5">
                                            <button type="button" class="quantity-btn decrease">−</button>
                                            <input type="text" placeholder="Add Quantity for the item" value='${medicine.prescribed_units}'
                                                name="medicines[${index}][prescribed_units]" min="1" required
                                                class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                            <button type="button" class="quantity-btn increase">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#medicines-container').append(medicineRow);
                    initializeMedicineSelect();

                    // Pre-select the medicine in select2 and populate other fields
                    const selectedOption = {
                        id: medicine.inventory_id, // Assigning the inventory_id as id
                        text: medicine.name, // Name of the medicine
                        name: medicine.name,
                        sale_price: medicine.sale_price,
                        cost_price: medicine.cost_price,
                        pack_size: medicine.pack_size,
                        pip_code: medicine.pip_code,
                    };

                    // Find the newly created select2 element and set its value
                    const medicineSelect = $('.select2Medicine').last();
                    medicineSelect.val(selectedOption.id).trigger('change');
                });
            }

            // Open modal for creating a prescription
            $('#add-new-prescription').on('click', function() {
                openCreateModal();
            });

            // Open modal for editing a prescription (pass prescription data)
            $('#edit-prescription').on('click', function() {
                const prescriptionId = $(this).data('id'); // dynamically set this based on your use case
                const url = "{{ url('/get-prescriptions') }}/" + prescriptionId

                $.ajax({
                    url: url,
                    method: 'GET',
                    success: function(response) {
                        openEditModal(response);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error fetching prescription data:', error);
                    }
                });
            });

            // Event delegation for quantity buttons
            $(document).on('click', '.quantity-btn.increase', function(e) {
                e.preventDefault();
                increaseQuantity(this);
            });

            $(document).on('click', '.quantity-btn.decrease', function(e) {
                e.preventDefault();
                decreaseQuantity(this);
            });

            // Event delegation for quantity input changes
            $(document).on('change', '.quantityInput', function() {
                updateTotalPrice();
            });
        });
    </script>
@endpush
