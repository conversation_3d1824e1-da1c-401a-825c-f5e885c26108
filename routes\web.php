<?php

use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\CmsController;
use App\Http\Controllers\ContactsController;
use App\Http\Controllers\SubscribersController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ThemeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CrudGeneratorController;
use App\Http\Controllers\InventoriesController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PrescriptionsController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\QrCodeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\SubscribeController;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\WebsiteController;
use Illuminate\Support\Facades\Artisan;

// Developer Routes - Full system access
Route::group(['middleware' => ['auth', 'role:developer',]], function () {
    Route::resource("settings", "\App\Http\Controllers\SettingsController");
    Route::resource('roles', RoleController::class);
    Route::resource('users', UserController::class);
    Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
    Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');
});

// Admin Routes - System administration
Route::group(['middleware' => ['auth', 'role:admin', 'check.status']], function () {

    Route::get('/admin-user-requests', [ThemeController::class, 'userRequests'])->name('user.requests');
    Route::get('admin-users-detail/{id?}', [ThemeController::class, 'userDetails'])->name('users_details');
    Route::get('status/filter', [ReportController::class, 'userDetailFilter'])->name('user.details.filter');
    Route::post('/user-requests-status', [ThemeController::class, 'userRequestsStatus'])->name('user.requests.status');

    Route::get('/orders', [ThemeController::class, 'userOrders'])->name('orders');
    Route::get('/admin-users', [ThemeController::class, 'user'])->name('admin.users');
    Route::get('/prescription-requests', [ThemeController::class, 'prescriptionRequestIndex'])->name('prescription-requests');
    Route::get('/prescription-requests-details', [ThemeController::class, 'prescriptionRequestDetails'])->name('prescription-requests-details');
    Route::post('/inventories/import', [InventoriesController::class, 'import'])->name('inventories.import');
    Route::post('/inventories/search', [InventoriesController::class, 'search'])->name('inventories.search');
    Route::get('/subscriptions', [ThemeController::class, 'subscriptions'])->name('subscriptions');
    Route::resource('contacts', ContactsController::class);
    Route::resource('subscribers', SubscribersController::class);
    Route::post('admin-approve-orders', [PrescriptionsController::class, 'approveOrders'])->name('approve_orders');
    Route::post('change_user_details', [UserController::class, 'changeUserDetails'])->name('change.user.details');
    Route::get('change_status/{status?}', [ThemeController::class, 'changeStatus'])->name('change_status');
    Route::get('/social-media', [ThemeController::class, 'socialMedia'])->name('social.media');
    Route::patch('/social-media', [ThemeController::class, 'editSocialMedia'])->name('social.media.update');
    Route::post('privacy-policy-cms', [CmsController::class, 'storePrivacyPolicy'])->name('privacy_policy.store');
    Route::get('shipment/create', [ShipmentController::class, 'createShipment']);
    Route::get('shipment/{shipmentId}/print-label', [ShipmentController::class, 'printLabelForShipment']);

    Route::post('/create-shipment/{orderId?}/{scheduleId?}', [ShipmentController::class, 'createAndPrintShipment'])->name('shipment.create');
    Route::get('/user_details', [ThemeController::class, 'userDetails'])->name('user_details');
    Route::get('generatePdf', [PrescriptionsController::class, 'generatePdf']);


    Route::post('/subscription/search', [ThemeController::class, 'searchSubscription'])->name('subscription.search');

    Route::put('/update-prescriptions-schedule/{id}', [PrescriptionsController::class, 'updatePrescriptionSchedule'])->name('update.schedule');

    Route::get('/admin/invoices/{id}/download-medicines', [ThemeController::class, 'downloadMedicinesCSV'])->name('admin.invoice.download.medicines');
    Route::get('/admin/invoices/{id}/download-medicines-pdf', [ThemeController::class, 'downloadMedicinesPDF'])->name('admin.invoice.download.medicines.pdf');
    Route::get('/admin/invoices/{id}/download-prescriptions', [ThemeController::class, 'downloadPrescriptionsCSV'])->name('admin.invoice.download.prescriptions');
    Route::get('/download-contacts', [ContactsController::class, 'download'])->name('download.contacts');
    // CMS Routes for Admin
    Route::get('/cms', [CmsController::class, 'cms'])->name('cms');
    Route::get('/home-cms', [CmsController::class, 'homeCms'])->name('home_cms');
    Route::get('/order-terms-cms', [CmsController::class, 'ordersTerms'])->name('orders.terms');
    Route::post('/order-terms-cms', [CmsController::class, 'storeordersTerms'])->name('orders.terms.store');
    Route::get('/terms-conditions-cms', [CmsController::class, 'termsConditions'])->name('terms.conditions.cms');
    Route::post('/terms-conditions-cms', [CmsController::class, 'storeTermsConditions'])->name('terms.conditions.store');
    Route::get('/complaints-cms', [CmsController::class, 'complaints'])->name('complaints.cms');
    Route::post('/complaints-cms', [CmsController::class, 'storeComplaints'])->name('complaints.store');
    Route::post('/home-cms', [CmsController::class, 'homeCmsSubmit'])->name('home_cms');
    Route::get('/privacy-policy-cms', [CmsController::class, 'privacyPolicy'])->name('privacy.policy');
    Route::get('/regulatory-information-cms', [CmsController::class, 'regulatoryInformation'])->name('regulatory.information');
    Route::post('/regulatory-information-cms', [CmsController::class, 'storeRegulatoryInformation'])->name('regulatory.information.store');
    Route::get('/contact-cms', [CmsController::class, 'contactCms'])->name('contact_cms');
    Route::post('/contact-cms', [CmsController::class, 'contactCmsSubmit'])->name('contact_cms');
    Route::get('/about-cms', [CmsController::class, 'aboutCms'])->name('about_cms');
    Route::post('/about-cms', [CmsController::class, 'aboutCmsSubmit'])->name('about_cms');
    Route::get('/why-us-cms', [CmsController::class, 'whyusCms'])->name('whyus_cms');
    Route::post('/why-us-cms', [CmsController::class, 'whyusCmsSubmit'])->name('whyus_cms');
    Route::get('/prescribers-cms', [CmsController::class, 'prescribersCms'])->name('prescribers_cms');
    Route::post('/prescribers-cms', [CmsController::class, 'prescribersCmsSubmit'])->name('prescribers_cms');
    Route::get('/our-patients-cms', [CmsController::class, 'OurPatientsCms'])->name('our_patients');
    Route::post('/our-patients-cms', [CmsController::class, 'OurPatientsCmsSubmit'])->name('our_patients');
    Route::get('/prescription/pdf/{orderId?}', [PrescriptionsController::class, 'generatePrescriptionPdf'])->name('generate-prescription-pdf');
});

// Clinic Admin Routes - Clinic management
Route::group(['middleware' => ['auth', 'role:clinic_admin', 'check.status']], function () {
    Route::get('/clinic-staff', [ThemeController::class, 'clinicUser'])->name('clinic.staff.list');
    Route::get('/clinic-staff-details/{id}', [ThemeController::class, 'adminClinicUserDetails'])->name('clinic.staff.detail');
    Route::get('/clinic-request', [ThemeController::class, 'clinicRequest'])->name('clinic.request');
    Route::post('/create-staff', [UserController::class, 'createStaff'])->name('create.staff');

    Route::get('get-staff-list', [UserController::class, 'getStaff'])->name('staff.list');
    Route::get('get-patient-request', [UserController::class, 'getClinicPatient'])->name('clinic.patient');
    Route::get('get-prescription-request', [PrescriptionsController::class, 'getClinicprescription'])->name('clinic.prescription');
    Route::get('/clinic-transaction', [PrescriptionsController::class, 'clinicTransactions'])->name('clinic.transaction');
    Route::get('get-clinic-transactions', [PrescriptionsController::class, 'getClinicprescriptionByType'])->name('clinic.transactions');
    Route::get('/search-staff-details', [ThemeController::class, 'searchClinicStaff'])->name('search.clinic.staff');
    Route::get('/clinic-staff/{slug}/toggle-status', [UserController::class, 'toggleStatus'])->name('clinic.staff.toggle.status');
});

// Doctor Routes (Individual doctors)
Route::group(['middleware' => ['auth', 'role:doctor', 'check.status']], function () {
    Route::get('/prescriptions-approval', [PrescriptionsController::class, 'prescriptionsApproval'])->name('prescriptions.approval');
});

// Staff Routes (Receptionist and clinic doctors)
Route::group(['middleware' => ['auth', 'role:staff', 'check.status']], function () {
    Route::get('prescription/prescription/filter', [ReportController::class, 'patientPrescriptionFilter'])->name('patient.prescription.filter');
    Route::get('/patients_receptionist', [ThemeController::class, 'patientsReceptionist'])->name('patients-r');
    Route::get('/prescriptions_receptionist', [ThemeController::class, 'prescriptionsReceptionist'])->name('prescriptions-rece');
    Route::get('/prescriptions-approval', [PrescriptionsController::class, 'prescriptionsApproval'])->name('prescriptions.approval');
});

// Patient Routes
Route::group(['middleware' => ['auth', 'role:patients', 'check.status']], function () {
    Route::get('shipment/{consignmentNumber}/track', [ShipmentController::class, 'trackShipment'])->name('shipment.track');
    Route::get('/patient-orders', [PrescriptionsController::class, 'ordersPatient'])->name('orders.patient');
    Route::get('/get-patient-orders', [PrescriptionsController::class, 'getPatientOrders'])->name('get.patient.orders');
    Route::get('/order-details/{id}', [PrescriptionsController::class, 'orderDetails'])->name('order-details');
    Route::get('/create-onboarding-link', [StripeController::class, 'createOnboardingLink'])->name('stripe.onboarding.create');
    Route::get('/onboarding-complete', [StripeController::class, 'onboardingComplete'])->name('stripe.onboarding.complete');
    Route::get('/onboarding-cancel', [StripeController::class, 'onboardingCancel'])->name('stripe.onboarding.cancel');
    Route::get('checkout/create', [StripeController::class, 'createCheckoutSession'])->name('checkout.create');
    Route::get('checkout/success', [StripeController::class, 'checkoutSuccess'])->name('checkout.success');
    Route::get('checkout/cancel', [StripeController::class, 'checkoutCancel'])->name('checkout.cancel');
});

// Common routes for Admin, Clinic Admin, and Staff (inventory management)
Route::group(['middleware' => ['auth', 'role:admin|clinic_admin|staff', 'check.status']], function () {
    Route::get('/prescriptions/{slug}/toggle-status', [PrescriptionsController::class, 'toggleStatus'])->name('prescriptions.toggle.status');
    Route::get('/report', [ThemeController::class, 'report'])->name('report');

});
//all
Route::group(['middleware' => ['auth', 'role:admin|clinic_admin|staff|doctor', 'check.status']], function () {
    Route::get('/patients-prescription/{id}', [ThemeController::class, 'patientPrescription'])->name('patients-view-details');
    Route::get('/prescription-details/{id}', [PrescriptionsController::class, 'prescriptionRequestDetails'])->name('prescription-details');
    Route::get('/medicine/search', [InventoriesController::class, 'searchMedicines'])->name('medicines.search');
    Route::get('export/csv/{type?}', [ReportController::class, 'exportCsv'])->name('report.export.csv');
    Route::get('report/status/filter', [ReportController::class, 'statusFilter'])->name('report.status.filter');
    Route::get('/get-staff-prescriptions', [PrescriptionsController::class, 'getStaffPrescriptions'])->name('get.staff.prescriptions');
    Route::get('/admin-invoices-details/{id?}', [ThemeController::class, 'adminInvoicesDetails'])->name('admin-invoices-details');

});
Route::group(['middleware' => ['auth', 'role:admin|staff|doctor', 'check.status']], function () {
    Route::get('/get-prescriptions/{id}', [PrescriptionsController::class, 'getPrescriptionData'])->name('getPrescriptionData');
});
// Common routes for doctor and staff
Route::group(['middleware' => ['auth', 'role:staff|doctor', 'check.status']], function () {
    Route::get('/get-medicine-details/{id}', [InventoriesController::class, 'getMedicineDetails'])->name('getMedicineDetails');

    Route::get('/get-staff-patients', [UserController::class, 'getStaffPatients'])->name('get.staff.patients');
    Route::get('/patients/search', [UserController::class, 'searchPatients'])->name('patients.search');
    Route::get('/patients', [ThemeController::class, 'patients'])->name('patients');
    Route::post('/create-patient', [UserController::class, 'createPatient'])->name('create.patient');
    Route::resource('prescriptions', PrescriptionsController::class);
    Route::get('/add-prescription/{id?}', [ThemeController::class, 'addPrescription'])->name('add_prescription');

});

// Common routes for Admin and Clinic Admin
Route::group(['middleware' => ['auth', 'role:admin|clinic_admin', 'check.status']], function () {
    Route::get('clinic/patient/filter', [ThemeController::class, 'clinicPatientFilter'])->name('clinic.patient.filter');
    Route::get('contact/status/update/{id?}', [ContactsController::class, 'contactStatusUpdate'])->name('contact.status.update');
    Route::post('contact/search/filter', [ContactsController::class, 'contactSearchFilter'])->name('contact.search.filter');
    Route::get('/download-subscribers', [SubscribersController::class, 'download'])->name('download.subscribers');
    Route::get('/download-prescription', [SubscribersController::class, 'downloadPrescription'])->name('download_all_prescription');
    Route::get('clinic/staff/filter', [ThemeController::class, 'clinicStaffFilter'])->name('clinic.staff.filter');
});

Route::group(['middleware' => ['auth', 'role:admin|patients', 'check.status']], function () {
    Route::get('shipment/{consignmentNumber}/track', [ShipmentController::class, 'trackShipment'])->name('shipment.track');
});

// Common Authenticated Routes (All logged-in users)
Route::group(['middleware' => ['auth', 'check.status']], function () {


    Route::get('/home', [ThemeController::class, 'dashboard'])->name('home');
    Route::get('/notification', [ThemeController::class, 'notification'])->name('notification');
    Route::get('/prescriber-profile/{id?}', [ProfileController::class, 'prescriberProfile'])->name('prescriber_profile');
    Route::get('/profile-setting', [ProfileController::class, 'profileSetting'])->name('profile_setting');
    Route::post('/profile-setting', [ProfileController::class, 'profileSettingUpdate'])->name('profile_setting');
    Route::post('/update-avatar', [ProfileController::class, 'avatarUpdate'])->name('update_avatar');
    Route::post('/password-update', [ProfileController::class, 'passwordUpdate'])->name('password_update');
    Route::get('/analytics', [ThemeController::class, 'analyticsAdmin'])->name('analytics');
    Route::get('/invoice', [InvoiceController::class, 'adminInvoice'])->name('invoice');

    Route::resource('inventories', InventoriesController::class);
});

// Stripe Routes (for doctors and clinic admins)
Route::group(['middleware' => ['auth', 'role:doctor|clinic_admin|admin']], function () {

    Route::post('/bulk-import', [UserController::class, 'bulkImport'])->name('bulk.import');
    Route::get('/fetch-invoice', [InvoiceController::class, 'fetchInvoices'])->name('fetch.invoices');
});
// Website Public Pages
Route::get('about-us', [WebsiteController::class, 'aboutUs'])->name('about-us');
Route::get('why-us', [WebsiteController::class, 'whyUs'])->name('why-us');
Route::get('prescribers', [WebsiteController::class, 'prescribers'])->name('prescribers');
Route::get('our-patients', [WebsiteController::class, 'patients'])->name('our.patients');
Route::get('contact-us', [WebsiteController::class, 'contactUs'])->name('contact-us');
Route::post('contact-us', [WebsiteController::class, 'contactUsPost'])->name('contact_us');
Route::get('terms-and-conditions', [WebsiteController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('privacy-policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('regulatory-information', [WebsiteController::class, 'regulatoryInformation'])->name('regulatory-information');
Route::get('orders-and-returns', [WebsiteController::class, 'ordersAndReturns'])->name('orders-and-returns');
Route::get('complaints', [WebsiteController::class, 'complaints'])->name('complaints');
Route::get('faqs', [WebsiteController::class, 'faqs'])->name('faqs');
// Cron/System Routes (No authentication - for system tasks)
Route::get('/generate-invoice', [InvoiceController::class, 'generateInvoice'])->name('generate.invoices');
Route::get('/update-invoice-status', [InvoiceController::class, 'updateInvoiceStatus'])->name('update.invoice.status');
Route::get('/get-overview-data', [InvoiceController::class, 'getOverviewData'])->name('get.overview.data');
// Public Routes (No authentication required)
Route::get('/clear-all', [WebsiteController::class, 'clearAll']);
Route::get('/', [WebsiteController::class, 'index'])->name('/');
Route::get('/logout', [WebsiteController::class, 'logout']);
// Auth::routes();

// Auth routes (excluding login routes since we have custom ones)
Auth::routes(['login' => false]);


// Registration and OTP Routes
Route::post('/store-session', [RegisterController::class, 'storeSession'])->name('registration_session');
Route::post('/verify-otp', [RegisterController::class, 'verifyOtp'])->name('verify_otp');
Route::get('/registration-instruction', [ThemeController::class, 'registrationInstruction'])->name('registration-instruction');
Route::post('/check-email', [App\Http\Controllers\Auth\RegisterController::class, 'checkEmail'])->name('check.email');

// // Role-specific login routes
// Route::get('login/{role}', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('role.login');
// Route::post('login/{role}', [App\Http\Controllers\Auth\LoginController::class, 'login'])->name('role.login.submit');

// // Redirect root login to role selection
// Route::get('login', function () {
//     return view('auth.role-select');
// })->name('login');

// Separate login routes for each role
Route::get('admin/login', [App\Http\Controllers\Auth\LoginController::class, 'showAdminLoginForm'])->name('admin.login');
Route::post('admin/login', [App\Http\Controllers\Auth\LoginController::class, 'adminLogin'])->name('admin.login.submit');

Route::get('clinic/login', [App\Http\Controllers\Auth\LoginController::class, 'showClinicAdminLoginForm'])->name('clinic.login');
Route::post('clinic/login', [App\Http\Controllers\Auth\LoginController::class, 'clinicAdminLogin'])->name('clinic.login.submit');

Route::get('doctor/login', [App\Http\Controllers\Auth\LoginController::class, 'showDoctorLoginForm'])->name('doctor.login');
Route::post('doctor/login', [App\Http\Controllers\Auth\LoginController::class, 'doctorLogin'])->name('doctor.login.submit');

Route::get('patient/login', [App\Http\Controllers\Auth\LoginController::class, 'showPatientLoginForm'])->name('patient.login');
Route::post('patient/login', [App\Http\Controllers\Auth\LoginController::class, 'patientLogin'])->name('patient.login.submit');

Route::get('staff/login', [App\Http\Controllers\Auth\LoginController::class, 'showStaffLoginForm'])->name('staff.login');
Route::post('staff/login', [App\Http\Controllers\Auth\LoginController::class, 'staffLogin'])->name('staff.login.submit');
