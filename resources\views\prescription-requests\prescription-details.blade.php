@extends('theme.layout.master')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="d-flex justify-content-between align-items-center my-5">
                <button id="backButton" class="button-gradient white-color roboto fs-14 fw-400">Back</button>
                @if (auth()->user()->hasRole('admin') &&
                        $prescription->prescription_type == 'one_time' &&
                        $prescription->is_dispensed == 0 &&
                        $prescription->admin_approval == 1 &&
                        ($prescription->type != 'Type 1' || ($prescription->type == 'Type 1' && $prescription->status == 1)))
                    <button class="button-gradient white-color roboto fs-14 fw-400 create-shipment-btn"
                        data-order-id="{{ $prescription->order_id }}">
                        Print
                    </button>
                @endif
            </div>
            <div class="row my-5">
                <div class="col-lg-9">
                    <div class="table-patients table-border-all my-5">
                        <div
                            class="custom-dropdown px-5 roboto d-flex justify-content-between align-items-center pb-5 ps-2">
                            <h3>Medicines
                                <!-- <span class="input-text-gray">({{ $inventories->count() ?? 0 }})</span> -->
                            </h3>
                            @if ((auth()->user()->hasRole('doctor') || auth()->user()->hasRole('staff')) && $prescription->admin_approval == 0)
                                <div>
                                    {{-- <button type="button" class="medicines-edit-btn roboto modal-save"
                                        id="edit-prescription" data-id="{{ $prescription->order_id }}">Edit
                                    </button> --}}
                                    <a type="button" class="roboto modal-save"
                                        href="{{ route('add_prescription', $prescription->order_id) }}">Edit
                                    </a>
                                </div>
                            @endif

                        </div>
                        <div class="table-responsive">
                            <table id="staffPrescritionTable" class="table display custom-table gy-5 gs-5">
                                <thead>
                                    <tr>
                                        <th class="min-w-200px">Medicine</th>
                                        <th class="min-w-200px">Instruction to patients</th>
                                        {{-- <th class="w-200px">Type</th> --}}
                                        <th class="min-w-200px">Qty</th>
                                        <th class="min-w-200px">Price</th>

                                    </tr>
                                </thead>
                                <tbody class="ps-5">
                                    @foreach ($inventories as $itemlist)
                                        <tr>
                                            <td class="min-w-200px">{{ $itemlist->name ?? '' }}</td>
                                            <td class="min-w-200px">{{ $itemlist->pivot->dosage ?? '' }}</td>
                                            {{-- <td class="w-200px">Tablets</td> --}}
                                            <td class="min-w-200px">{{ $itemlist->pivot->prescribed_units ?? '' }}</td>
                                            <td class="min-w-200px">£{{ $itemlist->pivot->total_price ?? '' }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-between align-items-center mt-5">
                                <p>
                                    Showing {{ $inventories->firstItem() ?? '0' }} to {{ $inventories->lastItem() ?? '0' }}
                                    of
                                    {{ $inventories->total() ?? '0' }}
                                    entries
                                </p>
                                <div class="pagination">
                                    {{ $inventories->links() }}
                                </div>
                            </div>

                        </div>
                    </div>
                    @if ($prescription->prescription_type == 'repeat' && $prescription->admin_approval == 1)
                        <div class="table-patients table-border-all my-5">
                            <div class="custom-dropdown px-5 roboto d-flex justify-content-between align-items-center pb-5">
                                <h3>Schedule
                                    <!-- <span
                                            class="input-text-gray">({{ $payments->count() ?? 0 }})</span> -->
                                </h3>
                            </div>
                            <div class="table-responsive">
                                <table id="staffPrescritionTable" class="table display custom-table gy-5 gs-5">
                                    <thead>
                                        <tr>
                                            <th class="min-w-200px">Payment Date</th>
                                            <th class="min-w-200px">Payment Status</th>
                                            <th class="min-w-200px">Status</th>
                                            <th class="min-w-200px">Action</th>
                                        </tr>
                                    </thead>
                                    <tbody class="ps-5">
                                        @foreach ($payments as $itemlist)
                                            <tr>
                                                <td class="w-200px">{{ $itemlist->payment_date ?? '' }}</td>
                                                <td class="w-200px">
                                                    @php
                                                        $today = \Carbon\Carbon::today();
                                                        $paymentDate = $itemlist->payment_date
                                                            ? \Carbon\Carbon::parse($itemlist->payment_date)
                                                            : null;
                                                        $validUntil = $itemlist->valid_until
                                                            ? \Carbon\Carbon::parse($itemlist->valid_until)
                                                            : null;

                                                        if ($prescription->type == 'Type 1') {
                                                            if ($itemlist->status == 'paid') {
                                                                $paymentStatus = 'Paid';
                                                                $paymentBadgeClass = 'deliverd';
                                                            } elseif ($validUntil && $today->isAfter($validUntil)) {
                                                                $paymentStatus = 'Overdue';
                                                                $paymentBadgeClass = 'pending';
                                                            } else {
                                                                $paymentStatus = 'Pending';
                                                                $paymentBadgeClass = 'pending';
                                                            }
                                                        } else {
                                                            $paymentStatus = 'Outsourced';
                                                            $paymentBadgeClass = '';
                                                        }
                                                    @endphp
                                                    <span
                                                        class="badge {{ $paymentBadgeClass }}-badge badge-{{ $paymentStatus }} roboto fs-14 fw-400">
                                                        {{ $paymentStatus }}
                                                    </span>
                                                </td>
                                                <td class="w-200px">
                                                    @php
                                                        $status = $itemlist->admin_approval;
                                                        $depenseStatus = $itemlist->is_dispensed;
                                                        $label =
                                                            $depenseStatus == 1
                                                                ? 'Despensed'
                                                                : ($status == 1
                                                                    ? 'Approved'
                                                                    : ($status == 2
                                                                        ? 'Rejected'
                                                                        : 'Pending'));
                                                        $badgeClass = $status == 1 ? 'deliverd' : 'pending';
                                                    @endphp
                                                    <span
                                                        class="badge {{ $badgeClass }}-badge badge-{{ $label }} roboto fs-14 fw-400">
                                                        {{ $label }}
                                                    </span>
                                                </td>
                                                <td class="w-200px">
                                                    <div class="action_btn">
                                                        <a href="#"
                                                            class="btn btn-sm btn-flex btn-center button action_btn"
                                                            data-kt-menu-trigger="click"
                                                            data-kt-menu-placement="bottom-end">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                            data-kt-menu="true">
                                                            <div class="menu-item px-3">
                                                                @php
                                                                    $today = \Carbon\Carbon::today();
                                                                    $paymentDate = $itemlist->payment_date
                                                                        ? \Carbon\Carbon::parse($itemlist->payment_date)
                                                                        : null;
                                                                    $validUntil = $itemlist->valid_until
                                                                        ? \Carbon\Carbon::parse($itemlist->valid_until)
                                                                        : null;

                                                                    $isEnabled =
                                                                        (($prescription->type == 'Type 1' &&
                                                                            $itemlist->status == 'paid') ||
                                                                            $prescription->type == 'Type 2') &&
                                                                        $itemlist->is_dispensed == 0 &&
                                                                        $paymentDate &&
                                                                        $validUntil &&
                                                                        $today->between($paymentDate, $validUntil);
                                                                @endphp
                                                                <button
                                                                    class="px-3 {{ $isEnabled ? 'create-shipment-btn btn btn-primary bt-sm' : 'btn btn-secondary bt-sm disabled' }}"
                                                                    data-order-id="{{ $isEnabled ? $prescription->order_id : '' }}"
                                                                    data-schedule-id="{{ $isEnabled ? $itemlist->order_id : '' }}"
                                                                    {{ $isEnabled ? '' : 'disabled' }}>
                                                                    {{ $isEnabled ? 'Print' : 'Print' }}
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-between align-items-center mt-5">
                                    <p>
                                        Showing {{ $payments->firstItem() }} to {{ $payments->lastItem() }} of
                                        {{ $payments->total() }}
                                        entries
                                    </p>
                                    <div class="pagination">
                                        {{ $payments->links() }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    @endif
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-body p-0">

                            <!-- Header -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center py-3 ">
                                    @include('svg.prescriber-name')
                                    <span class="inter fs-12 fw-400 ps-2 deep-charcoal-blue">Prescriber Name</span>
                                </div>
                                @if (auth()->user()->hasRole('admin'))
                                    <span
                                        class="roboto fs-12 fw-400 {{ $prescription->is_dispensed == 1 || $prescription->admin_approval == 1 ? 'badge-active' : 'medicines-status' }}">{{ $prescription->is_dispensed == 1 ? 'Dispensed' : ($prescription->admin_approval == 0 ? 'Pending' : ($prescription->admin_approval == 1 ? 'Approved' : 'Rejected')) }}</span>
                                @elseif(auth()->user()->hasRole('doctor'))
                                    <span
                                        class="roboto fs-12 fw-400 {{ $prescription->doctor_approval == 1 ? 'badge-active' : 'medicines-status' }}">{{ $prescription->doctor_approval == 0 ? 'Pending' : ($prescription->doctor_approval == 1 ? 'Approved' : 'Rejected') }}</span>
                                @else
                                    <span
                                        class="roboto fs-12 fw-400 {{ $prescription->admin_approval == 1 ? 'badge-active' : 'medicines-status' }}">{{ $prescription->admin_approval == 0 ? 'Pending' : ($prescription->admin_approval == 1 ? 'Approved' : 'Rejected') }}</span>
                                @endif
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $prescription->prescribedBy->name ?? '' }}</h6>

                            <!-- Billing Type -->
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">Billing Type</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">{{ $prescription->type ?? '' }}</p>
                            </div>

                            <!-- Patient -->
                            <div class="d-flex align-items-center py-3">
                                @include('svg.patient-name')
                                <span class="roboto fs-12 fw-400 ps-2 deep-charcoal-blue">Patient Name</span>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $prescription->patient->name ?? '' }}</h6>

                            <!-- Notes -->
                            <p class="fs-12 fw-500 table-gary-text">Prescription Notes</p>
                            <p class="fs-13 fw-400 number-gray-black">
                                {{ $prescription->prescription ?? '' }}
                            </p>


                            <!-- Prescription Type -->
                            <p class="fs-12 fw-500 table-gary-text">Prescription Type</p>
                            <p class="fs-14 fw-500 number-gray-black">
                                {{ strtoupper(str_replace('_', ' ', $prescription->prescription_type ?? '')) }}</p>
                            @if ($prescription->prescription_type == 'repeat' && $prescription->admin_approval !== 0)
                                <p class="fs-12 fw-500 table-gary-text">Prescription Duration</p>
                                <p class="fs-14 fw-500 number-gray-black">{{ $prescription->repetitions ?? '' }} month</p>


                                <p class="fs-12 fw-500 table-gary-text">Prescription Cycle</p>
                                <p class="fs-14 fw-500 number-gray-black">{{ $prescription->duration ?? '' }} days</p>
                            @endif
                            {{-- <select class="form-select fs-14 fw-500 number-gray-black rounded-3" name="prescription_type"
                                id="prescriptionType">
                                <option value="one-time">One-Time</option>
                                <option value="recurring">Recurring</option>
                            </select> --}}
                            @if ($prescription->prescription_type == 'repeat' && $prescription->admin_approval == 0)
                                <p class="fs-12 fw-500 table-gary-text">Prescription Duration</p>
                                <p class="fs-14 fw-500 number-gray-black">{{ $prescription->repetitions ?? '' }} month</p>
                                <!-- Recurring Section (Hidden by Default) -->
                                @if (auth()->user()->hasRole('admin'))
                                    <div id="recurringSection" class="mt-3">
                                        <!-- Assign Schedule Button -->
                                        <button class="btn  assign-btn" id="assignScheduleBtn">Assign Schedule in
                                            Days</button>
                                        <!-- Schedule Form (Hidden Initially) -->
                                        <form id="scheduleForm"
                                            action="{{ route('update.schedule', $prescription->order_id) }}"
                                            class="mt-3 d-none">
                                            @csrf
                                            @method('PUT')
                                            <!-- Days & Frequency -->
                                            <label class="fs-12 fw-500 table-gary-text">Add Schedule</label>

                                            <div class="d-flex align-items-center mb-3">
                                                <!-- Counter Input -->
                                                <input type="text" id="daysCount"
                                                    value="{{ $prescription->duration ?? 21 }}" name="duration"
                                                    placeholder="Add days frequency" readonly class="form-control me-2" />

                                                <!-- + / - Black Icon Buttons -->
                                                <button type="button" id="decreaseDays"
                                                    class="btn btn-sm me-1 d-flex align-items-center justify-content-center"
                                                    style="width: 32px;height: 32px;font-size: 25px;">
                                                    −
                                                </button>

                                                <button type="button" id="increaseDays"
                                                    class="btn btn-sm d-flex align-items-center justify-content-center"
                                                    style="width: 32px;height: 32px;font-size: 25px;">
                                                    +
                                                </button>
                                            </div>
                                            <!-- Save / Cancel Buttons -->
                                            <div class="d-flex justify-content-center mt-3">
                                                <button type="button" class="btn assign-btn btn-sm me-2">Cancel</button>
                                                <button type="button" class="btn assign-btn btn-sm"
                                                    id="schedule_form">Save</button>
                                            </div>

                                        </form>
                                    </div>
                                @endif
                            @endif


                            <!-- Billed -->
                            <p class="fs-12 fw-500 table-gary-text pt-5">Billed Amount</p>
                            <div>
                                <p class="fs-14 fw-400 number-gray-black">£<span
                                        class="ps-1">{{ $prescription->total }}</span></p>
                                <!-- <span class="fs-5 fw-semibold me-1">£</span> -->
                            </div>

                            @php
                                $approvalField = auth()->user()->hasRole('admin')
                                    ? $prescription->admin_approval
                                    : $prescription->doctor_approval ?? null;

                                $canApproveOrReject =
                                    ((auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Doctor') ||
                                        auth()->user()->hasRole('admin')) &&
                                    (($prescription->type == 'Type 1' && $prescription->status == 0) ||
                                        $prescription->type == 'Type 2');
                            @endphp
                            @if ($canApproveOrReject && $prescription->is_dispensed == 0)
                                <div class="d-flex mt-4 gap-5 flex-wrap">
                                    <button id="approveBtn"
                                        class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save toggle-status"
                                        data-status="Approve" data-slug="{{ $prescription->order_id }}"
                                        style="display: {{ $approvalField == 0 ? 'inline-block' : 'none' }};">Approve</button>

                                    <button id="rejectBtn"
                                        class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 toggle-status"
                                        data-status="Reject" data-slug="{{ $prescription->order_id }}"
                                        style="display: {{ $approvalField == 0 ? 'inline-block' : 'none' }};">Reject</button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="rejectionNoteModal" tabindex="-1" role="dialog"
        aria-labelledby="rejectionNoteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Prescription</h5>
                </div>
                <div class="modal-body">
                    <textarea id="rejection-note" class="form-control" placeholder="Enter rejection reason..."></textarea>
                    <input type="hidden" id="reject-prescription-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                        data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="confirm-reject"
                        class="gradient_modal_approve white-color roboto fs-14 fw-400">Reject</button>
                </div>
            </div>
        </div>
    </div>
    </section>
    @can('prescriptions-create')
        @include('dashboard.templates.modals.doctor-modals.new_prescription_modal')
    @endcan
@endsection
@push('js')
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script>
        $(document).on('click', '.create-shipment-btn', function() {
            var button = $(this);
            var orderId = button.data('order-id');
            var scheduleId = button.data('schedule-id') ?? null;

            Swal.fire({
                title: 'Confirm Action',
                text: 'Are you sure you want to create shipment and print the label for this prescription?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'Yes',
                cancelButtonText: 'No'
            }).then((result) => {
                if (!result.isConfirmed) {
                    return;
                }

                // AJAX request to create shipment and print label
                $.ajax({
                    url: '{{ route('shipment.create', ':orderId/:scheduleId') }}'
                        .replace(':orderId', orderId)
                        .replace(':scheduleId', scheduleId),
                    type: 'POST',
                    data: {
                        orderId: orderId,
                        scheduleId: scheduleId,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        // Handle success
                        if (response.message) {
                            Swal.fire({
                                title: 'Success!',
                                text: response.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Success!',
                                text: 'Shipment created and label printed successfully.',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload();
                            });
                        }

                        var row = button.closest('tr');
                        row.find('.create-shipment-btn').remove();

                        var checkbox = row.find('.checkbox_data input');
                        checkbox.prop('checked', false).prop('disabled', true);

                        var statusColumn = row.find('.status');
                        statusColumn.empty();

                        var badgeClass = 'success';
                        var label = 'Shipped';

                        statusColumn.append(`
                            <span class="badge deliverd-badge badge-Despensed roboto fs-14 fw-400">
                                Dispensed
                            </span>
                        `);
                    },
                    error: function(xhr, status, error) {
                        let errorMessage = 'Failed to create shipment. Please try again.';

                        if (xhr.responseJSON) {
                            if (xhr.responseJSON.error) {
                                errorMessage = xhr.responseJSON.error;
                            }

                            if (xhr.responseJSON.details && xhr.responseJSON.details.length >
                                0) {
                                errorMessage += '\n\nDetails:\n' + xhr.responseJSON.details
                                    .join('\n');
                            }
                        }

                        Swal.fire({
                            title: 'Shipment Creation Failed',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK',
                            width: '600px'
                        });
                    }
                });
            });
        });

        // Handle pagination click
        $(document).on('click', '.custom-pagination-link', function(e) {
            e.preventDefault();
            let page = $(this).data('page');
            let type = $(this).data('type');
            if (page && type == 'prescription') {
                loadStaffPrescriptions(page);
            }
        });

        // prescription-details
        $(document).ready(function() {
            $('#prescriptionType').on('change', function() {
                if ($(this).val() === 'recurring') {
                    $('#recurringSection').removeClass('d-none');
                } else {
                    $('#recurringSection').addClass('d-none');
                    $('#scheduleForm').addClass('d-none');
                }
            });

            $('#assignScheduleBtn').on('click', function() {
                $('#scheduleForm').toggleClass('d-none');
            });
        });


        $(document).ready(function() {
            let dayValue = 0;

            $('#increaseDays').on('click', function() {
                dayValue++;
                $('#daysCount').val(dayValue);
            });

            $('#decreaseDays').on('click', function() {
                if (dayValue > 0) {
                    dayValue--;
                    $('#daysCount').val(dayValue);
                }
            });


            $('#schedule_form').click(function(event) {
                event.preventDefault(); // Prevent the default form submission

                var formData = $('#scheduleForm').serialize(); // Serialize the form data

                // Make the AJAX request
                $.ajax({
                    url: $('#scheduleForm').attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        $('#scheduleForm').toggleClass('d-none');
                        Swal.fire({
                            title: 'Success!',
                            text: 'Schedule updated successfully.',
                            icon: 'success',
                            confirmButtonText: 'OK'
                        });

                    },
                    error: function(xhr) {
                        // If validation errors are returned, show them in the modal and in SweetAlert
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            showValidationErrors(
                                errors); // Show errors below the input fields in the form
                            showSweetAlertErrors(errors); // Show errors in SweetAlert
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                });
            });
        });
    </script>
    <script>
        $('.toggle-status').on('click', function() {
            let button = $(this);
            let prescriptionId = button.data('slug');
            let status = button.data('status');

            if (status === 'Reject') { // If rejected, show modal
                $('#reject-prescription-id').val(prescriptionId);
                $('#rejectionNoteModal').modal('show');
            } else {
                performStatusChange(prescriptionId, status, null);
            }
        });

        $('#confirm-reject').on('click', function() {
            let prescriptionId = $('#reject-prescription-id').val();
            let note = $('#rejection-note').val();
            $('#rejectionNoteModal').modal('hide');
            performStatusChange(prescriptionId, 2, note);
        });

        function performStatusChange(prescriptionId, status, note) {
            let row = $(`#prescription-${prescriptionId}`);

            $.ajax({
                url: "{{ url('prescriptions') }}" + `/${prescriptionId}/toggle-status`,
                type: 'GET',
                data: {
                    status: status,
                    note: note,
                    _token: '{{ csrf_token() }}',
                },
                success: function(response) {
                    if (response.status === 'success') {
                        let newStatus = response.new_status;
                        let statusLabel = $('.medicines-status');

                        // Update status text
                        statusLabel.text(newStatus === 1 ? 'Approved' : 'Rejected');

                        // Get the specific buttons for this prescription
                        let approveButton = $(
                            `.toggle-status[data-status="Approve"][data-slug="${prescriptionId}"]`);
                        let rejectButton = $(
                            `.toggle-status[data-status="Reject"][data-slug="${prescriptionId}"]`);

                        // Update Approve button
                        if (newStatus === 1) {
                            approveButton.hide();
                            rejectButton.show();
                        } else if (newStatus === 2) {
                            approveButton.show();
                            rejectButton.hide();
                        }

                        Swal.fire({
                            title: 'Success!',
                            text: `Prescription ${newStatus === 1 ? 'Approved' : 'Rejected'} successfully!`,
                            icon: 'success',
                            confirmButtonText: 'OK',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Reload the page after clicking OK
                                window.location.reload();
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to update status. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred. Please try again.',
                        icon: 'error',
                        confirmButtonText: 'OK',
                    });
                }
            });
        }

        $('#rejectionNoteModal').on('hidden.bs.modal', function() {
            $('#rejection-note').val('');
            $('#reject-prescription-id').val('');
        });
    </script>
@endpush
