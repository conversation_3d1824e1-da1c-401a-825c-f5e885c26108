<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Inventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'pip_code',
        'name',
        'pack_size',
        'description',
        'type',
        'cost_price',
        'sale_price',
        'stock_quantity'
    ];

    // Define the relationship to the PrescriptionInventory model
    public function prescriptionInventories()
    {
        return $this->hasMany(PrescriptionInventory::class);
    }

    // Define the relationship to the Prescriptions model through PrescriptionInventory
    public function prescriptions()
    {
        return $this->belongsToMany(Prescription::class, 'prescription_inventories')
            ->withPivot('prescribed_units', 'total_price')
            ->withTimestamps();
    }
}
