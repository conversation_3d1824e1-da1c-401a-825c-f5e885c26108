<nav class="navbar navbar-expand-xl navbar-header">
    <div class="container py-2 container-navbar">
        <a class="navbar-brand" href="{{ url('/') }}">
            <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->website_logo ?? '' }}"
                class="h-50px app-sidebar-logo-default" />
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mx-auto mb-5 mb-xl-0 navbar-items">
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 @if (request()->is('about-us')) active @endif"
                        aria-current="page" href="{{ url('/about-us') }}">About Us</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 @if (request()->is('why-us')) active @endif"
                        aria-current="page" href="{{ url('/why-us') }}">Why Us</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link  white-color fs-16 @if (request()->is('prescribers')) active @endif"
                        aria-current="page" href="{{ url('prescribers') }}">Prescribers</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 @if (request()->routeIs('our.patients')) active @endif"
                        aria-current="page" href="{{ route('our.patients') }}">Patients</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 @if (request()->is('contact-us')) active @endif"
                        aria-current="page" href="{{ url('contact-us') }}">Contact Us</a>
                </li>
                <li class="nav-item btn_nav ">
                    @auth
                        <div class="d-flex flex-md-nowrap flex-wrap row-gap-5">
                            <a class="button_login login-btn me-5" href="{{ url('logout') }}">Sign out</a>
                            <a class="button_login button1 login-btn" href="{{ url('home') }}">Dashboard</a>
                        </div>
                    @else
                        <a href="{{ url('register') }}" class="button_login button1 login-btn">Sign up</a>
                        <div class="dropdown">
                            <a class="button_login button1 white-color fs-16 dropdown-toggle" href="#" role="button"
                                id="loginDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Login
                            </a>
                            <ul class="header-login-menu dropdown-menu" aria-labelledby="loginDropdown">
                                <li><a class="dropdown-item" href="{{ route('admin.login') }}">Admin</a></li>
                                <li><a class="dropdown-item" href="{{ route('clinic.login') }}">Clinic Admin</a></li>
                                <li><a class="dropdown-item" href="{{ route('patient.login') }}">Patient</a></li>
                                <li><a class="dropdown-item" href="{{ route('doctor.login') }}">Doctor</a></li>
                                <li><a class="dropdown-item" href="{{ route('staff.login') }}">Staff</a></li>
                            </ul>
                        </div>
                    @endauth
                </li>
            </ul>
        </div>
        <div class=" btn_nav d-flex align-items-center gap-5">
            @auth
                <a href="{{ url('logout') }}" class="button_login login-btn me-2">Sign out</a>
                <a href="{{ url('home') }}" class="button_login button1 login-btn">Dashboard</a>
            @else
                <a href="{{ url('registration-instruction') }}" class="button_login button1 login-btn">Sign up</a>
                <div class="dropdown">
                    <a class="button_login button1 white-color fs-16 dropdown-toggle" href="#" role="button"
                        id="loginDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        Login
                    </a>
                    <ul class="header-login-menu dropdown-menu" aria-labelledby="loginDropdown">
                        {{-- <li><a class="dropdown-item" href="{{ route('admin.login') }}">Admin</a></li> --}}
                        <li><a class="dropdown-item" href="{{ route('clinic.login') }}">Clinic Admin</a></li>
                        <li><a class="dropdown-item" href="{{ route('doctor.login') }}">Doctor</a></li>    
                        <li><a class="dropdown-item" href="{{ route('staff.login') }}">Staff</a></li>
                        <li><a class="dropdown-item" href="{{ route('patient.login') }}">Patient</a></li>
                 
                    </ul>
                </div>
            @endauth
        </div>
    </div>
</nav>


<!-- {{-- <nav class="navbar navbar-expand-xl"> --}}
{{--    <div class="container py-2"> --}}
{{--        <a class="navbar-brand" href="{{ url('/') }}"> --}}
{{--            <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->logo ?? '' }}" --}}
{{--                 class="logo w-md-75 w-50"/> --}}
{{--        </a> --}}
{{--        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" --}}
{{--                data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" --}}
{{--                aria-expanded="false" --}}
{{--                aria-label="Toggle navigation"> --}}
{{--            <span class="navbar-toggler-icon"></span> --}}
{{--        </button> --}}
{{--        <div class="collapse navbar-collapse" id="navbarSupportedContent"> --}}
{{--            <ul class="navbar-nav mx-auto mb-2 mb-lg-0 navbar-items"> --}}
{{--                <li class="nav-item"> --}}
{{--                    <a class="nav-link text-black fs-16 @if (request()->is('/'))active @endif" aria-current="page" --}}
{{--                       href="{{url('/')}}">About Us</a></li> --}}
{{--                <li class="nav-item"> --}}
{{--                    <a class="nav-link text-black fs-16 @if (request()->is('why-us'))active @endif" aria-current="page" --}}
{{--                       href="{{ url('/why-us') }}">Why Us</a> --}}
{{--                </li> --}}
{{--                <li class="nav-item"> --}}
{{--                    <a class="nav-link  text-black fs-16 @if (request()->is('prescribers'))active @endif" --}}
{{--                       aria-current="page" --}}
{{--                       href="{{ url('prescribers') }}">Prescribers</a> --}}
{{--                </li> --}}
{{--                <li class="nav-item"> --}}
{{--                    <a class="nav-link text-black fs-16 @if (request()->is('patients'))active @endif" aria-current="page" --}}
{{--                       href="{{ url('patients') }}">Patients</a> --}}
{{--                </li> --}}
{{--                <li class="nav-item"> --}}
{{--                    <a class="nav-link text-black fs-16 @if (request()->is('contact-us'))active @endif" --}}
{{--                       aria-current="page" --}}
{{--                       href="{{ url('contact-us') }}">Contact Us</a> --}}
{{--                </li> --}}
{{--                <li class="nav-item btn_nav "> --}}
{{--                    @auth --}}
{{--                        <div class="d-flex flex-md-nowrap flex-wrap"> --}}
{{--                            <a class="button_login login-btn me-5" href="{{url('logout')}}">Logout</a> --}}
{{--                            <a class="button_login button1 login-btn" href="{{url('home')}}">Dashboard</a> --}}
{{--                        </div> --}}
{{--                    @else --}}
{{--                        <a href="{{ url('register') }}" class="button_login button1 login-btn">Sign Up</a> --}}
{{--                    @endauth --}}
{{--                </li> --}}
{{--            </ul> --}}
{{--        </div> --}}
{{--        <div class="btn_nav"> --}}
{{--            @auth --}}
{{--                <a href="{{ url('logout') }}" class="button_login login-btn me-2">Logout</a> --}}
{{--                <a href="{{ url('home') }}" class="button_login button1 login-btn">Dashboard</a> --}}
{{--            @else --}}
{{--                <a href="{{ url('register') }}" class="button_login button1 login-btn">Sign Up</a> --}}
{{--            @endauth --}}
{{--        </div> --}}
{{--    </div> --}}
{{-- </nav> --}} -->