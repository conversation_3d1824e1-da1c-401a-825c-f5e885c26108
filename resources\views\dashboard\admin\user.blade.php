@extends('theme.layout.master')
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <h4 class="roboto heading-gary"> All User</h4>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#admins">Clinic Admins</a>
                        </li>
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#doctors">Individual Prescribers</a>
                        </li>
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#staff_doctors">Staff Prescribers</a>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active custom_tabs" id="admins" role="tabpanel">
                            <form method="GET" action="{{ url()->current() }}">
                                <div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <div>
                                            <h3>Clinic Admins
                                                {{--
                                                <!-- <span class="input-text-gray"> ({{ $clinic_admins->total() ?? 0 }})</span> -->
                                                --}}
                                            </h3>
                                        </div>
                                        <div class="d-flex align-items-end justify-content-end gap-2 flex-wrap">

                                            <div class="search_box position-relative">
                                                <input type="text" id="searchMedicines"
                                                    value="{{ old('search_admin', $search_admin) }}" name="search_admin"
                                                    class=" form-control searchMedicines "
                                                    placeholder="Search clinic admins...">
                                                <a class="reset-btn position-absolute clearSearchBtn" id="clearSearchBtn"
                                                    href="{{ route('admin.users') }}#admins">
                                                    <i class="fa-solid fa-xmark"></i>
                                                </a>
                                            </div>
                                            <button class="button-gradient white-color roboto fs-14 fw-400"
                                                type="submit">Search
                                            </button>
                                        </div>
                                    </div>
                                    <div
                                        class="d-flex align-items-end justify-content-md-end justify-content-start gap-2 flex-wrap pb-5">
                                        <div class="result-container1">
                                            <div class="range-options additional-options">
                                                <div
                                                    class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                    <div class="start-range date-field">
                                                        <label for="adminStartRange">Start Range:</label>
                                                        <span class="error-message text-danger"
                                                            style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                            select Start Range
                                                            first.</span>
                                                        <input type="date" class="start_range-input start-range-input"
                                                            name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                    </div>

                                                    <div class="end-range date-field">
                                                        <label for="adminEndRange">End Range:</label>
                                                        <input type="date" class="end-range-input end-range-input"
                                                            name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <div class="d-flex align-items-end justify-content-end gap-2">
                                            <div class="custom-dropdown p-0">
                                                {{-- <div class="custom-select">
                                                    <select id="" name="adminStatus" class="status_filter type_filter"
                                                        data-control="select2" data-hide-search="true"
                                                        data-dropdown-css-class="w-200px">
                                                        <option {{ $adminStatus == null ? 'selected' : '' }} disabled value="">
                                                            All Admin</option>
                                                        <option {{ $adminStatus == '1' ? 'selected' : '' }} value="1">
                                                            Active
                                                        </option>
                                                        <option {{ $adminStatus == '0' ? 'selected' : '' }} value="0">
                                                            Inactive</option>
                                                    </select>
                                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div> --}}
                                            </div>
                                            <button class="button-gradient white-color roboto fs-14 fw-400"
                                                type="submit">Search
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class="table-responsive">
                                <table id="clinicTable" class="table display gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <th class="min-w-400px">Clinic Name</th>
                                            <th class="min-w-200px ">Admin Name</th>
                                            <th class="min-w-250px ">No. of Prescribers</th>
                                            <th class="min-w-250px ">No. of Receptionist</th>
                                            <th class="min-w-200px ">Date Added</th>
                                            <th class="min-w-200px ">Status</th>
                                            <th class="min-w-70px first-and-last "></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($clinic_admins as $clinic_admin)
                                            <tr>
                                                <td>
                                                    <a href="{{ route('users_details', ['id' => $clinic_admin->id]) }}">
                                                        <div class="d-flex gap-2 align-items-center">
                                                            <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                alt="" height="40px" width="40px">
                                                            <div class="d-flex flex-column">
                                                                <h5 class="fw-500 number-gray-black">
                                                                    {{ $clinic_admin->name ?? '' }}
                                                                </h5>
                                                                <span
                                                                    class="input-text-gray fs-14 fw-400">{{ $clinic_admin->email ?? '' }}</span>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </td>
                                                <td class="">{{ $clinic_admin->name ?? '' }}</td>
                                                <td class="">{{ $clinic_admin->countDoctors() }}</td>
                                                <td class="">{{ $clinic_admin->countReceptionists() }}</td>
                                                <td class="">{{ $clinic_admin->created_at->format('m d Y') }}</td>
                                                <td class=""><span
                                                        class="badge badge-{{ $clinic_admin->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $clinic_admin->status_text ?? '' }}</span>
                                                </td>

                                                <td class="action_btn">
                                                    <div class="dropdown">
                                                        <a class="nav-link" href="#" role="button"
                                                            id="dropdownMenuLink" data-bs-toggle="dropdown"
                                                            aria-expanded="true">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                            <li><a class="dropdown-item Satoshi"
                                                                    href="{{ route('users_details', ['id' => $clinic_admin->id]) }}">View</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>

                                <div class="d-flex justify-content-between align-items-center mt-5">
                                    <p>
                                        Showing {{ $clinic_admins->firstItem() }} to {{ $clinic_admins->lastItem() }} of
                                        {{ $clinic_admins->total() }}
                                        entries
                                    </p>
                                    <div class="pagination">
                                        {{ $clinic_admins->links() }}
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="tab-pane fade" id="doctors" role="tabpanel">
                            <div class="row custom-dropdown">
                                <div class="col-lg-12 doctor-table ">
                                    <form method="GET" action="{{ url()->current() }}#doctors">
                                        <div>
                                            <div
                                                class=" roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                                <div>
                                                    <h3>Prescribers <span class="input-text-gray">
                                                            {{-- <!-- ({{ $doctors->total() ?? 0 }})</span> --> --}}
                                                    </h3>
                                                </div>

                                                <div class="d-flex align-items-end justify-content-end gap-2 flex-wrap">
                                                    <div class="search_box position-relative">
                                                        <input type="text" id="searchDoctors"
                                                            value="{{ old('search_doctor', $search_doctor) }}"
                                                            name="search_doctor" class="form-control searchMedicines"
                                                            placeholder="Search Prescribers...">
                                                        <a class="reset-btn position-absolute clearSearchBtn"
                                                            href="{{ url()->current() }}#doctors">
                                                            <i class="fa-solid fa-xmark"></i>
                                                        </a>
                                                    </div>
                                                    <button class="button-gradient white-color roboto fs-14 fw-400"
                                                        type="submit">Search</button>
                                                </div>
                                            </div>
                                            <div
                                                class="d-flex align-items-end justify-content-md-end justify-content-start gap-2 flex-wrap pb-5">
                                                <div class="result-container1">
                                                    <div class="range-options additional-options">
                                                        <div
                                                            class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                            <div class="start-range date-field">
                                                                <label for="docStartRange">Start Range:</label>
                                                                <span class="error-message text-danger"
                                                                    style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                                    select Start Range
                                                                    first.</span>

                                                                <input type="date" class="start-range-input"
                                                                    name="docStartRange"
                                                                    value="{{ $docStartRange ?? '' }}"
                                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                            <div class="end-range date-field">
                                                                <label for="docEndRange">End Range:</label>
                                                                <input type="date" class="end-range-input"
                                                                    name="docEndRange" value="{{ $docEndRange ?? '' }}"
                                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="button-gradient white-color roboto fs-14 fw-400"
                                                    type="submit">Search
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="table-responsive">
                                    <table id="clinicTable" class="table display  gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-350px">Prescriber Name</th>
                                                <th class="min-w-200px">No. of Patients</th>
                                                <th class="min-w-250px">No. of Prescriptions</th>
                                                <th class="min-w-200px">Date Added</th>
                                                <th class="min-w-200px">Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($doctors as $doctor)
                                                <tr>
                                                    <td>
                                                        <a href="{{ route('users_details', ['id' => $doctor->id]) }}">
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $doctor->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $doctor->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </a>
                                                    </td>
                                                    <td>{{ $doctor->ownPatient() ?? '' }}</td>
                                                    <td>{{ $doctor->ownPrescriptions->count() ?? '' }}</td>
                                                    <td>{{ $doctor->created_at->format('m d Y') ?? '' }}</td>
                                                    <td><span
                                                            class="badge badge-{{ $doctor->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $doctor->status_text ?? '' }}</span>
                                                    </td>
                                                    <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button"
                                                                id="dropdownMenuLink" data-bs-toggle="dropdown"
                                                                aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi"
                                                                        href="{{ route('users_details', ['id' => $doctor->id]) }}">View</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $doctors->firstItem() }} to {{ $doctors->lastItem() }} of
                                            {{ $doctors->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $doctors->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="staff_doctors" role="tabpanel">
                            <div class="row custom-dropdown" >
                                <div class="col-lg-12 doctor-table">
                                    <form method="GET" action="{{ url()->current() }}#staff_doctors">
                                        <div>
                                            <div
                                                class=" roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                                <div>
                                                    <h3>Staff Prescribers <span class="input-text-gray">
                                                            {{-- <!-- ({{ $staff_doctors->total() ?? 0 }})</span> --> --}}
                                                    </h3>
                                                </div>

                                                <div class="d-flex align-items-end justify-content-end gap-2 flex-wrap">
                                                    <div class="search_box position-relative">
                                                        <input type="text" id="searchStaffDoctors"
                                                            value="{{ old('search_staff_doctor', $search_staff_doctor) }}"
                                                            name="search_staff_doctor"
                                                            class="form-control searchMedicines"
                                                            placeholder="Search Staff Prescribers...">
                                                        <a class="reset-btn position-absolute clearSearchBtn"
                                                            href="{{ url()->current() }}#staff_doctors">
                                                            <i class="fa-solid fa-xmark"></i>
                                                        </a>
                                                    </div>
                                                    <button class="button-gradient white-color roboto fs-14 fw-400"
                                                        type="submit">Search</button>
                                                </div>
                                            </div>
                                            <div
                                                class="d-flex align-items-end justify-content-md-end justify-content-start gap-2 flex-wrap pb-5">
                                                <div class="result-container1">
                                                    <div class="range-options additional-options">
                                                        <div
                                                            class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                            <div class="start-range date-field">
                                                                <label for="staffDocStartRange">Start Range:</label>
                                                                <span class="error-message text-danger"
                                                                    style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                                    select Start Range
                                                                    first.</span>

                                                                <input type="date" class="start-range-input"
                                                                    name="staffDocStartRange"
                                                                    value="{{ $staffDocStartRange ?? '' }}"
                                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                            <div class="end-range date-field">
                                                                <label for="staffDocEndRange">End Range:</label>
                                                                <input type="date" class="end-range-input"
                                                                    name="staffDocEndRange"
                                                                    value="{{ $staffDocEndRange ?? '' }}"
                                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <button class="button-gradient white-color roboto fs-14 fw-400"
                                                    type="submit">Search
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                                <div class="table-responsive">
                                    <table id="clinicTable" class="table display  gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-350px">Staff Prescriber Name</th>
                                                <th class="min-w-300px">Admin Name</th>
                                                <th class="min-w-200px">No. of Patients</th>
                                                <th class="min-w-250px">No. of Prescriptions</th>
                                                <th class="min-w-200px">Date Added</th>
                                                <th class="min-w-200px">Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($staff_doctors as $staff_doctor)
                                                <tr>
                                                    <td>
                                                        <a
                                                            href="{{ route('users_details', ['id' => $staff_doctor->id]) }}">
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/doctor-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $staff_doctor->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $staff_doctor->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </a>
                                                    </td>
                                                    <td>{{ $staff_doctor->createdBy->name ?? '' }}</td>
                                                    <td>{{ $staff_doctor->ownPatient() ?? '' }}</td>
                                                    <td>{{ $staff_doctor->ownPrescriptions->count() ?? '' }}</td>
                                                    <td>{{ $staff_doctor->created_at->format('m d Y') ?? '' }}</td>
                                                    <td><span
                                                            class="badge badge-{{ $staff_doctor->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $staff_doctor->status_text ?? '' }}</span>
                                                    </td>
                                                    <td class="action_btn">
                                                        <div class="dropdown">
                                                            <a class="nav-link" href="#" role="button"
                                                                id="dropdownMenuLink" data-bs-toggle="dropdown"
                                                                aria-expanded="true">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi"
                                                                        href="{{ route('users_details', ['id' => $staff_doctor->id]) }}">View</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing {{ $staff_doctors->firstItem() }} to {{ $staff_doctors->lastItem() }}
                                            of
                                            {{ $staff_doctors->total() }}
                                            entries
                                        </p>
                                        <div class="pagination">
                                            {{ $staff_doctors->links() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->
    <script>
        $(document).ready(function() {
            // Clear search inputs when clicking cross buttons
            $('.reset-btn').on('click', function(e) {
                e.preventDefault(); // Prevent default link behavior
                const searchInput = $(this).siblings('input[type="text"]');
                searchInput.val('');

                // Submit the form after clearing the input
                const form = $(this).closest('form');
                submitForm(form);
            });

            // Function to handle form submission
            function submitForm(form) {
                const activeTab = $('.nav-link.active').attr('href');
                form.attr('action', form.attr('action').split('#')[0] + activeTab);
                form.submit();
            }

            // Existing date range enable/disable logic
            $(document).on('change', 'input[name="adminStartRange"]', function() {
                $('input[name="adminEndRange"]').prop('disabled', false);
            });
            $(document).on('change', 'input[name="docStartRange"]', function() {
                $('input[name="docEndRange"]').prop('disabled', false);
            });
            $(document).on('change', 'input[name="staffDocStartRange"]', function() {
                $('input[name="staffDocEndRange"]').prop('disabled', false);
            });

            // New code for automatic form submission
            let searchTimeout;
            const autoSubmitDelay = 500; // 500ms delay

            // Handle input changes for admin, doctor, and staff doctor search
            $('input[name="search_admin"], input[name="search_doctor"], input[name="search_staff_doctor"]').on(
                'input',
                function() {
                    const form = $(this).closest('form');
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => submitForm(form), autoSubmitDelay);
                });

            // Handle select changes for admin, doctor, and staff doctor status
            $('select[name="adminStatus"], select[name="presStatus"], select[name="staffDocStatus"]').on('change',
                function() {
                    const form = $(this).closest('form');
                    submitForm(form);
                });

            // Handle date range changes for admin, doctor, and staff doctor
            $('input[name="adminStartRange"], input[name="adminEndRange"], input[name="docStartRange"], input[name="docEndRange"], input[name="staffDocStartRange"], input[name="staffDocEndRange"]')
                .on('change', function() {
                    const form = $(this).closest('form');
                    submitForm(form);
                });

            // Existing select all logic
            $('.select-all').on('click', function() {
                $('.row-select').prop('checked', $(this).prop('checked'));
            });
            $('.row-select').on('click', function() {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });
        });

        // Existing tab handling code
        document.addEventListener('DOMContentLoaded', function() {
            const hash = window.location.hash;
            if (hash === '#admins' || hash === '#doctors' || hash === '#staff_doctors') {
                const tabTrigger = document.querySelector(`a[href="${hash}"]`);
                if (tabTrigger) {
                    new bootstrap.Tab(tabTrigger).show();
                }
            }
        });
    </script>
@endpush
