@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
 @push('css')
    {{-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> --}}
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <h4 class="roboto heading-gary">All Transactions</h4>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto  fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#type_one">Type 1</a>
                        </li>
                        <li class="nav-item roboto fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#typeTwo">Type 2</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active custom_tabs" id="type_one" role="tabpanel" data-tab="Type 1">
                            <div
                                class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                <div>
                                    <h3>Transactions<span class="input-text-gray"></span></h3>
                                </div>
                                <div class="d-flex align-items-end justify-content-end gap-2 flex-wrap">
                                    <!-- <div class="custom-select">
                                        <input type="text" class="search form-control"
                                            value="{{ old('search', $search) }}" name="search"
                                            placeholder="Search by name...">
                                    </div> -->

                                    <div class="custom-select search-bar">
                                 <input type="search" class="search form-control"
                                            value="{{ old('search', $search) }}" name="search"
                                            placeholder="Search by name...">
                                </div>


                                    <!-- <div class="custom-select">
                                        <select id="statusFilter" class="status_filter">
                                            <option value="">All</option>
                                            <option value="1">Active</option>
                                            <option value="0">Inactive</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                            alt="Filter Icon">
                                    </div> -->

                                    <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All</option>
                                                <option value="0">Pending</option>
                                            <option value="1">Paid</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>

                                    <div class="custom-select">
                                            <select id="" class="type_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Types</option>
                                                <option value="one_time">One Time</option>
                                                <option value="repeat">Repeat</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table id="clinicTable" class="display table gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <!-- <th class="min-w-50px first-and-last"><input type="checkbox" id="select-all"
                                                    class="select-all"></th> -->
                                            <th class="min-w-300px">Patient Name</th>
                                            <th class="min-w-200px">Prescription Type</th>
                                            <th class="min-w-200px">Created By</th>
                                            <th class="min-w-200px">Date Requested</th>
                                            <th class="min-w-200px">Billed Amount</th>
                                            <th class="min-w-200px">Billing Status</th>
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody class="responseData">
                                        @foreach ($typeOne as $prescriptionOne)
                                                                                        <tr>
                                                                                            <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                                                            <td>
                                                                                                <a
                                                                                                    href="{{ route('prescription-details', $prescriptionOne->order_id) }}">
                                                                                                    <div class="d-flex gap-2 align-items-center">
                                                                                                        <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                                                            alt="" height="40px" width="40px">
                                                                                                        <div class="d-flex flex-column">
                                                                                                            <h5 class="fw-500 number-gray-black">
                                                                                                                {{ $prescriptionOne->patient->name ?? '' }}</h5>
                                                                                                            <span
                                                                                                                class="input-text-gray fs-14 fw-400">{{ $prescriptionOne->patient->email ?? '' }}</span>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </a>
                                                                                            </td>
                                                                                            <td>
                                                                                                <span class=" fs-14 fw-400">
                                                                                                    {{ strtoupper(str_replace('_', ' ', $prescriptionOne->prescription_type ?? '')) }}
                                                                                                </span>
                                                                                            </td>
                                                                                            <td>{{ $prescriptionOne->prescribedBy->name ?? '' }}</td>
                                                                                            <td>{{ $prescriptionOne->created_at->format('Y, M-d') }} </td>
                                                                                            <td>£{{ $prescriptionOne->total ?? '' }}</td>
                                                                                            {{-- <td><span
                                                                                                    class="badge badge-{{ $prescriptionOne->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $prescriptionOne->type == 'Type 1' ? ($prescriptionOne->status == 1 ? 'Paid' : 'Pending') : 'Outsourced' }}</span>
                                                                                            </td> --}}
                                                                                            <td>
                                                                                                <span
                                                                                                    class="badge {{ $prescriptionOne->payment_status['badge_class'] }}-badge badge-{{ $prescriptionOne->payment_status['badge_class'] }} roboto fs-14 fw-400">
                                                                                                    {{ $prescriptionOne->payment_status['status'] }}
                                                                                                </span>
                                                                                            </td>
                                                                                            <!-- {{-- <td><span
                                                                                                    class="badge {{ $prescriptionOne->status == 1 ? 'deliverd' : 'pending' }}-badge badge-{{ $prescriptionOne->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">Deliverd</span>
                                                                                            </td> --}} -->

                                            <td class="action_btn">
                                                <div class="dropdown">
                                                    <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                        <i class="fa-solid fa-ellipsis-vertical"></i>
                                                    </a>
                                                    <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                        <li><a class="dropdown-item Satoshi px-3"
                                                                href="{{ route('prescription-details', $prescriptionOne->order_id) }}">View</a>

                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>



                                                                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                 <div class="d-flex justify-content-between align-items-center mt-5">
                                <p>
                                    Showing {{ $typeOne->firstItem() }} to {{ $typeOne->lastItem() }} of {{ $typeOne->total() }}
                                    entries
                                </p>
                                 <div class="pagination">
                                    {{ $typeOne->appends(request()->except('typeOne'))->fragment('typeOne')->links() }}
                                </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade custom_tabs" id="typeTwo" role="tabpanel" data-tab="Type 2">
                            <div
                                class="custom-dropdown search-content d-flex justify-content-between align-items-center flex-wrap">
                                <div>
                                    <h3>Transactions<span class="input-text-gray"></span></h3>
                                </div>
                                <div class="d-flex align-items-end justify-content-end gap-2 flex-wrap">
                                    <div class="custom-select search-bar">
                                        <input type="search" class="search form-control"
                                            value="{{ old('search', $search) }}" name="search"
                                            placeholder="Search by name...">
                                    </div>

                                    <div class="custom-select">
                                            <select id="" class="type_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Types</option>
                                                <option value="one_time">One Time</option>
                                                <option value="repeat">Repeat</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>

                                    <!-- <div class="custom-select">
                                        <select id="statusFilter" class="status_filter">
                                            <option value="">All</option>
                                            <option value="1">Active</option>
                                            <option value="0">Inactive</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                            alt="Filter Icon">
                                    </div> -->
                                </div>

                            </div>
                            <div class="table-responsive">
                                <table id="clinicTable" class="display table gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <!-- <th class="min-w-50px first-and-last"><input type="checkbox" id="select-all"
                                                    class="select-all"></th> -->
                                            <th class="min-w-300px">Patient Name</th>
                                            <th class="min-w-200px">Prescription Type</th>
                                            <th class="min-w-200px">Created By</th>
                                            <th class="min-w-200px">Date Requested</th>
                                            <th class="min-w-200px">Billed Amount</th>
                                            <!-- {{-- <th>Billing Status</th> --}} -->
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody class="responseData">
                                        @foreach ($typeTwo as $item)
                                            <tr>
                                                <!-- <td><input type="checkbox" class="row-select"></td> -->
                                                <td>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                            alt="" height="40px" width="40px">
                                                        <div class="d-flex flex-column">
                                                            <h5 class="fw-500 number-gray-black">
                                                                {{ $item->patient->name ?? '' }}</h5>
                                                            <span
                                                                class="input-text-gray fs-14 fw-400">{{ $item->patient->email ?? '' }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class=" fs-14 fw-400">
                                                        {{ strtoupper(str_replace('_', ' ', $item->prescription_type ?? '')) }}
                                                    </span>
                                                </td>
                                                <td>{{ $item->prescribedBy->name ?? '' }}</td>
                                                <td>{{ $item->created_at->format('Y, M-d') }} </td>
                                                <td>£{{ $item->total ?? '' }}</td>
                                                <!-- {{-- <td><span
                                                        class="badge badge-{{ $item->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $item->status == 1 ? 'Paid' : 'Pending' }}</span>
                                                </td> --}} -->
                                                <!-- {{-- <td><span
                                                        class="badge {{ $item->status == 1 ? 'deliverd' : 'pending' }}-badge badge-{{ $item->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">Deliverd</span>
                                                </td> --}} -->

                                                <td class="action_btn">
                                                <div class="dropdown">
                                                    <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                        <i class="fa-solid fa-ellipsis-vertical"></i>
                                                    </a>
                                                    <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                        <li><a class="dropdown-item Satoshi px-3"
                                                             href="{{ route('prescription-details', $item->order_id) }}">View</a>

                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>




                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-between align-items-center mt-5">
                                <p>
                                    Showing {{ $typeTwo->firstItem() }} to {{ $typeTwo->lastItem() }} of {{ $typeTwo->total() }}
                                    entries
                                </p>
                                 <div class="pagination">
                                    {{ $typeTwo->appends(request()->except('typeTwo'))->fragment('typeTwo')->links() }}
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </section>
@endsection
@push('js')
  {{-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> --}}
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const hash = window.location.hash;

            if (hash === '#type_one' || hash === '#typeTwo') {
                const tabTrigger = document.querySelector(`a[href="${hash}"]`);
                if (tabTrigger) {
                    new bootstrap.Tab(tabTrigger).show();
                }

            }
        });
    </script>

    <script>
        $(document).on('click', '.custom-pagination-class a', function(e) {
            e.preventDefault();
            var page = $(this).attr('href').split('page=')[1];
            handleSearch(page);
        });
        $(document).ready(function() {
            let typingTimer;
            let doneTypingInterval = 500;
            $('.search').on('input', function() {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    handleSearch();
                }, doneTypingInterval);
            });

            function handleSearch(page = 1) {
                var active_div = $('.tab-content .tab-pane.active');
                var status = active_div.find('.status_filter').val();
                var type = active_div.find('.type_filter').val();
                var search = active_div.find('.search').val();
                var tabType = active_div.data('tab');

                // Add page parameter to the request
                $.ajax({
                    url: "{{ route('subscription.search') }}",
                    method: "POST",
                    data: {
                        tabType: tabType,
                        search: search,
                        status: status,
                        type: type,
                        page: page, // Include page number for pagination
                        _token: "{{ csrf_token() }}",
                    },
                    success: function(response) {
                        if (response.success) {
                            // Clear previous response data
                            active_div.find('.responseData').html('');

                            // Append new data
                            response.data.forEach(function(item) {
                                let html = `
                    <tr>

                        <td>
                            <div class="d-flex gap-2 align-items-center">
                                <img src="${item.avatar ?? '{{ asset('website') }}/assets/media/images/table-featured-icon.svg'}"
                                     alt="" height="40px" width="40px">
                                <div class="d-flex flex-column">
                                    <h5 class="fw-500 number-gray-black">${item.patient.name ?? ''}</h5>
                                    <span class="input-text-gray fs-14 fw-400">${item.patient.email ?? ''}</span>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class=" fs-14 fw-400">
                                ${item.prescription_type ? item.prescription_type.toUpperCase().replace('_', ' ') : ''}
                            </span>
                        </td>
                        <td>${item.prescribed_by?.name ?? ''}</td>
                        <td>${item.created_at ? new Date(item.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }) : ''}</td>
                        <td>£${item.total ?? ''}</td>
                        ${item.type === 'Type 1' ? `
                        <td>
                            <span class="badge badge-${item.status == 1 ? 'active' : 'inactive'} roboto fs-14 fw-400">
                                ${item.status == 1 ? 'Paid' : 'Pending'}
                            </span>
                        </td>
                        ` : ''}
                                                <td>
                                                    <div class="dowpdown">
                                                        <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                               <i class="fa-solid fa-ellipsis-vertical"></i>
                            </a>
                                                        <ul class="dropdown-menu main-submenu" aria-labelledby="dropdownMenuLink">
                                                            <li><a class="dropdown-item Satoshi" href="{{ url('prescription-details', '') }}/${item.order_id}" >View</a>
                                                            </li>
                                                        </ul>
                            </div>
                        </td>
                    </tr>
                    `;
                                active_div.find('.responseData').append(html);
                            });

                            // Append pagination
                            if (response.pagination) {
                                var paginationContainer = active_div.find(
                                    '.pagination');

                                // Clear existing pagination if necessary
                                paginationContainer.empty();
                                paginationContainer.append(response
                                    .pagination);
                                paginationContainer.find('a').on('click', function(e) {
                                    e.preventDefault();
                                    var page = $(this).attr('href').split('page=')[
                                        1];
                                    handleSearch(
                                        page
                                    );
                                });
                            } else {
                                var paginationContainer = active_div.find(
                                    '.pagination');
                                paginationContainer.empty();
                            }

                            // Update pagination information text
                            if (response.pagination_info) {
                                var paginationInfo = active_div.find('.d-flex.justify-content-between.align-items-center.mt-5 p');
                                paginationInfo.html(response.pagination_info);
                            }
                        }
                    }
                });
            }
            const table = $('.data_table').DataTable({
                paging: false,
                pageLength: 5,
                lengthChange: false,
                searching: false,
                // ordering: true,
                info: false,
                ordering: false,
                columnDefs: [{
                    orderable: false,
                    targets: 0
                }],
                language: {
                    paginate: {
                        previous: '<i class="fa fa-chevron-left"></i>',
                        next: '<i class="fa fa-chevron-right"></i>'
                    }
                }
            });

            $('.status_filter').on('change', function() {
                // alert('ok');
                handleSearch();

            });

            $('.type_filter').on('change', function() {
                handleSearch();
            });

            $('.select-all').on('click', function() {
                const isChecked = $(this).prop('checked');
                $('.row-select').prop('checked', isChecked);
            });

            $('.row-select').on('click', function() {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });
        });
    </script>
@endpush
