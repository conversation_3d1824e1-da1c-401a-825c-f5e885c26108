@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12 custom-dropdown py-5">
                    <div class="d-flex justify-content-between">
                        <h4 class="roboto heading-gary"> All Transactions </h4>
                        <!-- {{-- <a href="#!" class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save"> Add Doctors</a> --}} -->
                    </div>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="clinic_doctors" role="tabpanel">
                            <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                <h3> Transactions <span class="input-text-gray"></span></h3>
                                <!-- <div class="custom-select">
                                        <select id="statusFilter">
                                            <option value="">All transactions</option>
                                            <option value="1">Paid</option>
                                            <option value="0">Pending</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                    </div> -->
                                <div class="custom-select">
                                    <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                        data-dropdown-css-class="w-150px">
                                        <option value="" selected>All transactions</option>
                                        <option value="1">Paid</option>
                                        <option value="0">Pending</option>
                                    </select>
                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table id="clinicTable" class="table display custom-table gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <!-- <th class="ps-5 min-w-20px first-and-last"><input type="checkbox"
                                                    id="select-all" class="select-all ">
                                            </th> -->
                                            <th class="min-w-300px">Staff Name</th>
                                            <th class="min-w-200px">Month</th>
                                            <th class="min-w-200px">Total Amount</th>
                                            <th class="min-w-200px">Cashback Amount</th>
                                            <th class="min-w-200px">Payment Status</th>
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="typeOneTableBody">
                                        @forelse ($transactions as $transaction)
                                            <tr>
                                                <!-- <td><input type="checkbox" class="row-slect"></td> -->
                                                <td>{{ $transaction->user->name ?? '' }}</td>
                                                <td>{{ \Carbon\Carbon::parse($transaction->month)->format('F Y') ?? '' }}</td>
                                                <td>£{{ $transaction->total_amount ?? '' }}</td>
                                                <td>£{{ $transaction->cashback_amount ?? '' }}</td>
                                                @if ($transaction->status == 0)
                                                    <td data-status="{{ $transaction->status }}">
                                                        <span class="badge badge-inactive roboto fs-14 fw-400">Pending</span>
                                                    </td>
                                                @else
                                                    <td data-status="{{ $transaction->status }}">
                                                        <span class="badge badge-inactive roboto fs-14 fw-400">Paid</span>
                                                    </td>
                                                @endif
                                                <td></td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7" class="text-center">No transactions found</td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-between align-items-center mt-5">
                                    <p>
                                        Showing {{ $transactions->firstItem() }} to {{ $transactions->lastItem() }} of
                                        {{ $transactions->total() }}
                                        entries
                                    </p>
                                    <div class="pagination">
                                        {{ $transactions->links() }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.templates.modals.clinic-modals.prescription_modal')
@endsection

@push('js')
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->
    <!-- {{-- <script>
            $(document).ready(function() {
                let user_id = "{{ $user->id ?? '' }}";
                let Timer;

                // Search input handler
                $(document).on('keyup', '.search', function() {
                    clearTimeout(Timer);
                    Timer = setTimeout(function() {
                        fetchData();
                    }, 300);
                });

                // Status filter handler
                $(document).on('change', '.search_filter', function() {
                    fetchData();
                });

                // Pagination handler
                $(document).on('click', '.pagination a', function(e) {
                    e.preventDefault();
                    let page = $(this).attr('href').split('page=')[1];
                    fetchData(page);
                });

                function fetchData(page = 1) {
                    var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                    var prescriptionType = $('.nav-tabs .nav-link.active', '#prescriptionTabContent').attr('href');
                    var searchValue = $(prescriptionType + ' .search').val();
                    var statusValue = $(prescriptionType + ' #statusFilter').val();

                    $.ajax({
                        url: "{{ route('search.clinic.staff') }}",
                        type: 'GET',
                        data: {
                            search: searchValue,
                            status: statusValue,
                            type: prescriptionType === '#type_one' ? 'Type 1' : 'Type 2',
                            tab: activeTab,
                            page: page,
                            user_id: user_id
                        },
                        success: function(data) {
                            // Update the appropriate table based on the active prescription tab
                            if (prescriptionType === '#type_one') {
                                $('#typeOneTable').html(data);
                            } else {
                                $('#typeTwoTable').html(data);
                            }

                            // Update the count in the active tab's header
                            if (activeTab === '#patients') {
                                $(activeTab + ' .input-text-gray').text('(' + $(data).find('.pagination-container').data('total') + ')');
                            } else if (activeTab === '#prescriptions') {
                                $(prescriptionType + ' .input-text-gray').text('(' + $(data).find('.pagination-container').data('total') + ')');
                            }
                        },
                        error: function(xhr) {
                            console.error('Search failed:', xhr);
                        }
                    });
                }

                // Initial load for both prescription types
                fetchData();

                $('#statusFilter').on('change', function() {
                    const status = $(this).val();
                    table.column(7).search(status, true, false).draw();
                });

                $('.select-all').on('click', function() {
                    const isChecked = $(this).prop('checked');
                    $('.row-select').prop('checked', isChecked);
                });

                $('.row-select').on('click', function() {
                    if (!$('.row-select:checked').length) {
                        $('#select-all').prop('checked', false);
                    }
                });

                function loadTransactions(page = 1, role = '', perPage = 10) {
                    $.ajax({
                        url: "{{ route('clinic.transactions') }}",
                        type: "GET",
                        dataType: "json",
                        data: {
                            page: page,
                            role: role,
                            per_page: perPage,
                        },
                        beforeSend: function() {
                            console.log("Loading page:", page);
                        },
                        success: function(data) {
                            let tableBody, paginationLinks;
                            if (role === 'Type 1') {
                                tableBody = $('#typeOneTableBody');
                                paginationLinks = $('#typeOnePaginationLinks');
                            } else if (role === 'Type 2') {
                                tableBody = $('#typeTwoTableBody');
                                paginationLinks = $('#typeTwoPaginationLinks');
                            }

                            if (tableBody && paginationLinks) {
                                if (!data.rows.trim()) {
                                    tableBody.html(
                                        `<tr><td colspan="8" class="text-center">No Record Found</td></tr>`
                                    );
                                    paginationLinks.html(""); // Clear pagination if no records exist
                                } else {
                                    tableBody.html(data.rows);
                                    paginationLinks.html(data.pagination);
                                }
                            }
                        },
                        complete: function() {
                            KTMenu.createInstances();
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Error: ", xhr.responseText);
                        }
                    });
                }
                // Load staff on page load
                loadTransactions(null, 'Type 1');
                loadTransactions(null, 'Type 2');

                // Handle pagination click
                $(document).on('click', '.custom-pagination-link', function(e) {
                    e.preventDefault();
                    let page = $(this).data('page');
                    let role = $(this).data('type');
                    if (page) {
                        loadTransactions(page, role);
                    }
                });
            });
        </script> --}} -->
    <script>
        $(document).ready(function () {
            $('#statusFilter').on('change', function () {
                var selectedStatus = $(this).val();

                $('#clinicTable tbody tr').each(function () {
                    var rowStatus = $(this).find('td[data-status]').data('status').toString();

                    if (selectedStatus === '' || rowStatus === selectedStatus) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        });
    </script>
@endpush