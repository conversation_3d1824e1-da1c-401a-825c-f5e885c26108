<div class="modal fade" tabindex="-1" id="add_new_staff_modal">
    <div class="modal-dialog new-staff modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header ">
                <h4 class="modal-title white-color inter w-100 text-center">Create new Staff Member</h4>
            </div>
            <form id="staff-form" action="{{ route('create.staff') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body new-staff-modal pb-0">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Title</h5>
                                <select class="form-select px-0 pt-0 @error('title') is-invalid @enderror"
                                    name="title">
                                    <option value="" selected disabled>Select an option</option>
                                    <option value="Dr." {{ old('title') == 'Dr.' ? 'selected' : '' }}>Dr.</option>
                                    <option value="Nurse" {{ old('title') == 'Nurse' ? 'selected' : '' }}>Nurse
                                    </option>
                                    <option value="Pharmacist" {{ old('title') == 'Pharmacist' ? 'selected' : '' }}>
                                        Pharmacist</option>
                                    <option value="Ward boy" {{ old('title') == 'Ward boy' ? 'selected' : '' }}>Ward boy
                                    </option>
                                </select>
                                @error('title')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Full Name</h5>
                                <input type="text" placeholder="As per official documents"
                                    class="w-100 @error('name') is-invalid @enderror" name="name"
                                    value="{{ old('name') }}" />
                                @error('name')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Date of Birth</h5>
                                <input type="date" name="dob" class="w-100 @error('dob') is-invalid @enderror"
                                    value="{{ old('dob') }}" />
                                @error('dob')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Health Care Professional Type</h5>
                                <select class="form-select px-0 pt-0 @error('professional_type') is-invalid @enderror"
                                    name="professional_type">
                                    <option selected value="" disabled>Select an option</option>
                                    <option value="Nurse" {{ old('professional_type') == 'Nurse' ? 'selected' : '' }}>
                                        Nurse</option>
                                    <option value="Pharmacist"
                                        {{ old('professional_type') == 'Pharmacist' ? 'selected' : '' }}>Pharmacist
                                    </option>
                                    <option value="Ward boy"
                                        {{ old('professional_type') == 'Ward boy' ? 'selected' : '' }}>Ward boy
                                    </option>
                                </select>
                                @error('professional_type')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Registration Number</h5>
                                <input type="text" placeholder="Enter your professional reg. no."
                                    class="w-100 @error('reg_no') is-invalid @enderror" name="reg_no"
                                    value="{{ old('reg_no') }}" />
                                @error('reg_no')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Mobile Number</h5>
                                <input type="number" placeholder="Your mobile contact no."
                                    class="w-100 @error('mobile_number') is-invalid @enderror" name="mobile_number"
                                    value="{{ old('mobile_number') }}" />
                                @error('mobile_number')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
                         <div class="row">
                        <div id="doctorDocs" style="display: none;">
                            <div class="col-md-12 border-line">
                                <div class="clinic-certificate mb-7 pt-4">
                                    <div class="upload-container upload_doc pt-5">
                                        <img src="{{ asset('website') }}/assets/media/images/upload_doc.svg"
                                            alt="">
                                        <p class="fs-14 fw-500 text-center my-3">Upload Photo ID document (Passport or
                                            Drivers Licence)</p>
                                        <p class="drag-text text-center fs-12 fw-500">
                                            Drag a File here or
                                            <label class="uploadLabel">Upload a Document</label>
                                        </p>
                                        <input type="file"
                                            class="fileUpload @error('id_document') is-invalid @enderror"
                                            accept=".png,.jpg,.jpeg,.pdf" style="display: none;" name="id_document"
                                            id="id_document">
                                        @error('id_document')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 border-line">
                                <div class="clinic-certificate mb-7 pt-4">
                                    <div class="upload-container upload_doc pt-5">
                                        <img src="{{ asset('website') }}/assets/media/images/upload_doc.svg"
                                            alt="">
                                        <p class="fs-14 fw-500 text-center my-3">Upload Indemnity Certificate</p>
                                        <p class="drag-text text-center fs-12 fw-500">
                                            Drag a File here or
                                            <label class="uploadLabel">Upload a Document</label>
                                        </p>
                                        <input type="file"
                                            class="fileUpload @error('idemnity_certificate') is-invalid @enderror"
                                            accept=".png,.jpg,.jpeg,.pdf" style="display: none;"
                                            name="idemnity_certificate" id="idemnity_certificate">
                                        @error('idemnity_certificate')
                                            <span class="invalid-feedback">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row my-4">
                            <div class="col-lg-6">
                                <div class="patients-email mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Email Address</h5>
                                    <input type="text" placeholder="Email address to create account"
                                        class="w-100 @error('email') is-invalid @enderror" name="email"
                                        value="{{ old('email') }}" />
                                    @error('email')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="patients-email mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Password</h5>
                                    <input type="password" placeholder="Create a strong password"
                                        class="w-100 @error('password') is-invalid @enderror" name="password" />
                                    @error('password')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mb-5">
                            <div class="mb-5 border-line">
                                <div class="fs-7 mb-3">Allow staff to add patients</div>
                                <label class="form-check form-switch form-check-custom form-check-solid">
                                    <input class="form-check-input mb-4" name="permissions[]" type="checkbox"
                                        value="patient-create" checked="checked"
                                        {{ in_array('patient-create', old('permissions', [])) ? 'checked' : '' }} />
                                </label>
                            </div>
                            <div class=" mb-5 border-line">
                                <div class="fs-7 mb-3">Allow staff to add prescriptions</div>
                                <label class="form-check form-switch form-check-custom form-check-solid">
                                    <input class="form-check-input mb-4" name="permissions[]" type="checkbox"
                                        value="prescriptions-create" checked="checked"
                                        {{ in_array('prescriptions-create', old('permissions', [])) ? 'checked' : '' }} />
                                </label>
                            </div>
                            @error('permissions')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Role</h5>
                                <select class="form-select px-0 pt-0 @error('role') is-invalid @enderror" name="role"
                                    id="role">
                                    <option selected value="" disabled>Select a role</option>
                                    <option value="Doctor" {{ old('role') == 'Doctor' ? 'selected' : '' }}>Doctor
                                    </option>
                                    <option value="Receptionist" {{ old('role') == 'Receptionist' ? 'selected' : '' }}>
                                        Receptionist</option>
                                </select>
                                @error('role')
                                    <span class="invalid-feedback">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                    </div>
               
                </div>
                <div class="modal-footer justify-content-center gap-3 border-0 pt-0">
                    <button type="button" class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                        data-bs-dismiss="modal">Cancel
                    </button>
                    <button type="submit"
                        class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save">Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.querySelectorAll('.fileUpload').forEach((fileInput) => {
        const uploadLabel = fileInput.closest('.upload-container').querySelector('.uploadLabel');

        fileInput.addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                uploadLabel.textContent = `File uploaded: ${fileInput.files[0].name}`;
                uploadLabel.style.color = "#197C48"; // Optional: Change color to indicate success
            }
        });

        // Allow label to trigger file upload
        uploadLabel.addEventListener('click', () => fileInput.click());
    });
</script>
@push('js')
    <script>
        $(document).ready(function() {
            @if ($errors->any())
                @if (old('role') == 'Doctor')
                    $('#doctorDocs').show();
                @endif
                $('#add_new_staff_modal').modal('show');
            @endif

            $('#add_new_staff_modal').on('hidden.bs.modal', function() {
                $('#staff-form')[0].reset(); // Reset all fields in the form
                $('#staff-form').find('.is-invalid').removeClass('is-invalid'); // Remove error styles
                $('#staff-form').find('.invalid-feedback').remove(); // Remove error messages
                $('#staff-form').find('.text-danger').remove(); // Remove error messages
            });
            $('#role').on('change', function() {
                var selectedValue = $(this).val(); // Get selected value

                if (selectedValue === 'Doctor') {
                    $('#id_document').prop('disabled', false);
                    $('#idemnity_certificate').prop('disabled', false);

                    $('#doctorDocs').show();
                } else {
                    $('#id_document').prop('disabled', true);
                    $('#idemnity_certificate').prop('disabled', true);
                    $('#doctorDocs').hide();
                }
            });
        });
    </script>
@endpush
