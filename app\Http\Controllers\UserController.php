<?php

namespace App\Http\Controllers;
use App\Rules\NotDisposableEmail;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Jobs\SendUserImportEmail;
use App\Mail\PatientMail;
use App\Mail\UserImportedMail;
use App\Models\User;
use App\Models\UserType;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;
use Hash;
use App\Models\Crud;
use App\Models\Profile;
use App\Services\DpdApiService;
use Illuminate\Support\Arr;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Spatie\Permission\Models\Permission;
use File;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    function __construct()
    {
        $this->middleware('permission:user-list|user-create|user-edit|user-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:user-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:user-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:user-delete', ['only' => ['destroy']]);
        $this->middleware('permission:user-list', ['only' => ['show']]);
    }

    public function index(Request $request): View
    {
        $data = User::latest()->paginate(20);

        return view('theme.user-management.users.index', compact('data'))
            ->with('i', ($request->input('page', 1) - 1) * 5);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(): View
    {
        $roles = Role::pluck('name', 'name')->all();
        return view('theme.user-management.users.create', compact('roles'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request): RedirectResponse
    {
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|same:confirm-password',
            'roles' => 'required',
            'pic' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        $input = $request->all();
        $input['password'] = Hash::make($input['password']);

        $user = User::create($input);
        $profile = $user->profile;
        if ($user->profile == null) {
            $profile = new  Profile();
        }
        if ($request->hasFile('pic')) {
            $file = $request->pic;
            $extension = $file->extension() ?: 'png';
            $destinationPath = public_path() . '/storage/uploads/users/';
            $safeName = Str::random(10) . '.' . $extension;
            $file->move($destinationPath, $safeName);
            $profile->pic = $safeName;
        } else {
            $profile->pic = 'no_avatar.jpg';
        }
        $profile->user_id = $user->id;
        $profile->save();
        $user->assignRole($request->input('roles'));
        return redirect()->route('users.index')->with(['title' => 'Done', 'message' => 'User created successfully', 'type' => 'success']);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id): View
    {
        $user = User::find($id);
        return view('theme.user-management.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id): View
    {
        $user = User::find($id);
        $roles = Role::pluck('name', 'name')->all();
        $userRole = $user->roles->pluck('name', 'name')->all();

        return view('theme.user-management.users.edit', compact('user', 'roles', 'userRole'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id): RedirectResponse
    {
        $this->validate($request, [
            'name' => 'required',
            'email' => 'required|email|unique:users,email,' . $id,
            'password' => 'same:confirm-password',
            'roles' => 'required'
        ]);

        $input = $request->all();
        if (!empty($input['password'])) {
            $input['password'] = Hash::make($input['password']);
        } else {
            $input = Arr::except($input, array('password'));
        }

        $user = User::find($id);
        $user->update($input);
        $profile = $user->profile;
        if ($user->profile == null) {
            $profile = new  Profile();
        }
        if ($request->hasFile('pic')) {
            $file = $request->pic;
            $extension = $file->extension() ?: 'png';
            $destinationPath = public_path() . '/storage/uploads/users/';
            $safeName = Str::random(10) . '.' . $extension;
            $file->move($destinationPath, $safeName);
            //delete old pic if exists
            if (File::exists($destinationPath . $user->pic)) {
                File::delete($destinationPath . $user->pic);
            }
            //save new file path into db
            $profile->pic = $safeName;
        }
        $profile->user_id = $user->id;
        $profile->save();
        DB::table('model_has_roles')->where('model_id', $id)->delete();
        $user->assignRole($request->input('roles'));
        return redirect()->route('users.index')->with(['title' => 'Done', 'message' => 'User updated successfully', 'type' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id): RedirectResponse
    {
        User::find($id)->delete();
        return redirect()->route('users.index')
            ->with('success', 'User deleted successfully');
    }


    public function createStaff(Request $request)
    {
        // return $request->all();
        $this->validate($request, [
            'name' => 'required',
            'title' => 'required',
            'email' => 'required|email|unique:users,email',
            'password' => 'required',
            'dob' => ['required'],
            'professional_type' => ['required'],
            'reg_no' => ['required'],
            'mobile_number' => ['required', 'regex:/^(\+\d{1,3}[- ]?)?\d{10,15}$/'],
            'role' => 'required|in:Doctor,Receptionist',
            'id_document' => 'required_if:role,Doctor|file|mimes:png,jpg,jpeg,pdf|max:2048',
            'idemnity_certificate' => 'required_if:role,Doctor|file|mimes:png,jpg,jpeg,pdf|max:2048',
            'permissions' => 'required|array',
            'permissions.*' => 'in:patient-create,prescriptions-create',
        ], [
            'name.required' => 'Full Name is required.',
            'title.required' => 'Please select a Title.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.same' => 'Password and Confirm Password must match.',
            'dob.required' => 'Date of Birth is required.',
            'professional_type.required' => 'Please select a Professional Type.',
            'reg_no.required' => 'Registration Number is required.',
            'mobile_number.required' => 'Mobile Number is required.',
            'mobile_number.regex' => 'Please enter a valid mobile number (10-15 digits, optionally with country code).',
            'role.required' => 'Please select a Role.',
            'role.in' => 'The selected role is invalid',
            'id_document.required_if' => 'The ID document is required.',
            'idemnity_certificate.required_if' => 'The indemnity certificate is required.',
            'id_document.file' => 'The ID document must be a file.',
            'id_document.mimes' => 'The ID document must be a PNG, JPG, JPEG, or PDF file.',
            'id_document.max' => 'The ID document must not be greater than 2MB.',
            'idemnity_certificate.file' => 'The indemnity certificate must be a file.',
            'idemnity_certificate.mimes' => 'The indemnity certificate must be a PNG, JPG, JPEG, or PDF file.',
            'idemnity_certificate.max' => 'The indemnity certificate must not be greater than 2MB.',
            'permissions.required' => 'Please select at least one permission.',
            'permissions.*.in' => 'Invalid permission selected.',
        ]);

        $input = $request->all();
        $input['user_id'] = auth()->user()->id;
        $input['clinic_id'] = auth()->user()->id;
        if (strtolower($input['role']) === 'doctor') {
            $input['status'] = 0;
        } else {
            $input['status'] = 1;
        }
        $input['password'] = Hash::make($input['password']);

        $user = User::create($input);
        $profile = $user->profile;
        if ($user->profile == null) {
            $profile = new Profile();
        }
        if ($request->hasFile('pic')) {
            $file = $request->pic;
            $extension = $file->extension() ?: 'png';
            $destinationPath = public_path() . '/storage/uploads/users/';
            $safeName = Str::random(10) . '.' . $extension;
            $file->move($destinationPath, $safeName);
            $profile->pic = $safeName;
        } else {
            $profile->pic = null;
        }
        $id_document_path = null;
        if ($request->hasFile('id_document')) {
            $id_document_path = $this->storeImage('documents', $input['id_document']);
        }
        $profile->user_id = $user->id;
        $profile->professional_type = $input['professional_type'];
        $profile->reg_no = $input['reg_no'];
        $profile->mobile_number = $input['mobile_number'];
        $profile->title = $input['title'];
        $profile->role = $input['role'];
        if (strtolower($input['role']) === 'doctor') {
            $uniqueId = 'DOC-' . date('Ymd') . '-' . strtoupper(Str::random(6));
            $profile->doctor_id = $uniqueId;
        }
        $profile->id_document = $id_document_path;
        $profile->save();
        $user->assignRole('staff');
        if ($request->has('permissions')) {
            $permissions = $request->input('permissions');
            if (in_array('patient-create', $permissions)) {
                $permissions[] = 'patient-list';
            }
            if (in_array('prescriptions-create', $permissions)) {
                $permissions[] = 'prescriptions-list';
            }
            $user->syncPermissions($permissions);
        }
        $admin = User::whereHas('roles', function ($query) {
            $query->where('name', 'admin');
        })->first();
        $this->sendNotification($user->id, 'Staff Created', $user->name . ' registered in a platfrom as a staff');
        $this->sendNotification(auth()->id(), 'Staff Created', $user->name . ' registered in a platfrom as a staff');
        $this->sendNotification($admin->id, 'Staff Created', $user->name  . ' registered in a platfrom as a staff');
        return redirect()->back()->with(['title' => 'Done', 'message' => 'Staff created successfully', 'type' => 'success']);
    }

    public function createPatient(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'gender' => 'required|in:Male,Female',
            'mobile_number' => ['required', 'regex:/^(\+\d{1,3}[- ]?)?\d{10,15}$/'],
            // 'email' => ['required', 'email', 'unique:users,email', new NotDisposableEmail],
            'email' => ['required', 'email', 'unique:users,email'],
            'dob' => 'required|date',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'postal' => 'required',
            'country' => 'required|string|max:100',
            'delivery_address' => 'required|string|max:255',
            'delivery_city' => 'required|string|max:100',
            'delivery_postal' => 'required',
            'delivery_country' => 'required|string|max:100',
        ], [
            'name.required' => 'Full name is required.',
            'name.string' => 'Full name must be a string.',
            'name.max' => 'Full name cannot exceed 255 characters.',
            'gender.required' => 'Please select gender.',
            'gender.in' => 'Please select a valid gender.',
            'mobile_number.required' => 'Mobile number is required.',
            'mobile_number.regex' => 'Please enter a valid mobile number (10-15 digits, optionally with country code).',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'dob.required' => 'Date of birth is required.',
            'dob.date' => 'Please enter a valid date.',
            'address.required' => 'Address is required.',
            'address.string' => 'Address must be a string.',
            'address.max' => 'Address cannot exceed 255 characters.',
            'city.required' => 'City is required.',
            'city.string' => 'City must be a string.',
            'city.max' => 'City cannot exceed 100 characters.',
            'postal.required' => 'Postcode is required.',
            'country.required' => 'Country is required.',
            'country.string' => 'Country must be a string.',
            'country.max' => 'Country cannot exceed 100 characters.',
            'delivery_address.required' => 'Delivery Address is required.',
            'delivery_address.string' => 'Delivery Address must be a string.',
            'delivery_address.max' => 'Delivery Address cannot exceed 255 characters.',
            'delivery_city.required' => 'Delivery City is required.',
            'delivery_city.string' => 'Delivery City must be a string.',
            'delivery_city.max' => 'Delivery City cannot exceed 100 characters.',
            'delivery_postal.required' => 'Delivery Postcode is required.',
            'delivery_country.required' => 'Delivery Country is required.',
            'delivery_country.string' => 'Delivery Country must be a string.',
            'delivery_country.max' => 'Delivery Country cannot exceed 100 characters.',
        ]);

        try {
            // Validate UK address
            $userService = new DpdApiService();
            $userService->validateUKAddress([
                'delivery_postal' => $request->input('delivery_postal'),
                'delivery_city' => $request->input('delivery_city'),
                'delivery_country' => $request->input('delivery_country'),
            ]);
        } catch (ValidationException $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors($e->errors());
        }

        DB::beginTransaction();
        try {
            $user = auth()->user();
            $input = $request->all();
            $input['user_id'] = $user->id;
            if ($user->hasRole('staff')) {
                $input['clinic_id'] = $user->user_id ?? null;
                $input['status'] = 1;
            } else {
                $input['clinic_id'] = $user->id;
                $input['status'] = 1;
            }
            $pass = bin2hex(random_bytes(4));
            $input['password'] = Hash::make($pass);

            $user = User::create($input);
            $profile = $user->profile;
            if ($user->profile == null) {
                $profile = new Profile();
            }
            if ($request->hasFile('pic')) {
                $file = $request->pic;
                $extension = $file->extension() ?: 'png';
                $destinationPath = public_path() . '/storage/uploads/users/';
                $safeName = Str::random(10) . '.' . $extension;
                $file->move($destinationPath, $safeName);
                $profile->pic = $safeName;
            } else {
                $profile->pic = null;
            }
            $profile->user_id = $user->id;
            $profile->gender = $input['gender'];
            $profile->mobile_number = $input['mobile_number'];
            $profile->dob = $input['dob'];
            $profile->address = $input['address'];
            $profile->city = $input['city'];
            $profile->postal = $input['postal'];
            $profile->country = $input['country'];
            $profile->delivery_address = $input['delivery_address'];
            $profile->delivery_city = $input['delivery_city'];
            $profile->delivery_postal = $input['delivery_postal'];
            $profile->delivery_country = $input['delivery_country'];
            $profile->save();
            $data = [
                'name' => $user->name,
                'email' => $user->email,
                'password' => $pass,
            ];
            $user->assignRole('patients');
            Mail::to($user->email)->send(new PatientMail($data));

            DB::commit();
            return redirect()->back()->with(['title' => 'Done', 'message' => 'Patient created successfully', 'type' => 'success']);
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Failed to create patient: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }


    public function getStaff(Request $request)
    {
        $user = auth()->user();
        $query = User::orderBy('created_at', 'DESC')->where('user_id', $user->id)->with('profile')->select('id', 'name', 'email', 'status', 'created_at');
        if ($request->has('status') && $request->filled('status') && in_array($request->status, ['active', 'inactive'])) {
            if ($request->status == 'active') {
                $query->where('status', 1);
            } else {
                $query->whereIn('status', [0, 2]);
            }
        }
        if ($request->has('search') && $request->input('search') != '') {
            $search = $request->input('search');
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%');
            });
        }
        if ($request->has('role') && in_array($request->role, ['doctor', 'receptionist'])) {
            $query->whereHas('profile', function ($q) use ($request) {
                $q->where('role', $request->role);
            });
        } else {
            $query->whereHas('profile', function ($q) use ($request) {
                $q->whereIn('role', ['doctor', 'receptionist']);
            });
        }
        $perPage = $request->has('per_page') ? (int) $request->per_page : 10;
        $staff = $query->paginate(10);
        return response()->json([
            'rows' => view('dashboard.clinic-admin.staff-list', compact('staff'))->render(),
            'pagination' => view('dashboard.components.pagination', ['paginator' => $staff, 'type' => $request->role ?? '',])->render()
        ]);
    }
    public function getClinicPatient(Request $request)
    {
        $user = auth()->user();
        $query = User::with(['createdBy', 'profile'])
            ->orderBy('created_at', 'DESC')
            ->where('clinic_id', $user->id);

        $query->whereHas('roles', function ($q) {
            $q->where('name', 'patients');
        });

        // Add search functionality
        if ($request->has('search') && $request->input('search') != null) {
            $search = $request->input('search');
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%');
            });
        }

        // Add status filter
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('status', $request->input('status'));
        }

        $perPage = $request->has('per_page') ? (int) $request->per_page : 30;
        $patients = $query->paginate($perPage);

        return response()->json([
            'rows' => view('dashboard.clinic-admin.patient-list', compact('patients'))->render(),
            'pagination' => view('dashboard.components.pagination', [
                'paginator' => $patients,
                'type' => 'patient',
            ])->render()
        ]);
    }
    public function getStaffPatients(Request $request)
    {
        $user = auth()->user();
        $query = User::orderBy('created_at', 'DESC')
            ->where('clinic_id', $user->hasRole('doctor') ? $user->id : $user->clinic_id)
            ->with('profile')
            ->select('id', 'name', 'email', 'status', 'created_at')
            ->whereHas('roles', function ($q) {
                $q->where('name', 'patients');
            });

        // // Handle status filter
        if ($request->status != null) {
            $query->where('status', $request->status);
        }

        // Handle search
        if ($request->search != '') {
            $query->where(function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('email', 'like', '%' . $request->search . '%');
            });
        }

        $perPage = $request->has('per_page') ? (int) $request->per_page : 10;
        $patients = $query->paginate($perPage);

        return response()->json([
            'rows' => view('dashboard.staff.patient-list', compact('patients'))->render(),
            'pagination' => view('dashboard.components.pagination', [
                'paginator' => $patients,
                'type' => 'patient',
            ])->render()
        ]);
    }

    public function searchPatients(Request $request)
    {
        $user = auth()->user();
        $query = $request->get('q');

        return User::with('profile')
            ->when($user->hasRole('doctor'), function ($q) use ($user) {
                return $q->where('user_id', $user->id);
            }, function ($q) use ($user) {
                return $q->where('clinic_id', $user->clinic_id);
            })
            ->whereHas('roles', function ($q) {
                $q->where('name', 'patients');
            })
            ->where('name', 'like', '%' . $query . '%')
            ->limit(10)
            ->get()
            ->map(function ($user) {
                $dob = optional($user->profile)->dob;
                $age = $dob ? \Carbon\Carbon::parse($dob)->age : null;

                return [
                    'id' => $user->id,
                    'text' => $user->name,
                    'name' => $user->name,
                    'email' => $user->email,
                    'age' => $age,
                    'gender' => optional($user->profile)->gender,
                    'phone' => optional($user->profile)->mobile_number,
                    'address' => optional($user->profile)->address,
                ];
            });
    }


    public function changeUserDetails(Request $request)
    {
        $input = $request->all();
        $user = User::findOrFail($request->userId);

        // Validation for cashback percentage if type is present
        if ($request->filled('type')) {
            $this->validate($request, [
                'cashback_percentage' => 'required|numeric|min:0|max:100'
            ], [
                'cashback_percentage.required' => 'Cashback percentage is required.',
                'cashback_percentage.numeric' => 'Cashback percentage must be a number.',
                'cashback_percentage.min' => 'Cashback percentage must be between 0 and 100.',
                'cashback_percentage.max' => 'Cashback percentage must be between 0 and 100.',
            ]);
        }
        // Handle payment type change
        if (!empty($input['type'])) {
             $newType = $input['type'];
            $currentType = UserType::getCurrentType($user->id);

            if ($currentType !== $newType) {
                // Apply 3-month change restriction (skip if first time)
                if (!is_null($currentType) && !UserType::canChangeType($user->id)) {
                    $lastChangeDate = UserType::getLatestChangeDate($user->id);
                    $nextAllowedChangeDate = $lastChangeDate->copy()->addMonths(3);

                    return response()->json([
                        'success' => false,
                        'message' => 'Payment type cannot be changed within 3 months of the last change.',
                        'last_changed_on' => $lastChangeDate->format('m d Y'),
                        'next_allowed_change_date' => $nextAllowedChangeDate->format('m d Y'),
                    ], 400);
                }

                // Save new type
                UserType::create([
                    'user_id' => $user->id,
                    'type' => $newType,
                ]);

                // Update user's type column (for backward compatibility)
                $user->type = $newType;
                // Send notification to user
                $this->sendNotification(
                    $user->id,
                    'Payment Type Updated',
                    'Your payment type has been updated to ' . $newType . ' by a Pharmacy Admin.'
                );

                // Send notification to clinic admin
                $this->sendPaymentTypeChangeNotification($user, $currentType, $newType);
            }

            // Remove from input to avoid re-updating
            unset($input['type']);
        }

        // Handle cashback percentage update
        if (
            $user->status == 1 &&
            isset($input['cashback_percentage']) &&
            $user->cashback_percentage != $input['cashback_percentage']
        ) {
            $this->sendNotification(
                $user->id,
                'Cashback Percentage Updated',
                'Your cashback percentage has been updated to ' . $input['cashback_percentage'] . '% by a Pharmacy Admin.'
            );
        }

        // Update user with remaining input
        $user->update($input);

        return response()->json([
            'success' => true,
            'message' => 'Record updated successfully!',
            'data' => $user,
        ]);
    }


    public function toggleStatus($slug)
    {
        $user = User::findOrFail($slug);
        if ($user->status == 1) {
            $user->status = 2;
        } else {
            $user->status = 1;
        }
        $user->save();
        return response()->json(['status' => 'success', 'new_status' => $user->status]);
    }

    public function bulkImport(Request $request)
    {
        $request->validate([
            'csvFile' => 'required|mimes:csv,txt|max:10240',
        ]);
        $csvFile = $request->file('csvFile');
        DB::beginTransaction();
        try {
            if (($handle = fopen($csvFile->getRealPath(), 'r')) !== false) {
                $inserted = 0;
                $updated = 0;
                $header = null;
                $authUserId = auth()->id();
                $plainPassword = 'nmdp7788';
                $password = Hash::make($plainPassword);
                while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                    if (!$header) {
                        $header = $row;
                        continue;
                    }
                    $record = array_combine($header, $row);
                    if (empty($record['email']) || !filter_var($record['email'], FILTER_VALIDATE_EMAIL)) {
                        continue;
                    }
                    $user = User::where('email', $record['email'])->first();
                    $userData = [
                        'name' => $record['first_name'] . ' ' . $record['last_name'],
                        'email' => $record['email'],
                        'user_id' => $authUserId,
                        'clinic_id' => $authUserId,
                        'password' => $password,
                    ];
                    $profileData = [
                        'dob' => Carbon::createFromFormat('Y-m-d', $record['dob'])->format('Y-m-d'),
                        'mobile_number' => $record['mobile'],
                        'gender' => $record['gender'],
                        'title' => $record['title'],
                        'address' => $record['address'],
                        'postal' => $record['postal'],
                        'city' => $record['city'],
                        'country' => $record['country'],
                        'delivery_address' => $record['delivery_address'],
                        'delivery_postal' => $record['delivery_postal'],
                        'delivery_city' => $record['delivery_city'],
                        'delivery_country' => $record['delivery_country'],
                    ];
                    if ($user) {
                        $user->update($userData);
                        $user->profile()->updateOrCreate(['user_id' => $user->id], $profileData);
                        $updated++;
                    } else {
                        $user = User::create($userData);
                        $user->profile()->create(array_merge($profileData, ['user_id' => $user->id]));
                        $user->assignRole('patients');
                        $inserted++;
                        SendUserImportEmail::dispatch($user, $plainPassword);
                        // Mail::to($user->email)->send(new UserImportedMail($user, $plainPassword));
                    }
                }
                fclose($handle);
                DB::commit();
                return response()->json([
                    'message' => "Import completed! {$inserted} records inserted and {$updated} records updated.",
                ]);
            }
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'There was an issue with the CSV file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send notification to clinic admin when payment type is changed
     */
    private function sendPaymentTypeChangeNotification($user, $oldType, $newType)
    {
        // Find the clinic admin for this user
        $clinicAdmin = null;

        if ($user->hasRole('patients')) {
            // For patients, find their clinic admin
            $clinicAdmin = User::find($user->clinic_id);
        } elseif ($user->hasRole('staff') || $user->hasRole('doctor')) {
            // For staff/doctors, find their clinic admin
            $clinicAdmin = User::find($user->user_id);
        } elseif ($user->hasRole('clinic_admin')) {
            // For clinic admin, they are their own admin (no notification needed)
            return;
        }

        // If no clinic admin found, don't send notification
        if (!$clinicAdmin || !$clinicAdmin->hasRole('clinic_admin')) {
            return;
        }

        // Create notification message
        $userName = $user->name;
        $userRole = $user->getRoleNames()->first();
        $title = 'Payment Type Changed';
        $message = "Payment type for {$userName} ({$userRole}) has been changed from {$oldType} to {$newType} by Pharmacy Admin.";

        // Send notification to clinic admin
        $this->sendNotification($clinicAdmin->id, $title, $message);
    }
}
