<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    protected $roleRoutes = [
        'admin' => '/admin/dashboard',
        'clinic_admin' => '/clinic-admin/dashboard',
        'doctor' => '/doctor/dashboard',
        'patient' => '/patient/dashboard',
        'staff' => '/staff/dashboard'
    ];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    // Admin Login Methods
    public function showAdminLoginForm()
    {
        return view('auth.admin.login');
    }

    public function adminLogin(Request $request)
    {
        return $this->handleLogin($request, 'admin');
    }

    // Clinic Admin Login Methods
    public function showClinicAdminLoginForm()
    {
        return view('auth.clinic.login');
    }

    public function clinicAdminLogin(Request $request)
    {
        return $this->handleLogin($request, 'clinic_admin');
    }

    // Doctor Login Methods
    public function showDoctorLoginForm()
    {
        return view('auth.doctor.login');
    }

    public function doctorLogin(Request $request)
    {
        return $this->handleLogin($request, 'doctor');
    }

    // Patient Login Methods
    public function showPatientLoginForm()
    {
        return view('auth.patient.login');
    }

    public function patientLogin(Request $request)
    {
        return $this->handleLogin($request, 'patient');
    }

    // Staff Login Methods
    public function showStaffLoginForm()
    {
        return view('auth.staff.login');
    }

    public function staffLogin(Request $request)
    {
        return $this->handleLogin($request, 'staff');
    }

    // Common login handling method
    protected function handleLogin(Request $request, $role)
    {
        $this->validateLogin($request);

        if ($this->attemptLogin($request, $role)) {
            return $this->sendLoginResponse($request);
        }

        return $this->sendFailedLoginResponse($request, $role);
    }

    protected function attemptLogin(Request $request, $role)
    {
        $credentials = $request->only('email', 'password');
        $credentials['status'] = 1; // Only allow active users

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // Check if user has the correct role
            if ($this->checkUserRole($user, $role)) {
                return true;
            }

            // If user doesn't have the correct role, logout and return false
            Auth::logout();
            return false;
        }

        return false;
    }

    protected function checkUserRole($user, $role)
    {
        // Convert role names to match the database
        $roleMap = [
            'admin' => 'admin',
            'clinic_admin' => 'clinic_admin',
            'doctor' => 'doctor',
            'patient' => 'patients',
            'staff' => 'staff'
        ];

        $expectedRole = $roleMap[$role] ?? $role;
        return $user->hasRole($expectedRole);
    }

    protected function sendFailedLoginResponse(Request $request, $role)
    {
        throw ValidationException::withMessages([
            'email' => [trans('auth.failed')],
        ]);
    }

    protected function authenticated(Request $request, $user)
    {
        return redirect()->intended('/home');
    }

    protected function getRoleFromRequest(Request $request)
    {
        $path = $request->path();
        if (strpos($path, 'admin/login') !== false) return 'admin';
        if (strpos($path, 'clinic/login') !== false) return 'clinic_admin';
        if (strpos($path, 'doctor/login') !== false) return 'doctor';
        if (strpos($path, 'patient/login') !== false) return 'patient';
        if (strpos($path, 'staff/login') !== false) return 'staff';
        return null;
    }

    public function logout(Request $request)
    {
        $this->guard()->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        return redirect('/');
    }
}
