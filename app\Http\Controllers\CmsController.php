<?php

namespace App\Http\Controllers;
use App\Models\AboutCms;
use App\Models\complaintsCms;
use App\Models\ContactCms;
use App\Models\FeatureCms;
use App\Models\HomeCms;
use App\Models\OrderTermsCms;
use App\Models\OurPatientCms;
use App\Models\OurTeamCms;
use App\Models\PrescribersCms;
use App\Models\TestimonialCms;
use Illuminate\Http\Request;
use App\Models\WhyUsCms;
use Illuminate\Support\Facades\Storage;
use App\Models\PrivacyPolicy;
use App\Models\PrivacyPolicyCms;
use App\Models\RegulatoryInformationCms;
use App\Models\TermsConditionCms;
use Illuminate\Support\Facades\DB;

class CmsController extends Controller
{
    public function cms(){
        return view('cms.index');
    }
    public function homeCms()
    {
        $home_cms = HomeCms::first();
        $section_one_cards = FeatureCms::where('slug', 'section_one')->where('page', 'home')->get();
        $section_two_cards = FeatureCms::where('slug', 'section_two')->where('page', 'home')->get();
        $section_three_cards = FeatureCms::where('slug', 'section_three')->where('page', 'home')->get();
        $section_four_cards = FeatureCms::where('slug', 'section_four')->where('page', 'home')->get();
        $section_seven_cards = FeatureCms::where('slug', 'section_seven')->where('page', 'home')->get();
        $testimonials = TestimonialCms::all();
        return view('cms.home', compact(
            'testimonials',
            'home_cms',
            'section_one_cards',
            'section_two_cards',
            'section_three_cards',
            'section_four_cards',
            'section_seven_cards',
            'testimonials'
        ));
    }
    public function homeCmsSubmit(Request $request)
    {
        try {
            DB::beginTransaction();

            // Get or create home_cms record
            $home_cms = HomeCms::first() ?? new HomeCms();

            // Update home_cms with all fields except banner_image
            $home_cms->fill($request->except('banner_image'));

            // Handle banner image upload
            if ($request->hasFile('banner_image')) {
                $banner_image = $request->file('banner_image');
                $banner_image_name = time() . '_' . $banner_image->getClientOriginalName();
                $banner_image->move(public_path('website'), $banner_image_name);
                $home_cms->banner_image = $banner_image_name;
            }

            $home_cms->save();

            // Process section features
            $processedFeatureIds = [];

            // Process feature
            if ($request->has('feature')) {
                foreach ($request->feature as $index => $feature) {
                    $existingFeature = is_numeric($index) ? FeatureCms::find($index) : null;

                    if ($existingFeature) {
                        // Update existing feature
                        $existingFeature->title = $feature['title'] ?? '';
                        $existingFeature->description = $feature['description'] ?? '';
                        $existingFeature->save();
                        $processedFeatureIds[] = $existingFeature->id;
                    } else {
                        // Create new feature
                        $newFeature = new FeatureCms();
                        $newFeature->title = $feature['title'] ?? '';
                        $newFeature->description = $feature['description'] ?? '';
                        $newFeature->slug = 'section_one';
                        $newFeature->page = 'home';
                        $newFeature->save();
                        $processedFeatureIds[] = $newFeature->id;
                    }
                }
            }

            // Process section_two_feature
            if ($request->has('section_two_feature')) {
                foreach ($request->section_two_feature as $index => $feature) {
                    $existingFeature = is_numeric($index) ? FeatureCms::find($index) : null;

                    if ($existingFeature) {
                        // Update existing feature
                        $existingFeature->title = $feature['title'] ?? '';
                        $existingFeature->description = $feature['description'] ?? '';

                        // Handle image upload for existing feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($existingFeature->image) {
                                Storage::disk('website')->delete($existingFeature->image);
                            }
                            $image = $feature['image']->store('cms', 'website');
                            $existingFeature->image = $image;
                        }

                        $existingFeature->save();
                        $processedFeatureIds[] = $existingFeature->id;
                    } else {
                        // Create new feature
                        $newFeature = new FeatureCms();
                        $newFeature->title = $feature['title'] ?? '';
                        $newFeature->description = $feature['description'] ?? '';
                        $newFeature->slug = 'section_two';
                        $newFeature->page = 'home';

                        // Handle image upload for new feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            $image = $feature['image']->store('cms', 'website');
                            $newFeature->image = $image;
                        }

                        $newFeature->save();
                        $processedFeatureIds[] = $newFeature->id;
                    }
                }
            }

            // Process section_three_feature
            if ($request->has('section_three_feature')) {
                foreach ($request->section_three_feature as $index => $feature) {
                    $existingFeature = is_numeric($index) ? FeatureCms::find($index) : null;

                    if ($existingFeature) {
                        // Update existing feature
                        $existingFeature->title = $feature['title'] ?? '';
                        $existingFeature->description = $feature['description'] ?? '';

                        // Handle image upload for existing feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($existingFeature->image) {
                                Storage::disk('website')->delete($existingFeature->image);
                            }
                            $image = $feature['image']->store('cms', 'website');
                            $existingFeature->image = $image;
                        }

                        $existingFeature->save();
                        $processedFeatureIds[] = $existingFeature->id;
                    } else {
                        // Create new feature
                        $newFeature = new FeatureCms();
                        $newFeature->title = $feature['title'] ?? '';
                        $newFeature->description = $feature['description'] ?? '';
                        $newFeature->slug = 'section_three';
                        $newFeature->page = 'home';

                        // Handle image upload for new feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            $image = $feature['image']->store('cms', 'website');
                            $newFeature->image = $image;
                        }

                        $newFeature->save();
                        $processedFeatureIds[] = $newFeature->id;
                    }
                }
            }

            // Process section_four_feature
            if ($request->has('section_four_feature')) {
                foreach ($request->section_four_feature as $index => $feature) {
                    $existingFeature = is_numeric($index) ? FeatureCms::find($index) : null;

                    if ($existingFeature) {
                        // Update existing feature
                        $existingFeature->title = $feature['title'] ?? '';
                        $existingFeature->description = $feature['description'] ?? '';

                        // Handle image upload for existing feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($existingFeature->image) {
                                Storage::disk('website')->delete($existingFeature->image);
                            }
                            $image = $feature['image']->store('cms', 'website');
                            $existingFeature->image = $image;
                        }

                        $existingFeature->save();
                        $processedFeatureIds[] = $existingFeature->id;
                    } else {
                        // Create new feature
                        $newFeature = new FeatureCms();
                        $newFeature->title = $feature['title'] ?? '';
                        $newFeature->description = $feature['description'] ?? '';
                        $newFeature->slug = 'section_four';
                        $newFeature->page = 'home';

                        // Handle image upload for new feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            $image = $feature['image']->store('cms', 'website');
                            $newFeature->image = $image;
                        }

                        $newFeature->save();
                        $processedFeatureIds[] = $newFeature->id;
                    }
                }
            }

            // Process section seven features
            if ($request->has('section_seven_feature')) {
                foreach ($request->section_seven_feature as $index => $feature) {
                    $existingFeature = is_numeric($index) ? FeatureCms::find($index) : null;

                    if ($existingFeature) {
                        // Update existing feature
                        $existingFeature->title = $feature['title'] ?? '';
                        $existingFeature->description = $feature['description'] ?? '';

                        // Handle image upload for existing feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($existingFeature->image) {
                                Storage::disk('website')->delete($existingFeature->image);
                            }
                            $image = $feature['image']->store('cms', 'website');
                            $existingFeature->image = $image;
                        }

                        $existingFeature->save();
                        $processedFeatureIds[] = $existingFeature->id;
                    } else {
                        // Create new feature
                        $newFeature = new FeatureCms();
                        $newFeature->title = $feature['title'] ?? '';
                        $newFeature->description = $feature['description'] ?? '';
                        $newFeature->slug = 'section_seven';
                        $newFeature->page = 'home';

                        // Handle image upload for new feature
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            $image = $feature['image']->store('cms', 'website');
                            $newFeature->image = $image;
                        }

                        $newFeature->save();
                        $processedFeatureIds[] = $newFeature->id;
                    }
                }
            }

            // Process section_six_feature (testimonials)
            $processedTestimonialIds = [];
            if ($request->has('section_six_feature')) {
                foreach ($request->section_six_feature as $index => $feature) {
                    $existingTestimonial = is_numeric($index) ? TestimonialCms::find($index) : null;

                    if ($existingTestimonial) {
                        // Update existing testimonial
                        $existingTestimonial->description = $feature['description'] ?? '';
                        $existingTestimonial->name = $feature['name'] ?? '';
                        $existingTestimonial->designation = $feature['designation'] ?? '';
                        $existingTestimonial->rating = $feature['rating'] ?? 0;

                        // Handle image upload for existing testimonial
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            // Delete old image if exists
                            if ($existingTestimonial->image) {
                                Storage::disk('website')->delete($existingTestimonial->image);
                            }
                            $image = $feature['image']->store('cms', 'website');
                            $existingTestimonial->image = $image;
                        }

                        $existingTestimonial->save();
                        $processedTestimonialIds[] = $existingTestimonial->id;
                    } else {
                        // Create new testimonial
                        $newTestimonial = new TestimonialCms();
                        $newTestimonial->description = $feature['description'] ?? '';
                        $newTestimonial->name = $feature['name'] ?? '';
                        $newTestimonial->designation = $feature['designation'] ?? '';
                        $newTestimonial->rating = $feature['rating'] ?? 0;

                        // Handle image upload for new testimonial
                        if (isset($feature['image']) && $feature['image'] instanceof \Illuminate\Http\UploadedFile) {
                            $image = $feature['image']->store('cms', 'website');
                            $newTestimonial->image = $image;
                        }

                        $newTestimonial->save();
                        $processedTestimonialIds[] = $newTestimonial->id;
                    }
                }

                // Clean up old testimonials that are no longer present
                TestimonialCms::whereNotIn('id', $processedTestimonialIds)->delete();
            }

            // Clean up old features that are no longer present (excluding section_six which is handled by TestimonialCms)
            FeatureCms::whereIn('slug', ['section_one', 'section_two', 'section_three', 'section_four', 'section_seven'])
                ->where('page', 'home')
                ->whereNotIn('id', $processedFeatureIds)
                ->delete();

            DB::commit();
            return redirect()->back()->with('message', 'Data updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Error updating data: ' . $e->getMessage());
        }
    }
    public function aboutCms()
    {
        $about_cms = AboutCms::first();
        $section_one_cards = FeatureCms::where('slug', 'section_one')->where('page', 'about')->get();
        $section_two_cards = OurTeamCms::all();
        return view('cms.about', compact(
            'about_cms',
            'section_one_cards',
            'section_two_cards'
        ));
    }
    public function aboutCmsSubmit(Request $request)
    {
        $about_cms = AboutCms::first();
        $about_cms->update($request->all());
        $processedFeatureIds = [];
        foreach ($request->feature ?? [] as $index => $feature) {
            $existingFeature = FeatureCms::find($index);
            $image = $existingFeature->image ?? null;
            if ($request->hasFile("feature.$index.image")) {
                if(isset($existingFeature->image))
                {
                    Storage::disk('website')->delete($existingFeature->image);
                }
                $image = $request->file("feature.$index.image")->store('cms', 'website');
            }
            if ($existingFeature) {
                $existingFeature->update([
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = FeatureCms::create([
                    'image' => $image,
                    'slug' => 'section_one',
                    'page' => 'about',
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        //section two
        foreach ($request->section_two_feature ?? [ ] as $index => $feature) {
            $existingFeature = OurTeamCms::find($index);
            $image = $existingFeature->image ?? null;
            if ($request->hasFile("section_two_feature.$index.image")) {
                if(isset($existingFeature->image))
                {
                    Storage::disk('website')->delete($existingFeature->image);
                }
                $image = $request->file("section_two_feature.$index.image")->store('cms', 'website');
            }
            $social_icon_one_image = $existingFeature->social_icon_one_image ?? null;
            if ($request->hasFile("section_two_feature.$index.social_icon_one_image")) {
                Storage::disk('website')->delete($existingFeature->social_icon_one_image);
                $social_icon_one_image = $request->file("section_two_feature.$index.social_icon_one_image")->store('cms', 'website');
            }
            $social_icon_two_image = $existingFeature->social_icon_two_image ?? null;
            if ($request->hasFile("section_two_feature.$index.social_icon_two_image")) {
                Storage::disk('website')->delete($existingFeature->social_icon_two_image);
                $social_icon_two_image = $request->file("section_two_feature.$index.social_icon_two_image")->store('cms', 'website');
            }
            $social_icon_three_image = $existingFeature->social_icon_three_image ?? null;
            if ($request->hasFile("section_two_feature.$index.social_icon_three_image")) {
                Storage::disk('website')->delete($existingFeature->social_icon_three_image);
                $social_icon_three_image = $request->file("section_two_feature.$index.social_icon_three_image")->store('cms', 'website');
            }
            $social_icon_four_image = $existingFeature->social_icon_four_image ?? null;
            if ($request->hasFile("section_two_feature.$index.social_icon_four_image")) {
                Storage::disk('website')->delete($existingFeature->social_icon_four_image);
                $social_icon_four_image = $request->file("section_two_feature.$index.social_icon_four_image")->store('cms', 'website');
            }
            if ($existingFeature) {
                $existingFeature->update([
                    'image' => $image,
                    'social_icon_one_image' => $social_icon_one_image,
                    'social_icon_two_image' => $social_icon_two_image,
                    'social_icon_three_image' => $social_icon_three_image,
                    'social_icon_four_image' => $social_icon_four_image,
                    'social_icon_one_link' => $feature['social_icon_one_link'],
                    'social_icon_two_link' => $feature['social_icon_two_link'],
                    'social_icon_three_link' => $feature['social_icon_three_link'],
                    'social_icon_four_link' => $feature['social_icon_four_link'],
                    'title' => $feature['title'],
                    'subtitle' => $feature['subtitle'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = OurTeamCms::create([
                    'social_icon_one_image' => $social_icon_one_image,
                    'social_icon_two_image' => $social_icon_two_image,
                    'social_icon_three_image' => $social_icon_three_image,
                    'social_icon_four_image' => $social_icon_four_image,
                    'social_icon_one_link' => $feature['social_icon_one_link'],
                    'social_icon_two_link' => $feature['social_icon_two_link'],
                    'social_icon_three_link' => $feature['social_icon_three_link'],
                    'social_icon_four_link' => $feature['social_icon_four_link'],
                    'title' => $feature['title'],
                    'subtitle' => $feature['subtitle'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        OurTeamCms::whereNotIn('id', $processedFeatureIds)
            ->delete();
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Home Updated Successfully!']);
    }
    public function contactCms()
    {
        $contact_cms = ContactCms::first();
        return view('cms.contact', compact(
            'contact_cms'
        ));
    }
    public function regulatoryInformation()
    {
        $regulatory_info = RegulatoryInformationCms::first();
        return view('cms.regulatory_information', compact('regulatory_info'));
    }

    public function contactCmsSubmit(Request $request)
    {
        $contact_cms = ContactCms::first();
        $contact_cms->update($request->all());
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Contact page Updated Successfully!']);
    }
    public function whyusCms()
    {
        $why_us_cms = WhyUsCms::first();
        $section_cards = FeatureCms::where('page', 'why_us')->get();
        return view('cms.why_us', compact(
            'why_us_cms',
            'section_cards'
        ));
    }
    public function whyusCmsSubmit(Request $request)
    {
        $why_us_cms = WhyUsCms::first();
        $why_us_cms->update($request->all());
        $processedFeatureIds = [];
        foreach ($request->section_two_feature as $index => $feature) {
            $existingFeature = FeatureCms::find($index);
            $icon_image = $existingFeature->icon_image ?? null;
            if ($request->hasFile("section_two_feature.$index.icon_image")) {
                if(isset($existingFeature->image))
                {
                Storage::disk('website')->delete($existingFeature->icon_image);
                }
                $icon_image = $request->file("section_two_feature.$index.icon_image")->store('cms', 'website');
            }
            $image = $existingFeature->image ?? null;
            if ($request->hasFile("section_two_feature.$index.image")) {
                if(isset($existingFeature->image))
                {
                    Storage::disk('website')->delete($existingFeature->image);
                }
                $image = $request->file("section_two_feature.$index.image")->store('cms', 'website');
            }
            if ($existingFeature) {
                $existingFeature->update([

                    'icon_image' => $icon_image,
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = FeatureCms::create([
                    'page' => 'why_us',
                    'icon_image' => $icon_image,
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        FeatureCms::whereNotIn('id', $processedFeatureIds)->where('page','why_us')->delete();
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Home Updated Successfully!']);
    }
    public function prescribersCms()
    {
        $prescriber_cms = PrescribersCms::first();
        $section_cards = FeatureCms::where('page', 'prescribers')->get();
        return view( 'cms.prescribers', compact(
            'prescriber_cms',
            'section_cards'
        ));
    }
    public function prescribersCmsSubmit(Request $request)
    {
        $prescriber_cms = PrescribersCms::first();
        $prescriber_cms->update([
            'title' => $request->title,
            'section_one_title' => $request->section_one_title,
            'section_one_description' => $request->section_one_description,
            'section_two_title' => $request->section_two_title,
            'section_two_description' => $request->section_two_description,
        ]);
        $processedFeatureIds = [];
        foreach ($request->section_two_feature as $index => $feature) {
            $existingFeature = FeatureCms::find($index);
            $image = $existingFeature->image ?? null;
            if ($request->hasFile("section_two_feature.$index.image")) {
                if(isset($existingFeature->image))
                {
                    Storage::disk('website')->delete($existingFeature->image);
                }
                $image = $request->file("section_two_feature.$index.image")->store('cms', 'website');
            }
            if ($existingFeature) {
                $existingFeature->update([
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = FeatureCms::create([
                    'page' => 'prescribers',
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        FeatureCms::whereNotIn('id', $processedFeatureIds)->where('page','prescribers')->delete();
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Home Updated Successfully!']);
    }
    public function OurPatientsCms()
    {
        $our_patients = OurPatientCms::first();
        $section_cards = FeatureCms::where('page', 'our_patients')->where('slug','section_one')->get();
        $section_two_cards = FeatureCms::where('page', 'our_patients')->where('slug','section_two')->get();
        return view('cms.our_patients', compact(
            'our_patients',
            'section_cards',
            'section_two_cards'
        ));
    }
     public function OurPatientsCmsSubmit(Request $request)
    {
        $our_patients = OurPatientCms::first();
        $our_patients->update([
            'title' => $request->title,
            'section_two_title' => $request->section_two_title,
        ]);
        $processedFeatureIds = [];
        foreach ($request->section_two_feature ?? [] as $index => $feature) {
            $existingFeature = FeatureCms::find($index);
            $icon_image = $existingFeature->icon_image ?? null;
            if ($request->hasFile("section_two_feature.$index.icon_image")) {
                Storage::disk('website')->delete($existingFeature->icon_image);
                $icon_image = $request->file("section_two_feature.$index.icon_image")->store('cms', 'website');
            }
            $image = $existingFeature->image ?? null;
            if ($request->hasFile("section_two_feature.$index.image")) {
                if(isset($existingFeature->image))
                {
                    Storage::disk('website')->delete($existingFeature->image);
                }
                $image = $request->file("section_two_feature.$index.image")->store('cms', 'website');
            }
            if ($existingFeature) {
                $existingFeature->update([
                    'icon_image' => $icon_image,
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = FeatureCms::create([
                    'page' => 'our_patients',
                    'slug' => 'section_one',
                    'icon_image' => $icon_image,
                    'image' => $image,
                    'title' => $feature['title'],
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        foreach ($request->section_three_feature ?? [] as $index => $feature) {
            $existingFeature = FeatureCms::find($index);
            if ($existingFeature) {
                $existingFeature->update([
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $existingFeature->id;
            } else {
                $newFeature = FeatureCms::create([
                    'page' => 'our_patients',
                    'slug' => 'section_two',
                    'description' => $feature['description'],
                ]);
                $processedFeatureIds[] = $newFeature->id;
            }
        }
        FeatureCms::whereNotIn('id', $processedFeatureIds)->where('page','our_patients')->delete();
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Home Updated Successfully!']);
    }

    public function storePrivacyPolicy(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string'
        ]);

        $privacy_policy = PrivacyPolicyCms::first();

        if ($privacy_policy) {
            $privacy_policy->update($request->only(['title', 'description']));
        } else {
            PrivacyPolicyCms::create($request->only(['title', 'description']));
        }

        return redirect()->back()->with('message', 'Privacy Policy updated successfully');
    }
    public function privacyPolicy()
    {
        $privacy_policy = PrivacyPolicyCms::first();
        return view('cms.privacy_policy', compact(
            'privacy_policy'
        ));
    }

    public function storeRegulatoryInformation(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string'
        ]);

        $regulatory_info = RegulatoryInformationCms::first();

        if ($regulatory_info) {
            $regulatory_info->update($request->only(['title', 'description']));
        } else {
            RegulatoryInformationCms::create($request->only(['title', 'description']));
        }

        return view('cms.index')->with('message', 'Regulatory Information updated successfully');
    }
    public function ordersTerms()
    {
        $order_terms = OrderTermsCms::first();
        return view('cms.orders_terms', compact('order_terms'));
    }
    public function storeordersTerms(Request $request)
    {

        // $request->validate([
        //     'title' => 'required|string|max:255',
        //     'description' => 'required|string'
        // ]);
        $order_terms = OrderTermsCms::first();
        if ($order_terms) {
            $order_terms->update($request->only(['title', 'description']));
        } else {
            OrderTermsCms::create($request->only(['title', 'description']));
        }
        return view('cms.index')->with('message', 'Order Terms updated successfully');
    }
    public function complaints()
    {
        $complaints = complaintsCms::first();
        return view('cms.complaints', compact('complaints'));
    }
    public function storeComplaints(Request $request)
    {
        $order_terms = complaintsCms::first();
        if ($order_terms) {
            $order_terms->update($request->only(['title', 'description']));
        } else {
            complaintsCms::create($request->only(['title', 'description']));
        }
        return view('cms.index')->with(['message' =>'Complaints updated successfully']);
    }
    public function termsConditions()
    {
        $terms_conditions = TermsConditionCms::first();
        return view('cms.terms_and_condition', compact('terms_conditions'));
    }
    public function storeTermsConditions(Request $request)
    {

        // $request->validate([
        //     'title' => 'required|string|max:255',
        //     'description' => 'required|string'
        // ]);
        $order_terms = TermsConditionCms::first();
        if ($order_terms) {
            $order_terms->update($request->only(['title', 'description']));
        } else {
            TermsConditionCms::create($request->only(['title', 'description']));
        }
        return view('cms.index')->with(['message' =>'Terms and Conditions updated successfully']);
    }
}
