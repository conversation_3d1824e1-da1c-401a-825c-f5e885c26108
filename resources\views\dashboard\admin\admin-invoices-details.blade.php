@extends('theme.layout.master')

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <button id="backButton" class="button-gradient white-color roboto fs-14 fw-400 my-5">Back</button>
            <div class="row my-5">
                <div class="col-xxl-9 col-12">
                    <div class="custom_tabs">
                        <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                            <h3>Medicines</h3>
                            <div class="d-flex justify-content-end align-items-center gap-3">
                                <a href="{{ route('admin.invoice.download.medicines.pdf', ['id' => $invoice->id]) }}"
                                    class="badge badge-gradient fs-14 fw-400">Download PDF</a>
                                <a href="{{ route('admin.invoice.download.medicines', ['id' => $invoice->id]) }}"
                                    class="badge badge-gradient fs-14 fw-400">Download CSV</a>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table display custom-table gy-5 gs-5">
                                <thead>
                                    <tr>
                                        <th class="min-w-400px">Medicines</th>
                                        <th class="min-w-300px">Used</th>
                                        <th class="min-w-300px">Price</th>
                                    </tr>
                                </thead>
                                <tbody class="ps-5">
                                    @forelse ($uniqueMedicines as $detail)
                                        <tr>
                                            <td>{{ $detail->name }}</td>
                                            <td>{{ $detail->prescribed_units ?? '' }}</td>
                                            <td>£{{ $detail->total_price ?? '' }}</td>
                                        </tr>
                                    @empty
                                    @endforelse
                                    <tr class="fw-bold">
                                        <td>Total</td>
                                        <td></td>
                                        <td>£{{ $uniqueMedicines->sum('total_price') }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-xxl-3 col-12 mb-4">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-body p-0">
                            <!-- Header -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center py-3 ">
                                    @include('svg.prescriber-name')
                                    <span class="inter fs-12 fw-400 ps-2 deep-charcoal-blue">Prescriber Name</span>
                                </div>
                                <div class="d-flex gap-2 flex-wrap justify-content-end">
                                    <span id="is_paid_text"
                                        class="roboto fs-12 fw-400 medicines-status">{{ $invoice->is_paid == 0 ? 'Unpaid' : 'Paid' }}</span>
                                    <span id="is_acknow_text"
                                        class="roboto fs-12 fw-400 medicines-status"
                                        style="display: {{ $invoice->is_acknowledge == 1 ? 'inline-block' : 'none' }}">
                                        Acknowledged
                                    </span>
                                </div>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $invoice->user->name ?? '' }}</h6>
                            <p class="fs-12 fw-500 table-gary-text pt-3">Month</p>
                            <div class="d-flex align-items-baseline justify-content-between">
                                <span
                                    class="fs-5 fw-semibold me-1">{{ request()->get('month') ? ucfirst(request()->get('month')) : \Carbon\Carbon::parse($invoice->month)->format('F Y') }}</span>
                            </div>
                            <p class="fs-12 fw-500 table-gary-text pt-3">Total Amount</p>
                            <div class="d-flex align-items-baseline justify-content-between">
                                <span class="fs-5 fw-semibold me-1">£{{ $invoice->total_amount ?? '' }}</span>
                            </div>
                            <div class="d-flex justify-content-center flex-wrap gap-2">
                                @if (auth()->user()->hasRole('admin'))
                                    @if ($invoice->type == 'Type 1' && $invoice->is_paid == 0)
                                        <form id="update_status">
                                            <input type="hidden" name="id" value="{{ $invoice->id }}">
                                            <input value="1" type="hidden" name="is_paid">
                                            <button class="badge badge-gradient fs-14 fw-400" type="submit">Mark as Paid</button>
                                        </form>
                                    @elseif($invoice->type == 'Type 2' && $invoice->is_paid == 1 && $invoice->is_acknowledge == 0)
                                        <form id="update_status">
                                            <input type="hidden" name="id" value="{{ $invoice->id }}">
                                            <input value="1" type="hidden" name="is_acknowledge">
                                            <button class="badge badge-gradient fs-14 fw-400" type="submit">Mark as Acknowledge</button>
                                        </form>
                                    @endif
                                @elseif(auth()->user()->hasRole('clinic_admin'))
                                    @if ($invoice->type == 'Type 2' && $invoice->is_paid == 0)
                                        <form id="update_status">
                                            <input type="hidden" name="id" value="{{ $invoice->id }}">
                                            <input value="1" type="hidden" name="is_paid">
                                            <button class="badge badge-gradient fs-14 fw-400" type="submit">Mark as Paid</button>
                                        </form>
                                    @elseif($invoice->type == 'Type 1' && $invoice->is_paid == 1 && $invoice->is_acknowledge == 0)
                                        <form id="update_status">
                                            <input type="hidden" name="id" value="{{ $invoice->id }}">
                                            <input value="1" type="hidden" name="is_acknowledge">
                                            <button class="badge badge-gradient fs-14 fw-400" type="submit">Mark as Acknowledge</button>
                                        </form>
                                    @endif
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @if(auth()->user()->hasRole('clinic_admin'))
                <div class="custom_tabs">
                    <div>
                        <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                            <div>
                                <h3>Prescriptions</h3>
                            </div>
                            <div class="d-flex justify-content-end align-items-center gap-3">
                                <a href="{{ route('admin.invoice.download.prescriptions', ['id' => $invoice->id]) }}"
                                    class="badge badge-gradient fs-14 fw-400">Download CSV</a>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table display custom-table gy-5 gs-5">
                                <thead>
                                    <tr>
                                        <th class="min-w-200px">Order ID</th>
                                        <th class="min-w-200px">Patient Name</th>
                                        <th class="min-w-200px">Prescription Type</th>
                                        <th class="min-w-200px">Prescribe By</th>
                                        <th class="min-w-200px">Total Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="ps-5">
                                    @forelse ($invoice->prescriptions as $prescription)
                                        <tr>
                                            <td class="w-200px">{{ $prescription->order_id }}</td>
                                            <td class="w-200px">{{ $prescription->patient->name ?? 'Outsourced' }}</td>
                                            <td class="w-200px">{{ ucfirst($prescription->prescription_type) }}</td>
                                            <td class="w-200px">{{ $prescription->prescribedBy->name ?? '' }}</td>
                                            <td class="w-200px">£{{ $prescription->total }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center">No prescriptions found</td>
                                        </tr>
                                    @endforelse
                                    <tr class="fw-bold">
                                        <td class="w-200px">Total</td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px">£{{ $invoice->prescriptions->sum('total') }}</td>
                                    </tr>
                                    <tr class="fw-bold">
                                        <td class="w-200px">Total Cashback</td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px"></td>
                                        <td class="w-200px">£{{ $invoice->cashback_amount ?? 0 }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @endif
            </div>
            @if (!auth()->user()->hasRole('admin'))
                <div class="custom_tabs">
                    <div>
                        <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                            <div>
                                <h3>Cashback Summary</h3>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table display custom-table gy-5 gs-5">
                                <thead>
                                    <tr>
                                        <th class="w-200px">Prescriber Name</th>
                                        <th class="w-200px">Total Prescription Amount</th>
                                        <th class="w-200px">Cashback Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="ps-5">
                                    @php
                                        $cashbackSummary = $invoice->prescriptions->groupBy('user_id')->map(function ($prescriptions) {
                                            $totalAmount = $prescriptions->sum('total');
                                            $cashbackAmount = 0;

                                            // Separate prescriptions with and without clinic
                                            $clinicPrescriptions = $prescriptions->whereNotNull('clinic_id');
                                            $nonClinicPrescriptions = $prescriptions->whereNull('clinic_id');

                                            // Calculate cashback for clinic prescriptions
                                            $clinics = $clinicPrescriptions->map(function ($prescription) {
                                                return $prescription->clinic;
                                            })->unique('id');

                                            foreach ($clinics as $clinic) {
                                                if ($clinic && $clinic->cashback_percentage) {
                                                    $clinicPrescriptions = $prescriptions->where('clinic_id', $clinic->id);
                                                    $clinicTotal = $clinicPrescriptions->sum('total');
                                                    $cashbackAmount += ($clinicTotal * $clinic->cashback_percentage) / 100;
                                                }
                                            }

                                            // Calculate cashback for non-clinic prescriptions using user's cashback percentage
                                            if ($nonClinicPrescriptions->isNotEmpty()) {
                                                $user = $prescriptions->first()->prescribedBy;
                                                if ($user && $user->cashback_percentage) {
                                                    $nonClinicTotal = $nonClinicPrescriptions->sum('total');
                                                    $cashbackAmount += ($nonClinicTotal * $user->cashback_percentage) / 100;
                                                }
                                            }

                                            return [
                                                'prescriber' => $prescriptions->first()->prescribedBy->name ?? 'Outsourced',
                                                'total_amount' => $totalAmount,
                                                'cashback_amount' => $cashbackAmount
                                            ];
                                        });
                                    @endphp
                                    @forelse ($cashbackSummary as $summary)
                                        <tr>
                                            <td class="w-200px">{{ $summary['prescriber'] }}</td>
                                            <td class="w-200px">£{{ number_format($summary['total_amount'], 2) }}</td>
                                            <td class="w-200px">£{{ number_format($summary['cashback_amount'], 2) }}</td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="3" class="text-center">No cashback data found</td>
                                        </tr>
                                    @endforelse
                                    <tr class="fw-bold">
                                        <td class="w-200px">Total</td>
                                        <td class="w-200px">£{{ number_format($cashbackSummary->sum('total_amount'), 2) }}</td>
                                        <td class="w-200px">£{{ number_format($cashbackSummary->sum('cashback_amount'), 2) }}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function () {
            $('#update_status').on('submit', function (event) {
                event.preventDefault();
                let form = $(this);
                let formData = form.serialize();

                // Show confirmation modal for acknowledge action
                if (form.find('input[name="is_acknowledge"]').length > 0) {
                    Swal.fire({
                        title: 'Confirm Acknowledge',
                        text: 'Are you sure you want to acknowledge this invoice?',
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#3085d6',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Yes, acknowledge it!',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            submitForm(form, formData);
                        }
                    });
                } else {
                    submitForm(form, formData);
                }
            });

            function submitForm(form, formData) {
                $.ajax({
                    url: "{{ route('update.invoice.status') }}",
                    method: 'GET',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function (response) {
                        Swal.fire({
                            icon: response.icon,
                            title: response.message,
                            showConfirmButton: true,
                            timer: 2000
                        });

                        if (response.text == 'is_paid') {
                            $('#is_paid_text').text('Paid');
                            form.closest('.d-flex.justify-content-center').find('form').hide();
                        } else if (response.text == 'is_acknowledge') {
                            // Show the acknowledge tag
                            $('#is_acknow_text').show();
                            // Hide the acknowledge button
                            form.closest('.d-flex.justify-content-center').find('form').hide();
                        }
                    },
                    error: function (xhr) {
                        let response = xhr.responseJSON;
                        Swal.fire({
                            icon: response?.icon || 'error',
                            title: response?.message || 'Unexpected error',
                            showConfirmButton: true
                        });
                    }
                });
            }
        });
    </script>
@endpush
