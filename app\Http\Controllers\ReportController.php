<?php

namespace App\Http\Controllers;

use App\Models\Inventory;
use App\Models\Prescription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    public function statusFilter(Request $request)
    {
        $type_filter = $request->type_filter;
        $filter = $request->filter;
        $search = $request->search;
        $tab = ltrim($request->tab, '#');
        if (auth()->user()->hasRole('staff')) {
            $clinic_admin = User::find(auth()->user()->clinic_id);
        }
        if ($tab === 'patients') {
            if (auth()->user()->hasRole('admin')) {

                $query = User::patients();
            } elseif (auth()->user()->hasRole('doctor')) {
                $query = User::where('clinic_id', auth()->id())->whereHas('roles', function ($q) {
                    $q->where('name', 'patients');
                });
                // if (!is_null($type_filter)) {
                //     $query->where('type', $type_filter);
                // }
            } elseif (auth()->user()->hasRole('clinic_admin')) {
                $query = auth()->user()->clinicPatinets();
                // if (!is_null($type_filter)) {
                //     $query->whereHas('clinic', function ($q) use ($type_filter) {
                //         $q->where('type', $type_filter);
                //     });
                // }
            } elseif (auth()->user()->hasRole('staff')) {
                $query = $clinic_admin->clinicPatinets();
                // if (!is_null($type_filter)) {
                //     $query->whereHas('clinic', function ($q) use ($type_filter) {
                //         $q->where('type', $type_filter);
                //     });
                // }
            }
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        } elseif ($tab === 'Clinic-admin') {
            $query = User::clinicAdmins();
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
        } elseif ($tab === 'Prescribers') {

            $query = User::Doctors();

            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        } elseif ($tab === 'Receptionist') {
            if (auth()->user()->hasRole('clinic_admin')) {
                $query = auth()->user()->clinicStaff();
            } else {
                $query = User::staffs();
            }
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        } elseif ($tab === 'Prescriptions') {
            if (auth()->user()->hasRole('admin')) {
                $query = Prescription::query();
                if (!is_null($search)) {
                    $query->whereHas('patient', function ($q) use ($search) {
                        $q->where('name', 'like', '%' . $search . '%');
                    });
                }

            } elseif (auth()->user()->hasRole('doctor')) {
                $query = auth()->user()->ownPrescriptions();
                if (!is_null($search)) {
                    $query->where('name', 'like', '%' . $search . '%');
                }
            } elseif (auth()->user()->hasRole('clinic_admin')) {
                $query = auth()->user()->clinicPrescriptions();
                if (!is_null($search)) {
                    $query->where('name', 'like', '%' . $search . '%');
                }
            } elseif (auth()->user()->hasRole('staff')) {
                $query = $clinic_admin->clinicPrescriptions();
                if (!is_null($search)) {
                    $query->where('name', 'like', '%' . $search . '%');
                }
            }
            if (!is_null($filter)) {
                $query->where('admin_approval', (int) $filter);
            }
            if (!is_null($type_filter)) {
                $query->where('type', $type_filter);
            }
        } elseif ($tab === 'Medicines') {
            if (auth()->user()->hasRole('admin')) {
                $query = Inventory::query();
            } elseif (auth()->user()->hasRole('doctor')) {
                $query = auth()->user()->getUsedPrescriptionInventories();
            } elseif (auth()->user()->hasRole('clinic_admin')) {
                $query = auth()->user()->getUsedClinicInventories();
            } elseif (auth()->user()->hasRole('staff')) {
                $query = $clinic_admin->getUsedClinicInventories();
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        }
        if ($request->filled('month')) {
            $month = strtolower($request->month);  // Convert to lowercase for consistency

            // Get the first and last date of the selected month
            $startOfMonth = Carbon::parse("first day of {$month}")->startOfDay();
            $endOfMonth = Carbon::parse("last day of {$month}")->endOfDay();

            $query->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
        } elseif ($request->filled('start_date')) {
            $endDate = $request->filled('end_date')
                ? $request->end_date
                : now()->toDateString();
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
        }
        $results = $query->orderBy('created_at', 'DESC')->paginate(10);
        return view('reports.users', compact('results', 'tab'))->render();
    }
    public function exportCsv($type = null)
    {
        $filename = $type . '_report.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        $handle = fopen('php://output', 'w');
        if (auth()->user()->hasRole('staff')) {
            $clinic_admin = User::find(auth()->user()->clinic_id);
        }
        // Initialize dataset
        $data = collect();

        // Header row
        switch ($type) {
            case 'patients':
                if (auth()->user()->hasRole('admin')) {
                    $data = User::patients()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('doctor')) {
                    $data = auth()->user()->clinicStaff()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('clinic_admin')) {
                    $data = auth()->user()->clinicPatinets()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('staff')) {
                    $data = $clinic_admin->clinicPatinets()->orderBy('created_at', 'desc')->get();
                }
                fputcsv($handle, [
                    'Full Name',
                    'Gender',
                    'Mobile Number',
                    'Date of Birth',
                    'Email',
                    'Home Address',
                    'Postcode',
                    'Country',
                    'City',
                    'Delivery Address',
                ]);
                break;

            case 'clinic_admins':
                $data = User::clinicAdmins()->orderBy('created_at', 'desc')->get();
                fputcsv($handle, ['Name', 'Type', 'Date Requested', 'Status']);
                break;

            case 'doctors':
                $data = User::doctors()->orderBy('created_at', 'desc')->get();
                fputcsv($handle, ['Name', 'Type', 'Date Requested', 'Status']);
                break;

            case 'staffs':
                if (auth()->user()->hasRole('clinic_admin')) {
                    $data = auth()->user()->clinicStaff()->orderBy('created_at', 'DESC')->get();
                } elseif (auth()->user()->hasRole('staff')) {
                    $data = $clinic_admin->clinicStaff()->orderBy('created_at', 'DESC')->get();
                } else {
                    $data = User::staffs()->orderBy('created_at', 'desc')->get();
                }
                fputcsv($handle, ['Name', 'Created By', 'Date Requested', 'Status']);
                break;

            case 'prescriptions':
                if (auth()->user()->hasRole('admin')) {
                    $data = Prescription::orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('doctor')) {
                    $data = auth()->user()->ownPrescriptions()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('clinic_admin')) {
                    $data = auth()->user()->clinicPrescriptions()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('staff')) {
                    $data = $clinic_admin->clinicPrescriptions()->orderBy('created_at', 'desc')->get();
                }
                // $data = Prescription::orderBy('created_at', 'desc')->get();
                fputcsv($handle, ['Name', 'Prescribed By', 'Date Requested', 'Status']);
                break;

            case 'medicines':
                if (auth()->user()->hasRole('doctor')) {
                    $data = auth()->user()->getUsedPrescriptionInventories()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('admin')) {
                    $data = Inventory::orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('clinic_admin')) {
                    $data = auth()->user()->getUsedClinicInventories()->orderBy('created_at', 'desc')->get();
                } elseif (auth()->user()->hasRole('staff')) {
                    $data = $clinic_admin->getUsedClinicInventories()->orderBy('created_at', 'desc')->get();
                }
                // $data = Inventory::orderBy('created_at', 'desc')->get();
                fputcsv($handle, ['Name', 'Pack Size', 'Medicine Used', 'Cost Price', 'Sale Price']);
                break;

            default:
                echo "Invalid export type.";
                fclose($handle);
                exit;
        }
        // Data rows
        foreach ($data as $row) {
            switch ($type) {
                case 'patients':
                    fputcsv($handle, [
                        $row->name,
                        $row->profile->gender ?? '',
                        $row->profile->mobile_number ?? '',
                        $row->profile->dob ? date('d/m/Y', strtotime($row->profile->dob)) : '',
                        $row->email,
                        $row->profile->address ?? '',
                        $row->profile->postal ?? '',
                        $row->profile->country ?? '',
                        $row->profile->city ?? '',
                        $row->profile->delivery_address ?? '',
                    ]);
                    break;

                case 'clinic_admins':
                case 'doctors':
                    fputcsv($handle, [
                        $row->name,
                        $row->type,
                        $row->created_at->format('M d Y'),
                        $row->statusText,
                    ]);
                    break;

                case 'staffs':
                    fputcsv($handle, [
                        $row->name,
                        optional($row->createdBy)->name,
                        $row->created_at->format('M d Y'),
                        $row->statusText,
                    ]);
                    break;

                case 'prescriptions':
                    fputcsv($handle, [
                        $row->prescription,
                        optional($row->prescribedBy)->name,
                        $row->created_at->format('M d Y'),
                        $row->adminApprovalText,
                    ]);
                    break;

                case 'medicines':
                    fputcsv($handle, [
                        $row->name,
                        $row->pack_size,
                        optional($row->prescriptions)->count() ?? 0,
                        $row->cost_price,
                        $row->sale_price,
                    ]);
                    break;
            }
        }

        fclose($handle);
        exit;
    }
    public function userDetailFilter(Request $request)
    {
        $user = User::findOrFail($request->user_id);
        $type_filter = $request->type_filter;
        $filter = $request->filter;
        $search = $request->search;
        $tab = ltrim($request->tab, '#');

        if ($tab === 'patients') {
            $query = $user->clinicPatinets();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }

            $results = $query->orderBy('created_at', 'DESC')->paginate(5);
        } elseif ($tab === 'prescriptions') {
            $query = $user->clinicPrescriptions();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->whereHas('patient', function ($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%');
                });
            }
            if (!is_null($type_filter)) {
                $query->where('prescription_type', $type_filter);
            }
        } elseif ($tab === 'prescriber') {
            $query = $user->clinicStaff()->StaffDoctors();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        } elseif ($tab === 'staff') {
            $query = $user->clinicStaff()->StaffReceptionists();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        }
        // elseif
        if ($request->filled('start_date')) {
            $endDate = $request->filled('end_date') ? $request->end_date : now()->toDateString();
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
        }
        $results = $query->orderBy('created_at', 'DESC')->paginate(10);
        return view('ajax.user_detail', compact('results', 'tab', 'user'))->render();
    }
    public function patientPrescriptionFilter(Request $request)
    {
        $user = User::findOrFail($request->user_id);
        $type_filter = $request->type_filter;
        $filter = $request->filter;
        $search = $request->search;
        $tab = ltrim($request->tab, '#');

        if ($tab === 'patients') {
            $query = $user->clinicPatinets();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }

            $results = $query->orderBy('created_at', 'DESC')->paginate(5);
        } elseif ($tab === 'prescriptions') {
            $query = $user->patientPrescriptions();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->whereHas('patient', function ($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%');
                });
            }
            if (!is_null($type_filter)) {
                $query->where('prescription_type', $type_filter);
            }
        } elseif ($tab === 'prescriber') {
            $query = $user->clinicStaff()->StaffDoctors();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        } elseif ($tab === 'staff') {
            $query = $user->clinicStaff()->StaffReceptionists();
            if (!is_null($filter)) {
                $filter = (int) $filter;
                $filter === 0
                    ? $query->whereIn('status', [0, 2])
                    : $query->where('status', $filter);
            }
            if (!is_null($search)) {
                $query->where('name', 'like', '%' . $search . '%');
            }
        }
        // elseif
        if ($request->filled('start_date')) {
            $endDate = $request->filled('end_date') ? $request->end_date : now()->toDateString();
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
        }
        $results = $query->orderBy('created_at', 'DESC')->paginate(5);
        return view('ajax.user_detail', compact('results', 'tab', 'user'))->render();
    }
}
