<nav class="navbar navbar-expand-xl navbar-header">
    <div class="container py-2 container-navbar">
        <a class="navbar-brand" href="<?php echo e(url('/')); ?>">
            <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->website_logo ?? ''); ?>"
                class="h-50px app-sidebar-logo-default" />
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent"
            aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <ul class="navbar-nav mx-auto mb-5 mb-xl-0 navbar-items">
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 <?php if(request()->is('about-us')): ?> active <?php endif; ?>"
                        aria-current="page" href="<?php echo e(url('/about-us')); ?>">About Us</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 <?php if(request()->is('why-us')): ?> active <?php endif; ?>"
                        aria-current="page" href="<?php echo e(url('/why-us')); ?>">Why Us</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link  white-color fs-16 <?php if(request()->is('prescribers')): ?> active <?php endif; ?>"
                        aria-current="page" href="<?php echo e(url('prescribers')); ?>">Prescribers</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 <?php if(request()->routeIs('our.patients')): ?> active <?php endif; ?>"
                        aria-current="page" href="<?php echo e(route('our.patients')); ?>">Patients</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link white-color fs-16 <?php if(request()->is('contact-us')): ?> active <?php endif; ?>"
                        aria-current="page" href="<?php echo e(url('contact-us')); ?>">Contact Us</a>
                </li>
                <li class="nav-item btn_nav ">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="d-flex flex-md-nowrap flex-wrap row-gap-5">
                            <a class="button_login login-btn me-5" href="<?php echo e(url('logout')); ?>">Sign out</a>
                            <a class="button_login button1 login-btn" href="<?php echo e(url('home')); ?>">Dashboard</a>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(url('register')); ?>" class="button_login button1 login-btn">Sign up</a>
                        <div class="dropdown">
                            <a class="button_login button1 white-color fs-16 dropdown-toggle" href="#" role="button"
                                id="loginDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                Login
                            </a>
                            <ul class="header-login-menu dropdown-menu" aria-labelledby="loginDropdown">
                                <li><a class="dropdown-item" href="<?php echo e(route('admin.login')); ?>">Admin</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('clinic.login')); ?>">Clinic Admin</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('patient.login')); ?>">Patient</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('doctor.login')); ?>">Doctor</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('staff.login')); ?>">Staff</a></li>
                            </ul>
                        </div>
                    <?php endif; ?>
                </li>
            </ul>
        </div>
        <div class=" btn_nav d-flex align-items-center gap-5">
            <?php if(auth()->guard()->check()): ?>
                <a href="<?php echo e(url('logout')); ?>" class="button_login login-btn me-2">Sign out</a>
                <a href="<?php echo e(url('home')); ?>" class="button_login button1 login-btn">Dashboard</a>
            <?php else: ?>
                <a href="<?php echo e(url('registration-instruction')); ?>" class="button_login button1 login-btn">Sign up</a>
                <div class="dropdown">
                    <a class="button_login button1 white-color fs-16 dropdown-toggle" href="#" role="button"
                        id="loginDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        Login
                    </a>
                    <ul class="header-login-menu dropdown-menu" aria-labelledby="loginDropdown">
                        
                        <li><a class="dropdown-item" href="<?php echo e(route('clinic.login')); ?>">Clinic Admin</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('doctor.login')); ?>">Prescriber</a></li>    
                        <li><a class="dropdown-item" href="<?php echo e(route('staff.login')); ?>">Staff</a></li>
                        <li><a class="dropdown-item" href="<?php echo e(route('patient.login')); ?>">Patient</a></li>
                 
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</nav>


<!-- 























































 --><?php /**PATH D:\git\rx-direct\resources\views/website/templates/header.blade.php ENDPATH**/ ?>