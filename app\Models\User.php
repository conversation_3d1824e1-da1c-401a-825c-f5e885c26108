<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;
    //use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array

     */
    protected $fillable = [
        'name',
        'user_id',
        'type',
        'type_changed_at',
        'clinic_id',
        'clinic_approval',
        'email',
        'password',
        'status',
        'cashback_percentage',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array

     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array

     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'type_changed_at' => 'datetime',
    ];

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function userTypes()
    {
        return $this->hasMany(UserType::class);
    }

    public function latestUserType()
    {
        return $this->hasOne(UserType::class)->latestOfMany();
    }
    public function users()
    {
        return $this->hasMany(User::class)->orderByDesc('created_at');  // 'user_id' is the foreign key in the users table
    }
    public function ownPatient()
    {
        return $this->users()->whereHas('roles', function ($q) {
            $q->where('name', 'patients');
        })->count();
    }
    public function ownPrescriptions()
    {
        return $this->hasMany(Prescription::class, 'user_id')->orderByDesc('created_at');  // 'user_id' is the foreign key in the users table
    }
    public function patientPrescriptions()
    {
        return $this->hasMany(Prescription::class, 'patient_id')->orderByDesc('created_at');  // 'user_id' is the foreign key in the users table
    }
    public function createdBy()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function scopeClinicAdmins($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'clinic_admin'); // Adjust column name if necessary
        });
    }
    public function scopeDoctors($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'doctor'); // Adjust column name if necessary
        });
    }
    public function scopeStaffs($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'staff'); // Adjust column name if necessary
        });
    }
    public function scopePatients($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'patients'); // Adjust column name if necessary
        });
    }
    public function scopeStaffDoctors($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'staff'); // Adjust column name if necessary
        })->whereHas('profile', function ($q) {
            $q->where('role', 'Doctor');
        });
    }
    public function scopeStaffReceptionists($query)
    {
        return $query->whereHas('roles', function ($q) {
            $q->where('name', 'staff'); // Adjust column name if necessary
        })->whereHas('profile', function ($q) {
            $q->where('role', 'Receptionist');
        });
    }
    public function getStatusTextAttribute()
    {
        $status = [
            0 => 'Pending',
            1 => 'Active',
            2 => 'Deactive',
        ];

        // Use $this->attributes['status'] ?? null to prevent undefined property errors
        return $status[$this->attributes['status'] ?? null] ?? 'Unknown';
    }


    public function clinicStaff()
    {
        return $this->hasMany(User::class, 'user_id')->orderByDesc('created_at');  // 'user_id' is the foreign key in the users table
    }
    public function clinicPatinets()
    {
        return $this->hasMany(User::class, 'clinic_id')
            ->whereHas('roles', function($q) {
                $q->where('name', 'patients');
            })
            ->orderByDesc('created_at');
    }
    public function clinicPrescriptions()
    {
        return $this->hasMany(Prescription::class, 'clinic_id')->orderByDesc('created_at');  // 'user_id' is the foreign key in the users table
    }


    // Count the number of Doctors
    public function countDoctors()
    {
        return $this->clinicStaff()->whereHas('profile', function ($query) {
            $query->where('role', 'Doctor');
        })->count();
    }

    // Count the number of Receptionists
    public function countReceptionists()
    {
        return $this->clinicStaff()->whereHas('profile', function ($query) {
            $query->where('role', 'Receptionist');
        })->count();
    }
    public function clinic()
    {
        return $this->belongsTo(User::class, 'clinic_id');
    }
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }
    /**
     * Get all of the prescriptionInventories for the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function getUsedPrescriptionInventories()
    {
        return Inventory::whereHas('prescriptions', function ($query) {
            $query->where('user_id', $this->id);
        });
    }
    public function getUsedClinicInventories()
    {
        return Inventory::whereHas('prescriptions', function ($query) {
            $query->where('clinic_id', $this->id);
        });
    }
    /**
     * Get formatted role name for display
     *
     * @return string
     */
    public function getRoleText()
    {
        if ($this->hasRole('clinic_admin')) {
            return 'Clinic Admin';
        } elseif ($this->hasRole('doctor')) {
            return 'Doctor';
        } elseif ($this->hasRole('patients')) {
            return 'Patient';
        } elseif ($this->hasRole('admin')) {
            return 'Admin';
        } elseif ($this->hasRole('staff')) {
            if ($this->profile && $this->profile->role == 'Doctor') {
                return 'Doctor';
            } elseif ($this->profile && $this->profile->role == 'Receptionist') {
                return 'Receptionist';
            }
            return 'Staff';
        }
        return '';
    }

}
