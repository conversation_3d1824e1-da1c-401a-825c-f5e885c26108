<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->

<?php $__env->startPush('css'); ?>
    
    <style>
        .nav-btn {
            border: 1px solid #ccc;
            background: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        .month-text {
            font-weight: 500;
        }

        .filter-btn {
            background: white;
            border: 1px solid #ccc;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
        }

        .dropdown {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            min-width: 150px;
            padding: 12px;
            z-index: 1000;
        }

        .dropdown.show {
            display: block;
        }

        .dropdown label {
            display: block;
            margin-bottom: 8px;
        }

        .dropdown input[type="checkbox"] {
            margin-right: 6px;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <?php if(Auth::user()->hasRole('doctor')): ?>
                    <div class="col-lg-12">
                        <h2 class="mb-3">All Invoices</h2>
                        <div class="purple-card d-flex justify-content-between">
                            <p class="fs-20 fw-600 input-text-gray"> Payment Type </p>
                            <div class="d-flex gap-2 align-items-center">
                                <img src="<?php echo e(asset('website')); ?>/assets/media/images/table-featured-icon.svg" alt=""
                                    height="40px" width="40px">
                                <h5 class="fw-500 number-gray-black">
                                    Type1</h5>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="col-lg-12">
                    <?php if(!Auth::user()->hasAnyRole(['doctor', 'clinic_admin'])): ?>
                        <h2 class="mb-3">All Invoices</h2>
                    <?php endif; ?>
                    <div class="table-invoices">
                        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16" id="customTab">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#type1" data-type='Type 1'>Type 1</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#type2" data-type='Type 2'>Type 2</a>
                            </li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="type1" role="tabpanel">
                                <div class="overview">
                                    <h2 class=" fs-18 fw-500 mb-3">Overview</h2>
                                    <div class="row clinic_index">
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> TOTAL INCOME </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Total Balance
                                                        Available </p>
                                                </div>

                                                <div class="d-flex justify-content-between staff_percent">
                                                    <div class="fs-21 fw-700" data-overview="available_funds">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> FUTURE PAYMENTS </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Balance to be SEND </p>
                                                </div>

                                                <div class="d-flex justify-content-between ">
                                                    <div class="fs-21 fw-700" data-overview="future_payments">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> CASHBACK AMOUNT Send </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Amount to send Clinic
                                                        Admins/Prescribers </p>
                                                </div>

                                                <div class="d-flex justify-content-between">
                                                    <div class="fs-21 fw-700" data-overview="cashback_amount">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> CASHBACK AMOUNT LEFT </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Amount to send Clinic
                                                        Admins/Prescribers </p>
                                                </div>

                                                <div class="d-flex justify-content-between">
                                                    <div class="fs-21 fw-700" data-overview="cashback_amount">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="type2" role="tabpanel">
                                <div class="overview">
                                    <h2 class=" fs-16 fw-500  mb-3">Overview</h2>
                                    <div class="row clinic_index">
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> TOTAL Amount </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Total Balance
                                                        Available </p>
                                                </div>

                                                <div class="d-flex justify-content-between staff_percent">
                                                    <div class="fs-21 fw-700" data-overview="available_funds">£0</div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> RECEIVED AMOUNT </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Total amount received
                                                    </p>
                                                </div>

                                                <div class="d-flex justify-content-between staff_percent">
                                                    <div class="fs-21 fw-700" data-overview="recieved_amount">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-15 fw-600 heading-gary"> PENDING AMOUNT </p>
                                                    <p class="fs-15 fw-600 heading-gary"> Balance to be received </p>
                                                </div>

                                                <div class="d-flex justify-content-between ">
                                                    <div class="fs-21 fw-700" data-overview="pending_amount">£0</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if(Auth::user()->hasRole('admin')): ?>
                    <div class="col-lg-12">
                        <div class="table-all-invoices pt-3 ">
                            <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16" id="myTab">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#Clinic-admin"
                                        data-role='clinic_admin'>Clinic
                                        Admin</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#Prescribers"
                                        data-role="doctor">Individual
                                        Prescribers </a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane fade show active" id="Clinic-admin" role="tabpanel">
                                    <!--  -->
                                    <div class="custom_tabs">
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-between align-items-center pt-5">
                                            <h3>Clinic Admins
                                            </h3>
                                            <div class="d-flex justify-content-end align-items-center gap-3">
                                                <div class="search_box">
                                                    <input type="search" id="searchMedicines"
                                                        class="search form-control" name="search_admin"
                                                        placeholder="Search By Name...">
                                                </div>
                                                <div class="custom-select">
                                                    <select id="" class="status_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="" selected>All Invoices</option>
                                                        <option value="0">Unpaid</option>
                                                        <option value="1">Paid</option>
                                                    </select>
                                                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>
                                                <div class="custom-select">
                                                    <select id="" class="month_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="">All Months</option>
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="august">August</option>
                                                        <option value="september">September</option>
                                                        <option value="october">October</option>
                                                        <option value="november">November</option>
                                                        <option value="december">December</option>
                                                    </select>
                                                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>
                                            </div>

                                        </div>
                                        <div class="table-responsive">
                                            <table id=""
                                                class="table staffPrescritionTable display custom-table gy-5 gs-5 sortTable">
                                                <thead>
                                                    <tr>
                                                        <th class="min-w-300px">Clinic Admin Name</th>
                                                        <th class="min-w-250px">Month</th>
                                                        <th class="min-w-250px">Billed Amount</th>
                                                        <th class="min-w-250px">Cashback Amount</th>
                                                        <th class="min-w-250px payment_status">Payment Status</th>
                                                        <th class="min-w-50px first-and-last">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="ps-5" id="clinicAdmin">
                                                </tbody>
                                            </table>
                                            <div class="d-flex justify-content-between align-items-center mt-5">
                                                <p>
                                                    Showing <span class="first-item">0</span> to <span
                                                        class="last-item">0</span> of
                                                    <span class="total-items">0</span> entries
                                                </p>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="Prescribers" role="tabpanel">
                                    <!--  -->
                                    <div class="custom_tabs">
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-between align-items-center pt-5">
                                            <h3>Individual Prescribers</span>
                                            </h3>
                                            <div class="d-flex justify-content-end align-items-center gap-3">
                                                <div class="search_box">
                                                    <input type="search" id="searchMedicines"
                                                        class="search search form-control" name="search_admin"
                                                        placeholder="Search By Name...">
                                                </div>


                                                <div class="custom-select">
                                                    <select id="" class="status_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="" selected >All Invoices</option>
                                                        <option value="0">Unpaid</option>
                                                        <option value="1">Paid</option>
                                                    </select>
                                                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>


                                                <!--
                                                        <div class="custom-select">
                                                            <select id="statusFilter" class="status_filter">
                                                                <option value="">All Invoices</option>
                                                                <option value="0">Unpaid</option>
                                                                <option value="1">Paid</option>
                                                            </select>
                                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                                alt="Filter Icon">
                                                        </div> -->
                                                <div class="custom-select">
                                                    <select id="" class="month_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="">All Months</option>
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="august">August</option>
                                                        <option value="september">September</option>
                                                        <option value="october">October</option>
                                                        <option value="november">November</option>
                                                        <option value="december">December</option>
                                                    </select>
                                                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table id=""
                                                class="table staffPrescritionTable display custom-table gy-5 gs-5 sortTable">
                                                <thead>
                                                    <tr>
                                                        <th class="min-w-300px">Prescriber Name</th>
                                                        <th class="min-w-300px">Month</th>
                                                        <th class="min-w-300px">Billed Amount</th>
                                                        <th class="min-w-300px">Cashback Amount</th>
                                                        <th class="min-w-300px" class="payment_status">Payment Status</th>
                                                        <th class="min-w-50px first-and-last">Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="ps-5" id="doctor">
                                                </tbody>
                                            </table>
                                            <div class="d-flex justify-content-between align-items-center mt-5">
                                                <p>
                                                    Showing <span class="first-item">0</span> to <span
                                                        class="last-item">0</span> of
                                                    <span class="total-items">0</span> entries
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                <?php if(Auth::user()->hasAnyRole(['clinic_admin', 'doctor'])): ?>
                    <div class="col-lg-12">
                        <div class="table-all-invoices pt-3 ">
                            <div
                                class="report-header d-flex align-items-center justify-content-between column-gap-10 py-5">
                                <!--  -->
                            </div>
                            <div class="custom_tabs custom-dropdown">
                                <div class="custom-dropdown roboto d-flex justify-content-between align-items-center pt-5">
                                    <div>
                                        <h3>Invoices</h3>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                        <div class="search_box">
                                            <input type="search" id="searchMedicines" class="search search form-control"
                                                name="search_admin" placeholder="Search By Name...">
                                        </div>
                                        <div class="invoices custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected >All Invoices</option>
                                                <option value="0">Unpaid</option>
                                                <option value="1">Paid</option>
                                            </select>
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                        <div class="custom-select">
                                            <select id="" class="month_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="">All Months</option>
                                                <option value="january">January</option>
                                                <option value="february">February</option>
                                                <option value="march">March</option>
                                                <option value="april">April</option>
                                                <option value="may">May</option>
                                                <option value="june">June</option>
                                                <option value="july">July</option>
                                                <option value="august">August</option>
                                                <option value="september">September</option>
                                                <option value="october">October</option>
                                                <option value="november">November</option>
                                                <option value="december">December</option>
                                            </select>
                                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table id=""
                                        class="staffPrescritionTable table display custom-table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-300px">Month</th>
                                                <th class="min-w-250px">Billed Amount</th>
                                                <th class="min-w-250px">Cashback Amount</th>
                                                <th class="min-w-250px" class="payment_status">Payment Status</th>
                                                <th class="min-w-50px first-and-last">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody class="ps-5" id="appendBody">
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-between align-items-center mt-5">
                                        <p>
                                            Showing <span class="first-item">0</span> to <span class="last-item">0</span>
                                            of
                                            <span class="total-items">0</span> entries
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    
    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
    <script>
        // Common function for all users
        function updatePaginationInfo(container, data) {
            let firstItem, lastItem, totalItems;

            if (Array.isArray(data)) {
                totalItems = data.length;
                firstItem = totalItems > 0 ? 1 : 0;
                lastItem = totalItems;
            } else if (data && typeof data === 'object') {
                firstItem = data.from || (data.length > 0 ? 1 : 0);
                lastItem = data.to || data.length || 0;
                totalItems = data.total || data.length || 0;
            } else {
                firstItem = 0;
                lastItem = 0;
                totalItems = 0;
            }

            container.find('.first-item').text(firstItem);
            container.find('.last-item').text(lastItem);
            container.find('.total-items').text(totalItems);
        }

        $(document).ready(function() {
            handleTabTypeChange();
        });

        function handleTabTypeChange() {
            let activeType = $('#customTab .nav-link.active').data('type') || 'Type 1';

            if (activeType === 'Type 2') {
                $('.status_filter').parent().hide();
                // $('.payment_status').hide();
            } else {
                $('.status_filter').parent().show();
                // $('.payment_status').show();
            }

            // Update tables based on the new active type
            getInvoices();
        }

        $(document).on('click', '.nav-link', function() {
            let tabId = $(this).attr('href');
            let activeTabContainer = $(tabId);
            if ($(this).data('type') === 'Type 2') {
                activeTabContainer.find('.paymentStatus').hide();
            }
        });
        let currentIndex = 0;
        const allMonths = [];

        const today = new Date();
        for (let i = 0; i < 12; i++) {
            const d = new Date(today.getFullYear(), today.getMonth() - i, 1);
            const formatted = d.toLocaleString("default", {
                month: "long",
                year: "numeric"
            });
            allMonths.push(formatted);
        }

        // function updateMonthDisplay() {
        //     alert('ok');
        //     const selectedMonth = allMonths[currentIndex];
        //     document.querySelectorAll(".month-text").forEach(el => {
        //         el.textContent = selectedMonth;
        //     });
        //     document.querySelectorAll("table").forEach(table => {
        //         let hasVisibleRows = false;
        //         $(table).find('tbody tr').each(function() {
        //             const rowMonth = $(this).find('.month-column').text().trim();
        //             const isVisible = rowMonth === selectedMonth;
        //             $(this).toggle(isVisible);
        //             if (isVisible) hasVisibleRows = true;
        //         });
        //     });
        // }

        function previousMonth() {
            if (currentIndex < allMonths.length - 1) {
                currentIndex++;
                // updateMonthDisplay();
            }
        }

        function nextMonth() {
            if (currentIndex > 0) {
                currentIndex--;
                // updateMonthDisplay();
            }
        }

        document.addEventListener("DOMContentLoaded", () => {
            // updateMonthDisplay();
        });
    </script>

    <?php if(Auth::user()->hasRole('admin')): ?>
        <script>
            let activeType = 'Type 1';
            let activeRole = 'clinic_admin';
            let paymentStatus = '';
            let activeMonth = '';

            function getActiveType() {
                return $('#customTab .nav-link.active').data('type') || 'Type 1';
            }

            function getActiveRole() {
                return $('#myTab .nav-link.active').data('role') || 'clinic_admin';
            }

            function getPaymentStatus() {
                var activeTabLink = $('#myTab .nav-link.active');
                var targetPaneSelector = activeTabLink.attr('href');
                return $(targetPaneSelector).find('.status_filter').val();
            }

            function getMonthFilter() {
                var activeTabLink = $('#myTab .nav-link.active');
                var targetPaneSelector = activeTabLink.attr('href');
                var monthValue = $(targetPaneSelector).find('.month_filter').val();
                console.log('Selected month from filter:', monthValue);
                return monthValue;
            }

            function getSearch() {
                var activeTabLink = $('#myTab .nav-link.active');
                var targetPaneSelector = activeTabLink.attr('href');
                return $(targetPaneSelector).find('.search').val();
            }

            function formatMonthYear(date) {
                return date.toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'long'
                });
            }

            function getInvoices() {
                let activeType = getActiveType();
                let activeRole = getActiveRole();
                let activePaymentStatus = getPaymentStatus();
                activeMonth = getMonthFilter();
                let activeSearch = getSearch();

                console.log('Fetching invoices with params:', {
                    type: activeType,
                    role: activeRole,
                    payment_status: activePaymentStatus,
                    month: activeMonth,
                    search: activeSearch
                });

                $.ajax({
                    url: "<?php echo e(route('fetch.invoices')); ?>",
                    method: 'GET',
                    data: {
                        type: activeType,
                        role: activeRole,
                        payment_status: activePaymentStatus,
                        month: activeMonth,
                        search: activeSearch,
                    },
                    success: function(response) {
                        console.log("Raw response:", response);

                        // Calculate total income from response data
                        let totalIncome = 0;
                        let futurePayments = 0;
                        let cashbackAmountSent = 0;
                        let cashbackAmountLeft = 0;
                        let receivedAmount = 0;
                        let pendingAmount = 0;
                        if (response && Array.isArray(response)) {
                            response.forEach(function(invoice) {
                                if (invoice && invoice.total_amount) {
                                    totalIncome += parseFloat(invoice.total_amount);
                                }
                                // Calculate future payments (unpaid cashback amounts)
                                if (invoice && invoice.cashback_amount && invoice.is_paid === 0) {
                                    futurePayments += parseFloat(invoice.cashback_amount);
                                }
                                // Calculate cashback amount sent (paid cashback amounts)
                                if (invoice && invoice.cashback_amount && invoice.is_paid === 1) {
                                    cashbackAmountSent += parseFloat(invoice.cashback_amount);
                                }
                                // Calculate cashback amount left (unpaid invoice amounts)
                                if (invoice && invoice.total_amount && invoice.is_paid === 0) {
                                    cashbackAmountLeft += parseFloat(invoice.total_amount);
                                }
                                // Calculate received amount for Type 2 (paid cashback amounts)
                                if (invoice && invoice.cashback_amount && invoice.is_paid === 1) {
                                    receivedAmount += parseFloat(invoice.cashback_amount);
                                }
                                // Calculate pending amount for Type 2 (unpaid cashback amounts)
                                if (invoice && invoice.cashback_amount && invoice.is_paid === 0) {
                                    pendingAmount += parseFloat(invoice.cashback_amount);
                                }
                            });
                        }

                        // Update the total income card
                        $('[data-overview="available_funds"]').text(`£${totalIncome.toLocaleString()}`);

                        // Update the future payments card
                        $('[data-overview="future_payments"]').text(`£${futurePayments.toLocaleString()}`);

                        // Update the cashback amount sent card
                        $('[data-overview="cashback_amount"]').text(`£${cashbackAmountSent.toLocaleString()}`);

                        // Update the cashback amount left card
                        $('[data-overview="cashback_amount"]').last().text(
                            `£${cashbackAmountLeft.toLocaleString()}`);

                        // Update Type 2 cards
                        $('[data-overview="recieved_amount"]').text(`£${receivedAmount.toLocaleString()}`);
                        $('[data-overview="pending_amount"]').text(`£${pendingAmount.toLocaleString()}`);

                        if (activeRole === 'clinic_admin') {
                            let clinicAdmin = $('#clinicAdmin');
                            clinicAdmin.empty();

                            if (!response || !Array.isArray(response) || response.length === 0) {
                                console.log('No clinic admin invoices found');
                                clinicAdmin.append(
                                    `<tr><td colspan="6" class="text-center">No invoices found.</td></tr>`
                                );
                                updatePaginationInfo($('#Clinic-admin .d-flex.justify-content-between'), {
                                    from: 0,
                                    to: 0,
                                    total: 0
                                });
                                return;
                            }

                            // Filter by month if selected and not "All Months"
                            if (activeMonth && activeMonth !== '') {
                                console.log('Filtering by month:', activeMonth);
                                response = response.filter(invoice => {
                                    if (!invoice.month) {
                                        console.log('Invoice has no month:', invoice);
                                        return false;
                                    }

                                    const invoiceDate = new Date(invoice.month);
                                    const invoiceMonth = invoiceDate.toLocaleString('en-US', {
                                        month: 'long'
                                    });

                                    console.log('Comparing:', {
                                        invoiceMonth: invoiceMonth,
                                        selectedMonth: activeMonth,
                                        invoiceDate: invoiceDate,
                                        rawMonth: invoice.month
                                    });

                                    return invoiceMonth.toLowerCase() === activeMonth.toLowerCase();
                                });
                                console.log('Filtered response by month:', response);
                            } else {
                                console.log('Showing all months data');
                            }

                            response.forEach(function(invoice) {
                                console.log('Processing invoice:', invoice);

                                if (!invoice || !invoice.user) {
                                    console.log('Invalid invoice data:', invoice);
                                    return;
                                }

                                let type = invoice.type;
                                let paymentStatus = invoice.is_paid === 0 ? 'Pending' : 'Paid';
                                let paymentClass = invoice.is_paid === 0 ? 'badge-inactive' :
                                    'badge-active';

                                let monthYear = new Date(invoice.month);
                                let formattedMonthYear = monthYear.toLocaleString('en-US', {
                                    year: 'numeric',
                                    month: 'long'
                                });

                                let invoiceDetailsUrl = `/admin-invoices-details/${invoice.id}`;

                                let row = `
                                    <tr>
                                        <td class="w-200px">
                                            <a class="table-gray" href="${invoiceDetailsUrl}">${invoice.user.name || 'Outsourced'}</a>
                                        </td>
                                        <td class="w-200px month-column">${formattedMonthYear}</td>
                                        <td class="w-200px">£${parseFloat(invoice.total_amount || 0).toFixed(2)}</td>
                                        <td class="w-200px">£${parseFloat(invoice.cashback_amount || 0).toFixed(2)}</td>
                                        <td class="payment_status"><span class="badge ${paymentClass} roboto fs-14 fw-400">
                                        <td class="action_btn">
                                          <div class="">
                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                     <i class="fa-solid fa-ellipsis-vertical"></i>
                                            </a>
                                                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                    <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                            href="${invoiceDetailsUrl}">View</a>
                                                        </a>
                                                    </li>
                                                        <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                            href="/admin/invoices/${invoice.id}/download-medicines-pdf">Download PDF</a>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>`;

                                clinicAdmin.append(row);
                                console.log('Added row for invoice:', invoice.id);
                            });

                            KTMenu.createInstances();

                            // Update pagination info
                            updatePaginationInfo($('#Clinic-admin .d-flex.justify-content-between'), {
                                from: response.length > 0 ? 1 : 0,
                                to: response.length,
                                total: response.length
                            });
                        } else {
                            let doctor = $('#doctor');
                            doctor.empty();

                            if (!response || !Array.isArray(response) || response.length === 0) {
                                console.log('No prescriber invoices found');
                                doctor.append(
                                    `<tr><td colspan="6" class="text-center">No invoices found.</td></tr>`
                                );
                                updatePaginationInfo($('#Prescribers .d-flex.justify-content-between'), {
                                    from: 0,
                                    to: 0,
                                    total: 0
                                });
                                return;
                            }

                            // Filter by month if selected and not "All Months"
                            if (activeMonth && activeMonth !== '') {
                                console.log('Filtering by month:', activeMonth);
                                response = response.filter(invoice => {
                                    if (!invoice.month) {
                                        console.log('Invoice has no month:', invoice);
                                        return false;
                                    }

                                    const invoiceDate = new Date(invoice.month);
                                    const invoiceMonth = invoiceDate.toLocaleString('en-US', {
                                        month: 'long'
                                    });

                                    console.log('Comparing:', {
                                        invoiceMonth: invoiceMonth,
                                        selectedMonth: activeMonth,
                                        invoiceDate: invoiceDate,
                                        rawMonth: invoice.month
                                    });

                                    return invoiceMonth.toLowerCase() === activeMonth.toLowerCase();
                                });
                                console.log('Filtered response by month:', response);
                            } else {
                                console.log('Showing all months data');
                            }

                            response.forEach(function(invoice) {
                                let type = invoice.type;
                                let paymentStatus = invoice.is_paid === 0 ? 'Pending' : 'Paid';
                                let paymentClass = invoice.is_paid === 0 ? 'badge-inactive' :
                                    'badge-active';
                                let acknowledgementStatus = invoice.is_acknowledge === 0 ? 'Pending' :
                                    'Acknowledged';
                                let acknowledgeClass = invoice.is_acknowledge === 0 ? 'badge-inactive' :
                                    'badge-active';
                                let monthYear = new Date(invoice.month);
                                let formattedMonthYear = formatMonthYear(monthYear);
                                let invoiceDetailsUrl = `/admin-invoices-details/${invoice.id}`;
                                let row = `
                                                <tr>
                                                    <td class="w-200px"><a class="table-gray" href="${invoiceDetailsUrl}">${invoice.user.name}</a></td>
                                                    <td class="w-200px month-column">${formattedMonthYear}</td>
                                                    <td class="w-200px">£${invoice.total_amount}</td>
                                                    <td class="w-200px">£${invoice.cashback_amount}</td>
                                                     ${invoice.type === 'Type 1' ?
                                        `<td><span class="badge ${paymentClass} roboto fs-14 fw-400">${paymentStatus}</span></td>` :
                                        `<td>Outsourced</td>`
                                    }

                                      <td class="action_btn">
                                          <div class="">
                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                     <i class="fa-solid fa-ellipsis-vertical"></i>
                                            </a>
                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                        href="${invoiceDetailsUrl}">View</a>
                                                                    </a>
                                                                </li>
                                                                 <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                        href="/admin/invoices/${invoice.id}/download-medicines-pdf">Download PDF</a>
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>





                                                </tr>`;
                                doctor.append(row);
                                KTMenu.createInstances();
                            });
                            // Update pagination info for prescribers
                            updatePaginationInfo($('#Prescribers .d-flex.justify-content-between'), response);
                        }
                    },
                    error: function(xhr) {
                        console.error("Error fetching invoices:", xhr.responseText);
                        console.error("Error status:", xhr.status);

                        let targetContainer = activeRole === 'clinic_admin' ? $('#clinicAdmin') : $('#doctor');
                        targetContainer.empty();
                        targetContainer.append(
                            `<tr><td colspan="6" class="text-center">Error loading invoices. Please try again.</td></tr>`
                        );

                        let targetPagination = activeRole === 'clinic_admin' ?
                            $('#Clinic-admin .d-flex.justify-content-between') :
                            $('#Prescribers .d-flex.justify-content-between');

                        updatePaginationInfo(targetPagination, {
                            from: 0,
                            to: 0,
                            total: 0
                        });
                    }
                });
            }
            $(document).ready(function() {
                getInvoices();
                $('#customTab, #myTab').on('shown.bs.tab change', function(e) {
                    handleTabTypeChange();
                    getInvoices();
                });
            });
            let timeout;
            $(document).on('input', '.search', function() {
                clearTimeout(timeout);
                let input = $(this);
                timeout = setTimeout(() => {}, 1000);
                getInvoices();
                handleTabTypeChange();
            });
            $(document).on('change', '.status_filter', function() {
                getInvoices();
                handleTabTypeChange();
            });
            $(document).on('change', '.month_filter', function() {
                getInvoices();
                handleTabTypeChange();
            });
        </script>
    <?php endif; ?>
    <?php if(Auth::user()->hasAnyRole(['clinic_admin', 'doctor'])): ?>
        <script>
            let activeType = 'Type 1';
            let userId = <?php echo json_encode(auth()->user()->id, 15, 512) ?>;
            let paymentStatus = '';
            let searchValue = '';
            let activeMonth = '';

            function getActiveType() {
                return $('#customTab .nav-link.active').data('type') || 'Type 1';
            }

            function getPaymentStatus() {
                return $('.status_filter').val();
            }

            function getSearch() {
                return $('.search').val();
            }

            function getMonthFilter() {
                var monthValue = $('.month_filter').val();
                console.log('Selected month from filter:', monthValue);
                return monthValue;
            }

            function getInvoices() {
                console.log('Starting getInvoices function');

                let activeType = getActiveType();
                console.log('Active Type:', activeType);

                let activePaymentStatus = getPaymentStatus();
                console.log('Payment Status:', activePaymentStatus);

                let activeSearch = getSearch();
                console.log('Search Value:', activeSearch);

                activeMonth = getMonthFilter();
                console.log('Selected Month:', activeMonth);

                $.ajax({
                    url: "<?php echo e(route('fetch.invoices')); ?>",
                    method: 'GET',
                    data: {
                        type: activeType,
                        payment_status: activePaymentStatus,
                        search: activeSearch,
                        month: activeMonth
                    },
                    success: function(response) {
                        console.log('Raw AJAX Response:', response);
                        console.log('Response type:', typeof response);
                        console.log('Response keys:', Object.keys(response));

                        let appendBody = $('#appendBody');
                        appendBody.empty();

                        // Check if response is valid
                        if (!response || typeof response !== 'object') {
                            console.log('Invalid response format');
                            appendBody.append(
                                `<tr><td colspan="5" class="text-center">No invoices found.</td></tr>`
                            );
                            updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), {
                                from: 0,
                                to: 0,
                                total: 0
                            });
                            return;
                        }

                        // Handle array response (invoices)
                        if (Array.isArray(response)) {
                            console.log('Processing array response');

                            if (response.length === 0) {
                                console.log('No invoices found in response');
                                appendBody.append(
                                    `<tr><td colspan="5" class="text-center">No invoices found.</td></tr>`
                                );
                                updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), {
                                    from: 0,
                                    to: 0,
                                    total: 0
                                });
                                return;
                            }

                            // Sort invoices by month (newest first)
                            response.sort((a, b) => new Date(b.month) - new Date(a.month));

                            response.forEach(function(invoice) {
                                console.log('Processing invoice:', invoice);

                                if (!invoice || !invoice.month) {
                                    console.log('Invalid invoice data:', invoice);
                                    return;
                                }

                                let month = new Date(invoice.month).toLocaleString('en-US', {
                                    year: 'numeric',
                                    month: 'long'
                                });

                                let paymentStatus = invoice.is_paid === 0 ? 'Pending' : 'Paid';
                                let paymentClass = invoice.is_paid === 0 ? 'badge-inactive' : 'badge-active';
                                let invoiceDetailsUrl = `/admin-invoices-details/${invoice.id}`;

                                let row = `
                                    <tr>
                                        <td class="w-200px month-column">${month}</td>
                                        <td class="w-200px">£${parseFloat(invoice.total_amount || 0).toFixed(2)}</td>
                                        <td class="w-200px">£${parseFloat(invoice.cashback_amount || 0).toFixed(2)}</td>
                                        <td><span class="badge ${paymentClass} roboto fs-14 fw-400">${paymentStatus}</span></td>
                                        <td class="action_btn">
                                              <div class="">
                                                <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                         <i class="fa-solid fa-ellipsis-vertical"></i>
                                                </a>
                                                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                    <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                            href="${invoiceDetailsUrl}">View</a>
                                                        </a>
                                                    </li>
                                                        <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                            href="/admin/invoices/${invoice.id}/download-medicines-pdf">Download PDF</a>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>`;

                                appendBody.append(row);
                                console.log('Added row for invoice:', invoice.id);
                            });

                            KTMenu.createInstances();
                            let totalEntries = response.length;
                            console.log('Total entries:', totalEntries);

                            updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), {
                                from: totalEntries > 0 ? 1 : 0,
                                to: totalEntries,
                                total: totalEntries
                            });
                            console.log('Updated pagination info');
                        } else {
                            console.log('Unexpected response format:', response);
                            appendBody.append(
                                `<tr><td colspan="5" class="text-center">Unexpected response format.</td></tr>`
                            );
                            updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), {
                                from: 0,
                                to: 0,
                                total: 0
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error("Error fetching invoices:", xhr.responseText);
                        console.error("Error status:", xhr.status);
                        console.error("Error details:", xhr);

                        let appendBody = $('#appendBody');
                        appendBody.empty();
                        appendBody.append(
                            `<tr><td colspan="5" class="text-center">Error loading invoices. Please try again.</td></tr>`
                        );
                        updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), {
                            from: 0,
                            to: 0,
                            total: 0
                        });
                    }
                });
            }
            $(document).ready(function() {
                getInvoices();

                // Initial call to set correct visibility
                handleTabTypeChange();

                // Handle tab changes for Type tabs (Type 1/Type 2)
                $('#customTab').on('shown.bs.tab', function(e) {
                    let activeType = $(e.target).data('type') || 'Type 1';
                    handleTabTypeChange();
                    getInvoices();

                    // Reset pagination for both admin and non-admin views
                    if (activeType === 'Type 2') {
                        // For Type 2, we don't show payment status
                        $('.payment_status').hide();
                        $('.status_filter').parent().hide();
                    } else {
                        $('.payment_status').show();
                        $('.status_filter').parent().show();
                    }
                });

                // Handle tab changes for role tabs (Clinic Admin/Prescriber)
                $('#myTab').on('shown.bs.tab', function(e) {
                    let activeRole = $(e.target).data('role') || 'clinic_admin';
                    getInvoices();

                    // Reset pagination for the new active tab
                    if (activeRole === 'clinic_admin') {
                        updatePaginationInfo($('#Clinic-admin .d-flex.justify-content-between'), {
                            from: 0,
                            to: 0,
                            total: 0
                        });
                    } else {
                        updatePaginationInfo($('#Prescribers .d-flex.justify-content-between'), {
                            from: 0,
                            to: 0,
                            total: 0
                        });
                    }
                });

                // Handle search input with debounce
                let searchTimeout;
                $(document).on('input', '.search', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        getInvoices();
                    }, 500);
                });

                // Handle status filter changes
                $(document).on('change', '.status_filter', function() {
                    getInvoices();
                });
                $(document).on('change', '.month_filter', function() {
                    getInvoices();
                    handleTabTypeChange();
                });
            });
        </script>
    <?php endif; ?>
    <script>
        function fetchDashboardOverview(role = null) {
            // Get the active role if not provided
            if (!role) {
                role = $('#myTab .nav-link.active').data('role') || 'clinic_admin';
            }

            $.ajax({
                url: "<?php echo e(route('get.overview.data')); ?>",
                method: 'GET',
                data: {
                    role: role
                },
                success: function(data) {
                    $('[data-overview="available_funds"]').text(`£${data.available_funds.toLocaleString()}`);
                    $('[data-overview="future_payments"]').text(`£${data.future_payments.toLocaleString()}`);
                    $('[data-overview="cashback_amount"]').text(`£${data.cashback_amount.toLocaleString()}`);
                    $('[data-overview="total_income"]').text(`£${data.total_income.toLocaleString()}`);
                    $('[data-overview="recieved_amount"]').text(`£${data.recieved_amount.toLocaleString()}`);
                    $('[data-overview="pending_amount"]').text(`£${data.pending_amount.toLocaleString()}`);
                },
                error: function() {
                    console.error("Failed to load dashboard overview data.");
                }
            });
        }
        $(document).ready(function() {
            fetchDashboardOverview();
        });
        $(document).ready(function() {
            $('#update_status').on('submit', function(event) {
                event.preventDefault();
                let form = $(this);
                let formData = form.serialize();
                $.ajax({
                    url: "<?php echo e(route('update.invoice.status')); ?>",
                    method: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: response.icon,
                            title: response.message,
                            showConfirmButton: false,
                            timer: 2000
                        });
                        fetchDashboardOverview();
                        getInvoices();
                    },
                    error: function(xhr) {
                        let response = xhr.responseJSON;
                        Swal.fire({
                            icon: response?.icon || 'error',
                            title: response?.message || 'Unexpected error',
                            showConfirmButton: true
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git\rx-direct\resources\views/dashboard/admin/admin-invoices.blade.php ENDPATH**/ ?>