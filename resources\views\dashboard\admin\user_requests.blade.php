@extends('theme.layout.master')
@push('css')
    {{-- <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> --> --}}
@endpush
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <h4 class="roboto heading-gary"> All User</h4>
                    <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link active" data-bs-toggle="tab" href="#admins">Clinic Admins</a>
                        </li>
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#doctors">Prescribers (individuals)</a>
                        </li>
                        <li class="nav-item roboto fs-16 fw-600">
                            <a class="nav-link" data-bs-toggle="tab" href="#staff_doctors">Prescribers (staff)</a>
                        </li>
                        <!-- {{-- <li class="nav-item roboto fs-13 fw-600">
                                                        <a class="nav-link" data-bs-toggle="tab" href="#staff_receptionists">Receptionists</a>
                                                    </li> --}} -->
                    </ul>
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active custom_tabs" id="admins" role="tabpanel">
                            <div>
                                <div
                                    class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5 flex-wrap">
                                    <div>
                                        <h3>Clinic Admins
                                            {{-- <!-- <span class="input-text-gray"> ({{ $clinic_admins->total() }})</span> --> --}}
                                        </h3>
                                    </div>
                                    <div class="search_box">
                                        <input type="search" id="" value="" name="search_admin" class="form-control"
                                            placeholder="Search By Name">
                                    </div>
                                </div>
                                <div
                                    class="custom-dropdown roboto d-flex justify-content-md-end justify-content-start align-items-end pb-5 gap-2 flex-wrap">

                                    <div class="result-container1">
                                        <div class="range-options additional-options">
                                            <div
                                                class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                <div class="start-range date-field">
                                                    <label for="adminStartRange">Start Range:</label>
                                                    <span class="error-message text-danger"
                                                        style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                        select Start Range
                                                        first.</span>
                                                    <input type="date" class="start_range-input start-range-input"
                                                        name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                        max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                </div>

                                                <div class="end-range date-field">
                                                    <label for="adminEndRange">End Range:</label>
                                                    <input type="date" class="end-range-input end-range-input"
                                                        name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                        max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="custom-select">
                                        <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                            data-dropdown-css-class="w-200px">
                                            <option value="" selected>All</option>
                                            <option value="0">Pending</option>
                                            <option value="1">Approved</option>
                                            <option value="2">Rejected</option>
                                        </select>
                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                            alt="Filter Icon">
                                    </div>
                                    {{-- <!-- <div class="custom-select">
                                                <select id="statusFilter" class="status_filter">
                                                    <option value="">All</option>
                                                    <option value="0">Pending</option>
                                                    <option value="1">Approved</option>
                                                    <option value="2">Rejected</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                            </div> --> --}}
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table id="clinicTable" class="table display  gy-5 gs-5 sortTable">
                                    <thead>
                                        <tr>
                                            <th class="min-w-400px">Clinic Name</th>
                                            <th class="min-w-400px">Date Requested</th>
                                            <th class="min-w-400px">Status</th>
                                            <th class="min-w-50px first-and-last"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="admin">
                                        @if(count($clinic_admins) > 0)
                                            @foreach ($clinic_admins as $admin)
                                                            <tr id="row{{ $admin->id }}">
                                                                <td>
                                                                    <div class="d-flex gap-2 align-items-center">
                                                                        <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                            alt="" height="40px" width="40px">
                                                                        <div class="d-flex flex-column">
                                                                            <h5 class="fw-500 number-gray-black">
                                                                                {{ $admin->name ?? '' }}
                                                                            </h5>
                                                                            <span
                                                                                class="input-text-gray fs-14 fw-400">{{ $admin->email ?? '' }}</span>
                                                                        </div>
                                                                    </div>
                                                                </td>
                                                                <td>{{ $admin->created_at->format('m d Y') }}
                                                                </td>
                                                                <td class="user-status">
                                                                    <span
                                                                        class="badge badge-{{ $admin->status == 0 ? 'inactive' : ($admin->status == 1 ? 'active' : 'inactive') }} roboto fs-14 fw-400">
                                                                        {{ $admin->status == 0 ? 'Pending' : ($admin->status == 1 ? 'Approved' : 'Rejected') }}
                                                                    </span>
                                                                </td>
                                                                <td class="action_btn">
                                                                    <div class="dropdown">
                                                                        <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                            data-bs-toggle="dropdown" aria-expanded="true">
                                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                        </a>
                                                                        <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                            <li><a class="dropdown-item Satoshi px-3 listing_action_req"
                                                                                    data-bs-toggle="modal" href="#clinic-request-modal"
                                                                                    data-email="{{ $admin->email ?? '' }}"
                                                                                    data-name="{{ $admin->name ?? '' }}"
                                                                                    data-cashback="{{ $admin->cashback_percentage ?? '' }}"
                                                                                    data-payment_type="{{ $admin->type ?? '' }}"
                                                                                    data-type_changed_at="{{ $admin->latestUserType->created_at ?? '' }}"
                                                                                    data-id="{{ $admin->id ?? '' }}"
                                                                                    data-status="{{ $admin->status ?? 0 }}"
                                                                                    data-id_document="{{ $admin->profile->id_document ?? '' }}"
                                                                                    data-professional_type="{{ $admin->profile->professional_type ?? '' }}"
                                                                                    data-role="Clinic Admin"
                                                                                    data-idemnity_certificate="{{ $admin->profile->idemnity_certificate ?? '' }}">
                                                                                    {{ $admin->status != 0 ? 'Edit' : 'Request' }}
                                                                                </a>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </td>
                                                </div>
                                                </tr>
                                            @endforeach
                                        @else
                                <tr>
                                    <td colspan="4" class="text-center">No data found</td>
                                </tr>
                            @endif
                            </tbody>
                            </table>
                            <div class="d-flex justify-content-between align-items-center mt-5 paginate_custom">
                                <p>
                                    Showing {{ $clinic_admins->firstItem() }} to {{ $clinic_admins->lastItem() }} of
                                    {{ $clinic_admins->total() }}
                                    entries
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="tab-pane fade" id="doctors" role="tabpanel">
                        <div class="row">
                            <div class="col-lg-12 doctor-table">
                                <div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <div>
                                            <h3>Doctors
                                                <!-- <span class="input-text-gray"> ({{ $doctors->total() }})</span> -->
                                            </h3>
                                        </div>
                                        <div class="search_box">
                                            <input type="search" id="" value="" name="search_admin" class="form-control"
                                                placeholder="Search By Name">
                                        </div>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-md-end justify-content-start align-items-end pb-5 gap-2 flex-wrap">

                                        <div class="result-container1">
                                            <div class="range-options additional-options">
                                                <div
                                                    class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                                    <div class="start-range date-field">
                                                        <label for="adminStartRange">Start Range:</label>
                                                        <span class="error-message text-danger"
                                                            style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                            select Start Range
                                                            first.</span>
                                                        <input type="date" class="start_range-input start-range-input"
                                                            name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                    </div>

                                                    <div class="end-range date-field">
                                                        <label for="adminEndRange">End Range:</label>
                                                        <input type="date" class="end-range-input end-range-input"
                                                            name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                            max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                                    </div>
                                                </div>

                                            </div>
                                        </div>


                                        <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All</option>
                                                <option value="0">Pending</option>
                                                <option value="1">Approved</option>
                                                <option value="2">Rejected</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>


                                        <!-- <div class="custom-select">
                                                                        <select id="statusFilter" class="status_filter">
                                                                            <option value="">All</option>
                                                                            <option value="0">Pending</option>
                                                                            <option value="1">Approved</option>
                                                                            <option value="2">Rejected</option>
                                                                        </select>
                                                                        <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                                            alt="Filter Icon">
                                                                    </div> -->
                                    </div>
                                </div>
                                <!-- {{--                                        <div class="custom-select">--}}
                            {{--                                            <select id="statusFilter" class="status_filter">--}}
                            {{--                                                <option value="">All</option>--}}
                            {{--                                                <option value="Active">Active</option>--}}
                            {{--                                                <option value="Inactive">Inactive</option>--}}
                            {{--                                            </select>--}}
                            {{--                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"--}}
                            {{--                                                alt="Filter Icon">--}}
                            {{--                                        </div>--}} -->

                                <div class="table-responsive">
                                    <table id="clinicTable" class="table display  gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <th class="min-w-400px">Prescriber Name</th>
                                                <th class="min-w-400px">Date Added</th>
                                                <th class="min-w-400px">Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="doctors_div">
                                            @if(count($doctors) > 0)
                                                @foreach ($doctors as $doctor)
                                                    <tr id="row{{ $doctor->id }}">
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/doctor-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $doctor->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 mailto:fw-400">{{ $doctor->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td> {{ $doctor->created_at->format('m d Y') ?? '' }}</td>
                                                        <td class="user-status">
                                                            <span
                                                                class="badge badge-{{ $doctor->status == 0 ? 'inactive' : ($doctor->status == 1 ? 'active' : 'inactive') }} roboto fs-14 fw-400">
                                                                {{ $doctor->status == 0 ? 'Pending' : ($doctor->status == 1 ? 'Approved' : 'Rejected') }}
                                                            </span>
                                                        </td>



                                                        <td class="action_btn">
                                                            <div class="dropdown">
                                                                <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                    data-bs-toggle="dropdown" aria-expanded="true">
                                                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                </a>
                                                                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                    <li><a class="dropdown-item Satoshi" data-bs-toggle="modal"
                                                                            href="#clinic-request-modal"
                                                                            data-email="{{ $doctor->email ?? '' }}"
                                                                            data-cashback="{{ $doctor->cashback_percentage ?? '' }}"
                                                                            data-id="{{ $doctor->id ?? '' }}"
                                                                            data-name="{{ $doctor->name ?? '' }}"
                                                                            data-payment_type="{{ $doctor->type ?? '' }}"
                                                                            data-type_changed_at="{{ $doctor->latestUserType->created_at ?? '' }}"
                                                                            data-id_document="{{ $doctor->profile->id_document ?? '' }}"
                                                                            data-professional_type="{{ $doctor->profile->professional_type ?? '' }}"
                                                                            data-role="Prescriber (individual)"
                                                                            data-idemnity_certificate="{{ $doctor->profile->idemnity_certificate ?? '' }}"
                                                                            data-status="{{ $doctor->status ?? 0 }}">
                                                                            {{ $doctor->status != 0 ? 'Edit' : 'Request' }}</a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>


                                                    </tr>
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="4" class="text-center">No data found</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>

                                    <div class="d-flex justify-content-between align-items-center mt-5 paginate_custom">
                                        <p>
                                            Showing {{ $doctors->firstItem() }} to {{ $doctors->lastItem() }} of
                                            {{ $doctors->total() }}
                                            entries
                                        </p>
                                    </div>

                                </div>
                            </div>
                            <!-- {{-- <div class="col-lg-3">
                                                                <div class="purple-card">
                                                                    <div class="d-flex justify-content-between">
                                                                        <div class="d-flex">
                                                                            <div>
                                                                                <img src="{{ asset('website') }}/assets/media/images/doctor-icon.svg"
                                                                                    alt="Doctor-icon">
                                                                            </div>
                                                                            <div class="ms-2">
                                                                                <p class="fs-14 fw-500 mb-0 number-gray-black"> Harold Prohaska Clinic
                                                                                </p>
                                                                                <h6 class="heading-gary mb-0 input-text-gray"> <EMAIL> </h6>
                                                                            </div>
                                                                        </div>
                                                                        <div>
                                                                            <p class="light-green-badge"> Active </p>
                                                                        </div>
                                                                    </div>
                                                                    <hr>
                                                                    <div class="d-flex justify-content-between pb-3 roboto">
                                                                        <h6 class="mb-0 text-gray">
                                                                            <span><img
                                                                                    src="{{ asset('website') }}/assets/media/images/reinger-icon.svg"
                                                                                    alt="Doctor"> </span>
                                                                            Doctors
                                                                        </h6>
                                                                        <h6> 124 </h6>
                                                                    </div>

                                                                    <div class="d-flex justify-content-between pb-3 roboto">
                                                                        <h6 class="mb-0 text-gray">
                                                                            <span><img
                                                                                    src="{{ asset('website') }}/assets/media/images/reinger-icon.svg"
                                                                                    alt="Receptionist"></span>
                                                                            Receptionist
                                                                        </h6>
                                                                        <h6> 124 </h6>
                                                                    </div>
                                                                    <div class="d-flex justify-content-between pb-3 roboto">
                                                                        <h6 class="mb-0 text-gray">
                                                                            <span><img src="{{ asset('website') }}/assets/media/images/user_icon.svg"
                                                                                    alt="Patients"></span>
                                                                            Patients
                                                                        </h6>
                                                                        <h6> 600 </h6>
                                                                    </div>
                                                                    <div class="d-flex justify-content-between pb-3 roboto">
                                                                        <h6 class="mb-0 text-gray">
                                                                            <span><img
                                                                                    src="{{ asset('website') }}/assets/media/images/prescriptions.svg"
                                                                                    alt="Prescriptions"></span>
                                                                            Prescriptions
                                                                        </h6>
                                                                        <h6> 600 </h6>
                                                                    </div>
                                                                    <div class="card-button text-end pt-3">
                                                                        <button class="button-gradient white-color">Deactivate</button>
                                                                    </div>
                                                                </div>
                                                            </div> --}} -->
                        </div>
                    </div>
                    <div class="tab-pane fade custom_tabs" id="staff_doctors" role="tabpanel">
                        <div>
                            <div
                                class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5 flex-wrap">
                                <div>
                                    <h3>Doctor Staff
                                        <!-- <span class="input-text-gray"> ({{ $staff_doctors->total() }})</span> -->
                                    </h3>
                                </div>
                                <div class="search_box">
                                    <input type="search" id="" value="" name="search_admin" class="form-control"
                                        placeholder="Search By Name">
                                </div>
                            </div>
                            <div
                                class="custom-dropdown roboto d-flex justify-content-md-end justify-content-start align-items-end pb-5 gap-2 flex-wrap">

                                <div class="result-container1">
                                    <div class="range-options additional-options">
                                        <div
                                            class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group flex-sm-row flex-column">
                                            <div class="start-range date-field">
                                                <label for="adminStartRange">Start Range:</label>
                                                <span class="error-message text-danger"
                                                    style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                    select Start Range
                                                    first.</span>
                                                <input type="date" class="start_range-input start-range-input"
                                                    name="adminStartRange" value="{{ $adminStartRange ?? '' }}"
                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                            </div>

                                            <div class="end-range date-field">
                                                <label for="adminEndRange">End Range:</label>
                                                <input type="date" class="end-range-input end-range-input"
                                                    name="adminEndRange" value="{{ $adminEndRange ?? '' }}"
                                                    max="{{ \Carbon\Carbon::today()->toDateString() }}">

                                            </div>
                                        </div>

                                    </div>
                                </div>
                                <div class="custom-select">
                                    <select id="" class="status_filter" data-control="select2" data-hide-search="true"
                                        data-dropdown-css-class="w-200px">
                                        <option value="" selected>All</option>
                                        <option value="0">Pending</option>
                                        <option value="1">Approved</option>
                                        <option value="2">Rejected</option>
                                    </select>
                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="clinicTable" class="table display  gy-5 gs-5 sortTable">
                                <thead>
                                    <tr>
                                        <th class="min-w-300px">Clinic Name</th>
                                        <th class="min-w-300px">Admin Name</th>
                                        <th class="min-w-200px">Date Requested</th>
                                        <th class="min-w-200px">Status</th>
                                        <th class="min-w-50px first-and-last"></th>
                                    </tr>
                                </thead>
                                <tbody id="staff_doctors_div">
                                    @if(count($staff_doctors) > 0)
                                        @foreach ($staff_doctors as $staff_doctor)
                                            <tr id="row{{ $staff_doctor->id }}">
                                                <td>
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                            alt="" height="40px" width="40px">
                                                        <div class="d-flex flex-column">
                                                            <h5 class="fw-500 number-gray-black">
                                                                {{ $staff_doctor->name ?? '' }}
                                                            </h5>
                                                            <span
                                                                class="input-text-gray fs-14 fw-400">{{ $staff_doctor->email ?? '' }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ $staff_doctor->createdBy->name ?? '' }}</td>
                                                <td>{{ $staff_doctor->created_at->format('m d Y') ?? '' }}</td>
                                                <td class="user-status">
                                                    <span
                                                        class="badge badge-{{ $staff_doctor->status == 0 ? 'inactive' : ($staff_doctor->status == 1 ? 'active' : 'inactive') }} roboto fs-14 fw-400">
                                                        {{ $staff_doctor->status == 0 ? 'Pending' : ($staff_doctor->status == 1 ? 'Approved' : 'Rejected') }}
                                                    </span>
                                                </td>

                                                <td class="action_btn">
                                                    <div class="dropdown">
                                                        <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                            data-bs-toggle="dropdown" aria-expanded="true">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                            <li> <a class="dropdown-item Satoshi" data-bs-toggle="modal"
                                                                    href="#clinic-request-modal"
                                                                    data-email="{{ $staff_doctor->email ?? '' }}"
                                                                    data-id="{{ $staff_doctor->id ?? '' }}"
                                                                    data-name="{{ $staff_doctor->name ?? '' }}"
                                                                    data-payment_type="{{ $staff_doctor->type ?? '' }}"
                                                                    data-id_document="{{ $staff_doctor->profile->id_document ?? '' }}"
                                                                    data-professional_type="{{ $staff_doctor->profile->professional_type ?? '' }}"
                                                                    data-role="Prescriber (staff)"
                                                                    data-idemnity_certificate="{{ $staff_doctor->profile->idemnity_certificate ?? '' }}"
                                                                    data-status="{{ $staff_doctor->status ?? 0 }}">
                                                                    {{ $staff_doctor->status != 0 ? 'Edit' : 'Request' }}
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="5" class="text-center">No data found</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-between align-items-center mt-5 paginate_custom">
                                <p>
                                    Showing {{ $staff_doctors->firstItem() }} to {{ $staff_doctors->lastItem() }} of
                                    {{ $staff_doctors->total() }}
                                    entries
                                </p>
                            </div>

                        </div>
                    </div>
                    {{-- <div class="tab-pane fade custom_tabs" id="staff_receptionists" role="tabpanel">
                        <div
                            class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                            <h3>Receptionists<span class="input-text-gray">
                                    ({{ $staff_receptionists->total() }})</span></h3>
                            <div class="custom-select">
                                <select id="statusFilter" class="status_filter">
                                    <option value="">All</option>
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table id="clinicTable" class="display data_table gy-5 gs-5 sortTable">
                                <thead>
                                    <tr>
                                        <th class="min-w-300px">Receptionists Name</th>
                                        <th class="min-w-300px">Admin Name</th>
                                        <th class="min-w-200px">Date Requested</th>
                                        <th class="min-w-200px">Status</th>
                                        <th class="min-w-50px"></th>
                                    </tr>
                                </thead>
                                <tbody id="staff_receptionists_div">
                                    @foreach ($staff_receptionists as $recepionist)
                                    <tr id="row{{ $recepionist->id }}">
                                        <td>
                                            <div class="d-flex gap-2 align-items-center">
                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                    alt="" height="40px" width="40px">
                                                <div class="d-flex flex-column">
                                                    <h5 class="fw-500 number-gray-black">
                                                        {{ $recepionist->name ?? '' }}</h5>
                                                    <span class="input-text-gray fs-14 fw-400">{{ $recepionist->email ??
                                                        '' }}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $recepionist->createdBy->name ?? '' }}</td>
                                        <td>{{ $recepionist->created_at->format('m d Y') }}</td>
                                        <td class="user-status"><span
                                                class="badge badge-{{ $recepionist->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{
                                                $recepionist->status_text ?? '' }}</span>
                                        </td>
                                        <td class="action_btn">
                                            <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                            </a>
                                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600
                                                                                    menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                data-kt-menu="true">

                                                <div class="menu-item px-3">
                                                    <a class="menu-link px-3 listing_action_req" data-bs-toggle="modal"
                                                        href="#clinic-request-modal"
                                                        data-email="{{ $recepionist->email ?? '' }}"
                                                        data-id="{{ $recepionist->id ?? '' }}"
                                                        data-name="{{ $recepionist->name ?? '' }}"
                                                        data-payment_type="{{ $recepionist->type ?? '' }}"
                                                        data-id_document="{{ $recepionist->profile->id_document ?? '' }}"
                                                        data-professional_type="{{ $recepionist->profile->professional_type ?? '' }}"
                                                        data-role="Receptionist"
                                                        data-idemnity_certificate="{{ $recepionist->profile->idemnity_certificate ?? '' }}">
                                                        Request
                                                    </a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                            <div id="paginationLinks" class="ajax-pagination">
                                {{ $staff_receptionists->links() }}
                            </div>
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </div>
    </div>
    @include('dashboard.templates.modals.admin-modals.clinic_request_modal')
    </section>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

    <script>
        $(document).ready(function () {
            let searchTimeout;

            // Handle search input
            $('input[name="search_admin"]').on('input', function () {
                const searchInput = $(this);
                const tab = searchInput.closest('.tab-pane').attr('id');

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout
                searchTimeout = setTimeout(() => {
                    fetchAdmins(1, tab, searchInput.val());
                }, 500); // 500ms delay
            });

            $('.status_filter').on('change', function () {
                const tab = $(this).closest('.tab-pane').attr('id');
                const searchValue = $(`#${tab} input[name="search_admin"]`).val();
                fetchAdmins(1, tab, searchValue);
            });

            function fetchAdmins(page = 1, tab = null, search = '') {
                var activeTab = tab || $('.nav-tabs .nav-link.active').attr('href').replace('#', '');
                var selectedFilter = $(`#${activeTab}`).find('.status_filter').val();

                $.ajax({
                    url: '{{ route('user.requests.status') }}?page=' + page,
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        filter: selectedFilter,
                        tab: activeTab,
                        search: search
                    },
                    success: function (response) {
                        if (response.success) {
                            switch (response.value) {
                                case 'admins':
                                    updateAdminTable(response);
                                    break;
                                case 'doctors':
                                    updateDoctorTable(response);
                                    break;
                                case 'staff_doctors':
                                    updateStaffDoctorTable(response);
                                    break;
                                case 'staff_receptionists':
                                    updateStaffReceptionistTable(response);
                                    break;
                            }
                        }
                        setTimeout(() => {
                            KTMenu.createInstances();
                        }, 100);
                    },
                    error: function (xhr, status, error) {
                        if (xhr.status === 422) {
                            // Handle validation errors
                            var errors = xhr.responseJSON.errors;
                            if (errors && errors.cashback_percentage) {
                                Swal.fire({
                                    title: 'Validation Error!',
                                    text: errors.cashback_percentage[0],
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            } else {
                                Swal.fire({
                                    title: 'Validation Error!',
                                    text: 'Please check your input and try again.',
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                        } else if (xhr.responseJSON && xhr.responseJSON.message) {
                            // Handle custom error messages
                            Swal.fire({
                                title: 'Error!',
                                text: xhr.responseJSON.message,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'An error occurred while processing your request.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                        console.log(error);
                    }
                });
            }

            function updateAdminTable(response) {
                let admins = response.data.data;
                $('#admin').empty();
                if (admins.length > 0) {
                    admins.forEach(admin => {
                        let formattedDate = admin.created_at ? moment(admin.created_at).format('DD MM YYYY') :
                            '';
                        let requestText = admin.status != 0 ? 'Edit' : 'Request';
                        let statusText = admin.status == 0 ? 'Pending' : admin.status == 1 ? 'Approved' : 'Rejected';
                        let statusClass = admin.status == 0 ? 'inactive' : admin.status == 1 ? 'active' : 'inactive';
                        $('#admin').append(`
                                        <tr id="row${admin.id}">
                                            <td>
                                                <div class="d-flex gap-2 align-items-center">
                                                    <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                        alt="" height="40px" width="40px">
                                                    <div class="d-flex flex-column">
                                                        <h5 class="fw-500 number-gray-black">${admin.name ?? ''}</h5>
                                                        <span class="input-text-gray fs-14 fw-400">${admin.email ?? ''}</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>${formattedDate}</td>
                                            <td class="user-status">
                                                <span class="badge badge-${statusClass} roboto fs-14 fw-400">
                                                    ${statusText}
                                                </span>
                                            </td>

                                            <td class="action_btn">
                                                                        <div class="dropdown">
                                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                                data-bs-toggle="dropdown" aria-expanded="true">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </a>
                                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                                <li><a class="dropdown-item Satoshi menu-link px-3 listing_action_req" data-bs-toggle="modal"
                                                            href="#clinic-request-modal"
                                                            data-email="${admin.email ?? ''}"
                                                            data-name="${admin.name ?? ''}"
                                                            data-cashback="${admin.cashback_percentage ?? ''}"
                                                            data-payment_type="${admin.type ?? ''}"
                                                            data-type_changed_at="${admin.latestUserType?.created_at || ''}"
                                                            data-id="${admin.id ?? ''}"
                                                            data-status="${admin.status ?? 0}"
                                                            data-id_document="${admin.profile?.id_document ?? ''}"
                                                            data-professional_type="${admin.profile?.professional_type ?? ''}"
                                                            data-role="Clinic Admin"
                                                            data-idemnity_certificate="${admin.profile?.idemnity_certificate ?? ''}">
                                                            ${requestText}
                                                        </a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </td>




                                        </tr>
                                    `);
                    });
                } else {
                    $('#admin').append(`
                                                    <tr>
                                                        <td colspan="4" class="text-center">No data found</td>
                                                    </tr>
                                                `);
                }

                // Update pagination information
                let paginationInfo = $('#admins .paginate_custom');
                paginationInfo.html(`
                                                <p>
                                                    Showing ${response.data.from || 0} to ${response.data.to || 0} of
                                                    ${response.data.total || 0} entries
                                                </p>
                                            `);
            }

            function updateDoctorTable(response) {
                let doctors = response.data.data;
                $('#doctors_div').empty();
                if (doctors.length > 0) {
                    doctors.forEach(doctor => {
                        let formattedDate = doctor.created_at ? moment(doctor.created_at).format('DD MM YYYY') :
                            '';
                        let requestText = doctor.status != 0 ? 'Edit' : 'Request';
                        let statusText = doctor.status == 0 ? 'Pending' : doctor.status == 1 ? 'Approved' : 'Rejected';
                        let statusClass = doctor.status == 0 ? 'inactive' : doctor.status == 1 ? 'active' : 'inactive';
                        $('#doctors_div').append(`
                                        <tr id="row${doctor.id}">
                                            <td>
                                                <div class="d-flex gap-2 align-items-center">
                                                    <img src="https://rx-direct.democustomprojects.com/website/assets/media/images/doctor-icon.svg" alt="" height="40px" width="40px">
                                                    <div class="d-flex flex-column">
                                                        <h5 class="fw-500 number-gray-black">${doctor.name || ''}</h5>
                                                        <span class="input-text-gray fs-14 fw-400">${doctor.email || ''}</span>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>${formattedDate}</td>
                                            <td class="user-status">
                                                <span class="badge badge-${statusClass} roboto fs-14 fw-400">
                                                    ${statusText}
                                                </span>
                                            </td>


                                            <td class="action_btn">
                                                                        <div class="dropdown">
                                                                            <a class="nav-link" href="#" role="button" id="dropdownMenuLink"
                                                                                data-bs-toggle="dropdown" aria-expanded="true">
                                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                            </a>
                                                                            <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                                                <li> <a class=" dropdown-item Satoshi px-3 listing_action_req" data-bs-toggle="modal"
                                                            href="#clinic-request-modal"
                                                            data-email="${doctor.email || ''}"
                                                            data-id="${doctor.id || ''}"
                                                            data-name="${doctor.name || ''}"
                                                            data-cashback="${doctor.cashback_percentage || ''}"
                                                            data-payment_type="${doctor.type || ''}"
                                                            data-type_changed_at="${doctor.latest_user_type?.created_at || ''}"
                                                            data-id_document="${doctor.profile?.id_document || ''}"
                                                            data-professional_type="${doctor.profile?.professional_type || ''}"
                                                            data-role="Prescriber (individual)"
                                                            data-idemnity_certificate="${doctor.profile?.idemnity_certificate || ''}">
                                                            ${requestText}
                                                        </a>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    </td>
                                        </tr>
                                    `);
                    });
                } else {
                    $('#doctors_div').append(`
                                                    <tr>
                                                        <td colspan="4" class="text-center">No data found</td>
                                                    </tr>
                                                `);
                }

                // Update pagination information
                let paginationInfo = $('#doctors .paginate_custom');
                paginationInfo.html(`
                                                <p>
                                                    Showing ${response.data.from || 0} to ${response.data.to || 0} of
                                                    ${response.data.total || 0} entries
                                                </p>
                                            `);
            }

            function updateStaffDoctorTable(response) {
                let staff_doctors = response.data.data;
                $('#staff_doctors_div').empty();
                if (staff_doctors.length > 0) {
                    staff_doctors.forEach((doctor, index) => {
                        let formattedDate = doctor.created_at ? moment(doctor.created_at).format('DD MM YYYY') :
                            '';
                        let requestText = doctor.status != 0 ? 'Edit' : 'Request';
                        let statusText = doctor.status == 0 ? 'Pending' : doctor.status == 1 ? 'Approved' : 'Rejected';
                        let statusClass = doctor.status == 0 ? 'inactive' : doctor.status == 1 ? 'active' : 'inactive';
                        $('#staff_doctors_div').append(`
                                                        <tr id="row${doctor.id}">
                                                            <td>
                                                                <div class="d-flex gap-2 align-items-center">
                                                                    <img src="https://rx-direct.democustomprojects.com/website/assets/media/images/doctor-icon.svg" alt="" height="40px" width="40px">
                                                                    <div class="d-flex flex-column">
                                                                        <h5 class="fw-500 number-gray-black">${doctor.name ?? ''}</h5>
                                                                        <span class="input-text-gray fs-14 fw-400">${doctor.email ?? ''}</span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>${doctor.created_by.name}</td>
                                                            <td>${formattedDate}</td>
                                                            <td class="user-status">
                                                                <span class="badge badge-${statusClass} roboto fs-14 fw-400">
                                                                    ${statusText}
                                                                </span>
                                                            </td>
                                                            <td class="action_btn">
                                                                <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                                    data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                                    <i class="fa-solid fa-ellipsis-vertical"></i>
                                                                </a>
                                                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600
                                                                    menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">
                                                                    <div class="menu-item px-3">
                                                                        <a class="menu-link px-3 listing_action_req" data-bs-toggle="modal"
                                                                            href="#clinic-request-modal"
                                                                            data-email="${doctor.email ?? ''}"
                                                                            data-id="${doctor.id ?? ''}"
                                                                            data-name="${doctor.name ?? ''}"
                                                                            data-payment_type="${doctor.type ?? ''}"
                                                                            data-id_document="${doctor.profile?.id_document ?? ''}"
                                                                            data-professional_type="${doctor.profile?.professional_type ?? ''}"
                                                                            data-role="Prescriber (staff)"
                                                                            data-idemnity_certificate="${doctor.profile?.idemnity_certificate ?? ''}">
                                                                            ${requestText}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    `);
                    });
                } else {
                    $('#staff_doctors_div').append(`
                                                    <tr>
                                                        <td colspan="5" class="text-center">No data found</td>
                                                    </tr>
                                                `);
                }

                // Update pagination information
                let paginationInfo = $('#staff_doctors .paginate_custom');
                paginationInfo.html(`
                                                <p>
                                                    Showing ${response.data.from || 0} to ${response.data.to || 0} of
                                                    ${response.data.total || 0} entries
                                                </p>
                                            `);
            }

            function updateStaffReceptionistTable(response) {
                let staffReceptionists = response.data.data;
                $('#staff_receptionists_div').empty();
                staffReceptionists.forEach((receptionist) => {
                    let formattedDate = receptionist.created_at ? moment(receptionist.created_at).format(
                        'DD MM YYYY') : '';
                    let requestText = receptionist.status != 0 ? 'Edit' : 'Request';
                    let statusText = receptionist.status == 0 ? 'Pending' : receptionist.status == 1 ? 'Approved' : 'Rejected';
                    let statusClass = receptionist.status == 0 ? 'inactive' : receptionist.status == 1 ? 'active' : 'inactive';
                    $('#staff_receptionists_div').append(`
                                                    <tr id="row${receptionist.id}">
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="https://rx-direct.democustomprojects.com/website/assets/media/images/table-featured-icon.svg" alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">${receptionist.name ?? ''}</h5>
                                                                    <span class="input-text-gray fs-14 fw-400">${receptionist.email ?? ''}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td>${receptionist.created_by.name ?? ''}</td>
                                                        <td>${formattedDate}</td>
                                                        <td class="user-status">
                                                            <span class="badge badge-${statusClass} roboto fs-14 fw-400">
                                                                ${statusText}
                                                            </span>
                                                        </td>
                                                        <td class="action_btn">
                                                            <a href="#" class="btn btn-sm btn-flex btn-center button action_btn"
                                                                data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                                <i class="fa-solid fa-ellipsis-vertical"></i>
                                                            </a>
                                                            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600
                                                                menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4" data-kt-menu="true">

                                                                <div class="menu-item px-3">
                                                                    <a class="menu-link px-3 listing_action_req" data-bs-toggle="modal"
                                                                        href="#clinic-request-modal"
                                                                        data-email="${receptionist.email ?? ''}"
                                                                        data-id="${receptionist.id ?? ''}"
                                                                        data-name="${receptionist.name ?? ''}"
                                                                        data-payment_type="${receptionist.type ?? ''}"
                                                                        data-id_document="${receptionist.profile?.id_document ?? ''}"
                                                                        data-professional_type="${receptionist.profile?.professional_type ?? ''}"
                                                                        data-role="Receptionist"
                                                                        data-idemnity_certificate="${receptionist.profile?.idemnity_certificate ?? ''}">
                                                                        ${requestText}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                `);
                });

                // Update pagination information
                let paginationInfo = $('#staff_receptionists .paginate_custom');
                paginationInfo.html(`
                                                <p>
                                                    Showing ${response.data.from || 0} to ${response.data.to || 0} of
                                                    ${response.data.total || 0} entries
                                                </p>
                                            `);
            }

            $(document).off('click', '.ajax-pagination a').on('click', '.ajax-pagination a', function (event) {
                event.preventDefault();
                let page = $(this).attr('href').split('page=')[1];
                fetchAdmins(page);
            });

            var documentUrl = null;
            var idemityUrl = null;
            var originalPaymentType = null;
            var typeChangedAt = null;

            $('#clinic-request-modal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var email = button.data('email');
                var cashback = button.data('cashback');
                var name = button.data('name');
                var id = button.data('id');
                var role = button.data('role');
                var status = button.data('status');
                var id_document = button.data('id_document');
                var professional_type = button.data('professional_type');
                var idemnity_certificate = button.data('idemnity_certificate');
                var payment_type = button.data('payment_type');
                var type_changed_at = button.data('type_changed_at');


                documentUrl = id_document;
                idemityUrl = idemnity_certificate;
                originalPaymentType = payment_type;
                typeChangedAt = type_changed_at;

                let modalTitle = status == 1 ? 'Edit' : 'Request';
                $('#modal_title').text(role + ' ' + modalTitle);

                // Show modal footer first
                $('.modal-footer').show();

                // Handle button visibility and text based on status
                if (status != 0) {
                    if (status == 1) {
                        $('#reject_btn').show().text('Deactivate');
                        $('#accept_btn').hide();
                        $('#update_btn').show().text('Update');
                    } else if (status == 2) {
                        $('#reject_btn').hide();
                        $('#accept_btn').show().text('Activate');
                        $('#update_btn').show().text('Update');
                    }
                } else {
                    $('#reject_btn').show().text('Reject');
                    $('#accept_btn').show().text('Approve');
                    $('#update_btn').hide();
                }

                $('#name').text(name);
                $('#email_text').text(email);
                $('#professional_type').text(professional_type);
                $('#role').val(role);
                $('#user_id').val(id);
                console.log('Cashback value:', cashback);
                setTimeout(function () {
                    $('#cashback').val(cashback);
                    console.log('Cashback input value after setting:', $('#cashback').val());
                }, 100);

                if (role === 'Clinic Admin' || role === 'Prescriber (individual)') {
                    $('.clincDiv').show();
                    $('#paymentType').prop('disabled', false);
                    if (payment_type == 'Type 2') {
                        $('#paymentType').prop('checked', true);
                    } else {
                        $('#paymentType').prop('checked', false);
                    }
                    $('.clincFields').prop('disabled', false);
                } else {
                    $('.clincDiv').hide();
                    $('.clincFields').prop('disabled', true);
                    $('#paymentType').prop('disabled', true);
                }
            });
            $('.action-button').click(function (e) {
                e.preventDefault();
                isValid = false;
                var status = $(this).data('status');
                var buttonText = $(this).text().toLowerCase();



                if (buttonText === 'reject' || buttonText === 'deactivate') {
                    $('.reason-rejected').show();
                    let reason = $('.reason-rejected textarea').val().trim();
                    if (reason === '') {
                        $('#error-message').show();
                        isValid = false;
                    } else {
                        $('#error-message').hide();
                        isValid = true;
                    }
                } else {
                    $('.reason-rejected').hide();
                    isValid = true;
                }

                if (isValid) {
                    var formData = $('#user_details_form').serialize();
                    var typeValue;
                    if ($('#paymentType').prop('disabled')) {
                        typeValue = null;
                    } else {
                        typeValue = $('#paymentType').prop('checked') ? 'Type 2' : 'Type 1';
                    }
                    var extraData = {
                        type: typeValue,
                        is_update: $(this).attr('id') === 'update_btn'
                    };
                    if ($(this).attr('id') !== 'update_btn') {
                        extraData.status = $(this).attr('data-status');
                    }
                    formData += '&' + $.param(extraData);

                    $.ajax({
                        url: $('#user_details_form').attr('action'),
                        type: 'POST',
                        data: formData,
                        success: function (response) {
                            var row = $(`#row${response.data.id}`);
                            var status = response.data.status;
                            var statusText = status == 0 ? 'Pending' : status == 1 ? 'Approved' : 'Rejected';
                            var badgeClass = status == 0 ? 'pending' : status == 1 ? 'active' : 'inactive';

                            // Update status badge
                            row.find('.user-status').empty().html(
                                `<span class="badge badge-${badgeClass} roboto fs-14 fw-400">
                                                                ${statusText}
                                                            </span>`
                            );

                            // Update action menu text
                            row.find('.listing_action_req').text(status != 0 ? 'Edit' : 'Request');

                            // Update all data attributes with the new values from response
                            var actionLink = row.find('.listing_action_req');
                            actionLink.data('status', status);
                            actionLink.data('payment_type', response.data.type);
                            actionLink.data('cashback', response.data.cashback_percentage);
                            actionLink.data('email', response.data.email);
                            actionLink.data('name', response.data.name);
                            actionLink.data('id', response.data.id);
                            if (response.data.profile) {
                                actionLink.data('id_document', response.data.profile.id_document);
                                actionLink.data('professional_type', response.data.profile.professional_type);
                                actionLink.data('idemnity_certificate', response.data.profile.idemnity_certificate);
                            }

                            Swal.fire({
                                title: 'Success!',
                                text: 'Record updated successfully!',
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                $('#user_details_form')[0].reset();
                                $('#clinic-request-modal').modal('hide');
                            });

                            console.log(response);
                        },
                        error: function (xhr, status, error) {
                            if (xhr.status === 422) {
                                // Handle validation errors
                                var errors = xhr.responseJSON.errors;
                                if (errors && errors.cashback_percentage) {
                                    Swal.fire({
                                        title: 'Validation Error!',
                                        text: errors.cashback_percentage[0],
                                        icon: 'error',
                                        confirmButtonText: 'OK'
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Validation Error!',
                                        text: 'Please check your input and try again.',
                                        icon: 'error',
                                        confirmButtonText: 'OK'
                                    });
                                }
                            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                                // Handle custom error messages
                                Swal.fire({
                                    title: 'Error!',
                                    text: xhr.responseJSON.message,
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: 'An error occurred while processing your request.',
                                    icon: 'error',
                                    confirmButtonText: 'OK'
                                });
                            }
                            console.log(error);
                        }
                    });
                }
            });

            // Add event handler for payment type toggle
            $(document).on('change', '#paymentType', function () {
                var currentToggleState = $(this).is(':checked');
                var currentType = currentToggleState ? 'Type 2' : 'Type 1';
                var $validationMessage = $('#paymentTypeValidation');
                var $nextChangeDate = $('#nextChangeDate');

                console.log('Payment type changed:', {
                    currentToggleState: currentToggleState,
                    currentType: currentType,
                    originalPaymentType: originalPaymentType,
                    typeChangedAt: typeChangedAt
                });

                // Check if user is trying to change from the original type
                if (currentType !== originalPaymentType && typeChangedAt) {
                    console.log('Type changed from original, checking date restrictions');
                    var changeDate = new Date(typeChangedAt);
                    var currentDate = new Date();
                    var monthsDiff = (currentDate.getFullYear() - changeDate.getFullYear()) * 12 +
                        (currentDate.getMonth() - changeDate.getMonth());

                    console.log('Date calculations:', {
                        changeDate: changeDate,
                        currentDate: currentDate,
                        monthsDiff: monthsDiff
                    });

                    if (monthsDiff < 3) {
                        console.log('Change rejected - within 3 month restriction period');
                        // Calculate next available date (3 months from last change)
                        var nextAvailableDate = new Date(changeDate);
                        nextAvailableDate.setMonth(nextAvailableDate.getMonth() + 3);

                        // Format the date
                        var formattedDate = nextAvailableDate.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: 'short',
                            year: 'numeric'
                        });

                        console.log('Next available date:', {
                            nextAvailableDate: nextAvailableDate,
                            formattedDate: formattedDate
                        });

                        // Show validation message with next available date
                        $nextChangeDate.text(formattedDate);
                        $validationMessage.show();

                        // Revert toggle to original state
                        $(this).prop('checked', originalPaymentType === 'Type 2');

                        return false;
                    }
                }

                console.log('Change allowed - hiding validation message');
                // Hide validation message if change is allowed
                $validationMessage.hide();
            });

            // Hide validation message when modal is opened
            $('#clinic-request-modal').on('show.bs.modal', function (event) {
                $('#paymentTypeValidation').hide();
                var button = $(event.relatedTarget);
                var email = button.data('email');
                var cashback = button.data('cashback');
                var name = button.data('name');
                var id = button.data('id');
                var role = button.data('role');
                var status = button.data('status');
                var id_document = button.data('id_document');
                var professional_type = button.data('professional_type');
                var idemnity_certificate = button.data('idemnity_certificate');
                var payment_type = button.data('payment_type');
                var type_changed_at = button.data('type_changed_at');

                documentUrl = id_document;
                idemityUrl = idemnity_certificate;
                originalPaymentType = payment_type;
                typeChangedAt = type_changed_at;

                // ... rest of the existing modal show code ...
            });

            var baseUrl = "{{ asset('website') }}";
            $("#download-id-document, #download-idemity-document").on("click", function (e) {
                e.preventDefault();
                const isIdDocument = $(this).attr('id') === 'download-id-document';
                const url = isIdDocument ? documentUrl : idemityUrl;
                const docType = isIdDocument ? 'ID-Certificate' : 'Idemnity-Certificate';

                if (url) {
                    const fullUrl = baseUrl + '/' + url;
                    // Create a temporary link element
                    const link = document.createElement('a');
                    link.href = fullUrl;
                    link.download = docType + '.pdf'; // Set download filename
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            });

            if (window.location.hash) {
                $('.nav-link').removeClass('active');
                $('.tab-pane').removeClass('show active');

                let hash = window.location.hash;
                $(`.nav-link[href="${hash}"]`).addClass('active');
                $(hash).addClass('show active');
            }
            // const table = $('.data_table').DataTable({
            //     paging: false,
            //     pageLength: 5,
            //     lengthChange: false,
            //     searching: false,
            //     info: false,
            //     ordering: false,
            //     columnDefs: [{
            //         orderable: false,
            //         targets: 0
            //     }],
            //     language: {
            //         paginate: {
            //             previous: '<i class="fa fa-chevron-left"></i>',
            //             next: '<i class="fa fa-chevron-right"></i>'
            //         }
            //     }
            // });

            $('#statusFilter').on('change', function () {
                const status = $(this).val();
                table.column(7).search(status, true, false).draw();
            });

            $('.select-all').on('click', function () {
                const isChecked = $(this).prop('checked');
                $('.row-select').prop('checked', isChecked);
            });

            $('.row-select').on('click', function () {
                if (!$('.row-select:checked').length) {
                    $('#select-all').prop('checked', false);
                }
            });

            $(document).on('change', '.status_filter, .month_filter', function () {
                let activeRole = getActiveRole();
                let activeType = getActiveType();

                // Reset pagination before loading new data
                if (activeRole === 'clinic_admin') {
                    updatePaginationInfo($('#Clinic-admin .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
                } else if (activeRole === 'doctor') {
                    updatePaginationInfo($('#Prescribers .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
                } else {
                    updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
                }

                getInvoices();
            });
        });

        $('#statusFilter').on('change', function () {
            const status = $(this).val();
            table.column(7).search(status, true, false).draw();
        });

        $('.select-all').on('click', function () {
            const isChecked = $(this).prop('checked');
            $('.row-select').prop('checked', isChecked);
        });

        $('.row-select').on('click', function () {
            if (!$('.row-select:checked').length) {
                $('#select-all').prop('checked', false);
            }
        });

        $(document).on('change', '.status_filter, .month_filter', function () {
            let activeRole = getActiveRole();
            let activeType = getActiveType();

            // Reset pagination before loading new data
            if (activeRole === 'clinic_admin') {
                updatePaginationInfo($('#Clinic-admin .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
            } else if (activeRole === 'doctor') {
                updatePaginationInfo($('#Prescribers .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
            } else {
                updatePaginationInfo($('.table-all-invoices .d-flex.justify-content-between'), { from: 0, to: 0, total: 0 });
            }

            getInvoices();
        });

    </script>
@endpush
