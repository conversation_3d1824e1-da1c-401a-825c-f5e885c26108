<div class="modal fade" tabindex="-1" id="add_new_staff_modal">
    <div class="modal-dialog new-staff modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header ">
                <h4 class="modal-title white-color inter w-100 text-center">Create new Staff Member</h4>
            </div>
            <form id="staff-form" action="<?php echo e(route('create.staff')); ?>" method="POST" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="modal-body new-staff-modal pb-0">
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Title</h5>
                                <select class="form-select px-0 pt-0 <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    name="title">
                                    <option value="" selected disabled>Select an option</option>
                                    <option value="Dr." <?php echo e(old('title') == 'Dr.' ? 'selected' : ''); ?>>Dr.</option>
                                    <option value="Nurse" <?php echo e(old('title') == 'Nurse' ? 'selected' : ''); ?>>Nurse
                                    </option>
                                    <option value="Pharmacist" <?php echo e(old('title') == 'Pharmacist' ? 'selected' : ''); ?>>
                                        Pharmacist</option>
                                    <option value="Ward boy" <?php echo e(old('title') == 'Ward boy' ? 'selected' : ''); ?>>Ward boy
                                    </option>
                                </select>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Full Name</h5>
                                <input type="text" placeholder="As per official documents"
                                    class="w-100 <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="name"
                                    value="<?php echo e(old('name')); ?>" />
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Date of Birth</h5>
                                <input type="date" name="dob" class="w-100 <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    value="<?php echo e(old('dob')); ?>" />
                                <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Health Care Professional Type</h5>
                                <select class="form-select px-0 pt-0 <?php $__errorArgs = ['professional_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    name="professional_type">
                                    <option selected value="" disabled>Select an option</option>
                                    <option value="Nurse" <?php echo e(old('professional_type') == 'Nurse' ? 'selected' : ''); ?>>
                                        Nurse</option>
                                    <option value="Pharmacist"
                                        <?php echo e(old('professional_type') == 'Pharmacist' ? 'selected' : ''); ?>>Pharmacist
                                    </option>
                                    <option value="Ward boy"
                                        <?php echo e(old('professional_type') == 'Ward boy' ? 'selected' : ''); ?>>Ward boy
                                    </option>
                                </select>
                                <?php $__errorArgs = ['professional_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Registration Number</h5>
                                <input type="text" placeholder="Enter your professional reg. no."
                                    class="w-100 <?php $__errorArgs = ['reg_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="reg_no"
                                    value="<?php echo e(old('reg_no')); ?>" />
                                <?php $__errorArgs = ['reg_no'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Mobile Number</h5>
                                <input type="number" placeholder="Your mobile contact no."
                                    class="w-100 <?php $__errorArgs = ['mobile_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="mobile_number"
                                    value="<?php echo e(old('mobile_number')); ?>" />
                                <?php $__errorArgs = ['mobile_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
                         <div class="row">
                        <div id="doctorDocs" style="display: none;">
                            <div class="col-md-12 border-line">
                                <div class="clinic-certificate mb-7 pt-4">
                                    <div class="upload-container upload_doc pt-5">
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/images/upload_doc.svg"
                                            alt="">
                                        <p class="fs-14 fw-500 text-center my-3">Upload Photo ID document (Passport or
                                            Drivers Licence)</p>
                                        <p class="drag-text text-center fs-12 fw-500">
                                            Drag a File here or
                                            <label class="uploadLabel">Upload a Document</label>
                                        </p>
                                        <input type="file"
                                            class="fileUpload <?php $__errorArgs = ['id_document'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            accept=".png,.jpg,.jpeg,.pdf" style="display: none;" name="id_document"
                                            id="id_document">
                                        <?php $__errorArgs = ['id_document'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 border-line">
                                <div class="clinic-certificate mb-7 pt-4">
                                    <div class="upload-container upload_doc pt-5">
                                        <img src="<?php echo e(asset('website')); ?>/assets/media/images/upload_doc.svg"
                                            alt="">
                                        <p class="fs-14 fw-500 text-center my-3">Upload Indemnity Certificate</p>
                                        <p class="drag-text text-center fs-12 fw-500">
                                            Drag a File here or
                                            <label class="uploadLabel">Upload a Document</label>
                                        </p>
                                        <input type="file"
                                            class="fileUpload <?php $__errorArgs = ['idemnity_certificate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            accept=".png,.jpg,.jpeg,.pdf" style="display: none;"
                                            name="idemnity_certificate" id="idemnity_certificate">
                                        <?php $__errorArgs = ['idemnity_certificate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row my-4">
                            <div class="col-lg-6">
                                <div class="patients-email mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Email Address</h5>
                                    <input type="text" placeholder="Email address to create account"
                                        class="w-100 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email"
                                        value="<?php echo e(old('email')); ?>" />
                                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="patients-email mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Password</h5>
                                    <input type="password" placeholder="Create a strong password"
                                        class="w-100 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" />
                                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="invalid-feedback"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mb-5">
                            <div class="mb-5 border-line">
                                <div class="fs-7 mb-3">Allow staff to add patients</div>
                                <label class="form-check form-switch form-check-custom form-check-solid">
                                    <input class="form-check-input mb-4" name="permissions[]" type="checkbox"
                                        value="patient-create" checked="checked"
                                        <?php echo e(in_array('patient-create', old('permissions', [])) ? 'checked' : ''); ?> />
                                </label>
                            </div>
                            <div class=" mb-5 border-line">
                                <div class="fs-7 mb-3">Allow staff to add prescriptions</div>
                                <label class="form-check form-switch form-check-custom form-check-solid">
                                    <input class="form-check-input mb-4" name="permissions[]" type="checkbox"
                                        value="prescriptions-create" checked="checked"
                                        <?php echo e(in_array('prescriptions-create', old('permissions', [])) ? 'checked' : ''); ?> />
                                </label>
                            </div>
                            <?php $__errorArgs = ['permissions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="text-danger"><?php echo e($message); ?></span>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Role</h5>
                                <select class="form-select px-0 pt-0 <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="role"
                                    id="role">
                                    <option selected value="" disabled>Select a role</option>
                                    <option value="Doctor" <?php echo e(old('role') == 'Doctor' ? 'selected' : ''); ?>>Doctor
                                    </option>
                                    <option value="Receptionist" <?php echo e(old('role') == 'Receptionist' ? 'selected' : ''); ?>>
                                        Receptionist</option>
                                </select>
                                <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="invalid-feedback"><?php echo e($message); ?></span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>
               
                </div>
                <div class="modal-footer justify-content-center gap-3 border-0 pt-0">
                    <button type="button" class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                        data-bs-dismiss="modal">Cancel
                    </button>
                    <button type="submit"
                        class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save">Save
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.querySelectorAll('.fileUpload').forEach((fileInput) => {
        const uploadLabel = fileInput.closest('.upload-container').querySelector('.uploadLabel');

        fileInput.addEventListener('change', function() {
            if (fileInput.files.length > 0) {
                uploadLabel.textContent = `File uploaded: ${fileInput.files[0].name}`;
                uploadLabel.style.color = "#197C48"; // Optional: Change color to indicate success
            }
        });

        // Allow label to trigger file upload
        uploadLabel.addEventListener('click', () => fileInput.click());
    });
</script>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            <?php if($errors->any()): ?>
                <?php if(old('role') == 'Doctor'): ?>
                    $('#doctorDocs').show();
                <?php endif; ?>
                $('#add_new_staff_modal').modal('show');
            <?php endif; ?>

            $('#add_new_staff_modal').on('hidden.bs.modal', function() {
                $('#staff-form')[0].reset(); // Reset all fields in the form
                $('#staff-form').find('.is-invalid').removeClass('is-invalid'); // Remove error styles
                $('#staff-form').find('.invalid-feedback').remove(); // Remove error messages
                $('#staff-form').find('.text-danger').remove(); // Remove error messages
            });
            $('#role').on('change', function() {
                var selectedValue = $(this).val(); // Get selected value

                if (selectedValue === 'Doctor') {
                    $('#id_document').prop('disabled', false);
                    $('#idemnity_certificate').prop('disabled', false);

                    $('#doctorDocs').show();
                } else {
                    $('#id_document').prop('disabled', true);
                    $('#idemnity_certificate').prop('disabled', true);
                    $('#doctorDocs').hide();
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\git\rx-direct\resources\views/dashboard/templates/modals/clinic-modals/new_staff_modal.blade.php ENDPATH**/ ?>