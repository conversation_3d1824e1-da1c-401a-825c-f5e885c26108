<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Prescription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InvoiceController extends Controller
{
    public function fetchInvoices(Request $request)
    {
        $type = $request->input('type');
        $payment_status = $request->input('payment_status');
        $search = $request->input('search');
        $month = $request->input('month');

        // Start building the query
        $query = Invoice::with('user');

        if (Auth::user()->hasRole('admin')) {
            // Admin can see all invoices with role filtering
            $role = $request->input('role');

            if ($role) {
                $query->whereHas('user', function ($q) use ($role) {
                    $q->role($role);
                });
            }

            // Admin search by user name
            if (!empty($search)) {
                $query->whereHas('user', function ($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%');
                });
            }
        } else {
            // Non-admin users can only see their own invoices
            $query->where('user_id', Auth::id());

            // For non-admin users, search doesn't make sense since they only see their own invoices
            // But we can keep it for consistency if needed
        }

        // Apply common filters
        if (!empty($type)) {
            $query->where('type', $type);
        }

        if ($payment_status !== null && $payment_status !== '') {
            $query->where('is_paid', $payment_status);
        }

        // Handle month filtering - convert month name to number
        if (!empty($month)) {
            $monthNumber = $this->getMonthNumber($month);
            if ($monthNumber) {
                $currentYear = Carbon::now()->year;
                $query->whereMonth('month', $monthNumber)
                      ->whereYear('month', $currentYear);
            }
        }

        $invoices = $query->orderBy('created_at', 'desc')->get();

        return response()->json($invoices);
    }

    /**
     * Convert month name to month number
     */
    private function getMonthNumber($monthName)
    {
        $months = [
            'january' => 1,
            'february' => 2,
            'march' => 3,
            'april' => 4,
            'may' => 5,
            'june' => 6,
            'july' => 7,
            'august' => 8,
            'september' => 9,
            'october' => 10,
            'november' => 11,
            'december' => 12,
        ];

        return $months[strtolower($monthName)] ?? null;
    }

    public function adminInvoice()
    {
        $clinic_admins = Invoice::whereHas('user', function ($query) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'clinic_admin');
            });
        })->where('type', 'Type 1')->get();

        $doctors = Invoice::whereHas('user', function ($query) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'doctor');
            });
        })->where('type', 'Type 1')->get();
        return view('dashboard.admin.admin-invoices', compact('clinic_admins', 'doctors'));
    }

    public function generateInvoice()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        // $shouldGenerate = Carbon::now()->isLastOfMonth() && Carbon::now()->diffInMinutes(Carbon::now()->endOfDay()) <= 5;

        // if ($shouldGenerate) {
            $typeOneCount = $this->processInvoiceType('Type 1', $startOfMonth, $endOfMonth);
            $typeTwoCount = $this->processInvoiceType('Type 2', $startOfMonth, $endOfMonth);
            $transactions = $this->processTransaction($startOfMonth, $endOfMonth);
            $totalCount = $typeOneCount + $typeTwoCount;
            return [
                'invoice' => "{$totalCount} invoice(s) generated successfully.",
                'transaction' => "{$transactions} transaction(s) generated successfully.",
            ];
        // }
        return "No new invoices or transactions were generated.";
    }


    private function processInvoiceType($type, $startOfMonth, $endOfMonth)
    {
        $count = 0;
        $prescriptions = Prescription::where('type', $type)
       ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
       ->where(function($query) {
           $query->where('is_dispensed', 1)
               ->orWhereHas('payments', function($q) {
                   $q->where('is_dispensed', 1);
               });
       })
       ->get()
       ->groupBy('clinic_id');

        foreach ($prescriptions as $clinicId => $clinicPrescriptions) {
            $totalAmount = 0;

            foreach ($clinicPrescriptions as $prescription) {
                if ($prescription->prescription_type === 'repeat') {
                    // For repeat prescriptions, sum up the amounts from dispensed payments
                    $totalAmount += $prescription->payments()
                        ->where('is_dispensed', 1)
                        ->whereBetween('payment_date', [$startOfMonth, $endOfMonth])
                        ->sum('total');
                } else {
                    // For one-time prescriptions, use the prescription total
                    $totalAmount += $prescription->total;
                }
            }

            $user = User::find($clinicId);
            $cashbackPercentage = $user?->cashback_percentage ?? 0;
            $cashbackAmount = ($totalAmount * $cashbackPercentage) / 100;

            $invoice = Invoice::create([
                'user_id' => $clinicId,
                'month' => now(),
                'type' => $type,
                'total_amount' => $totalAmount,
                'cashback_amount' => $cashbackAmount,
            ]);
            $prescriptionIds = $clinicPrescriptions->pluck('id');
            $invoice->prescriptions()->attach($prescriptionIds);
            $count++;
        }
        return $count;
    }

    public function processTransaction($startOfMonth, $endOfMonth)
    {
        $count = 0;
        $prescriptions = Prescription::whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->where('is_dispensed', 1)
            ->get()
            ->groupBy('clinic_id');
        foreach ($prescriptions as $clinicId => $clinicPrescriptions) {
            $doctorsPrescriptions = $clinicPrescriptions->groupBy('doctor_id');
            foreach ($doctorsPrescriptions as $doctorId => $doctorPrescriptions) {
                $totalAmount = $doctorPrescriptions->sum('total');
                $user = User::find($doctorId);
                $cashbackPercentage = $user->cashback_percentage ?? 0;
                $cashbackAmount = ($totalAmount * $cashbackPercentage) / 100;
                $transaction = Transaction::create([
                    'user_id' => $doctorId,
                    'clinic_id' => $clinicId,
                    'month' => now(),
                    'total_amount' => $totalAmount,
                    'cashback_amount' => $cashbackAmount,
                ]);
                $count++;
            }
        }
        return $count;
    }

    public function updateInvoiceStatus(Request $request)
    {
        // return $request->all();
        try {
            $invoice = Invoice::findOrFail($request->id);
            $message = '';
            $icon = 'success';

            if ($request->has('is_paid')) {
                $invoice->is_paid = $request->is_paid;
                $message .= 'Marked as Paid. ';
                $text = "is_paid";
            }

            if ($request->has('is_acknowledge')) {
                $invoice->is_acknowledge = $request->is_acknowledge;
                $message .= 'Marked as Acknowledged. ';
                $text = "is_acknowledge";
            }

            $invoice->save();
            return response([
                'status' => true,
                'icon' => $icon,
                'message' => trim($message),
                'text' => $text,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Something went wrong: ' . $e->getMessage(),
                'icon' => 'error'
            ]);
        }
    }

    public function getOverviewData()
    {
        $amount_one = Invoice::where('type', 'Type 1')->where('is_paid', 0)->sum('total_amount');
        $amount_two = Invoice::where('type', 'Type 1')->where('is_paid', 1)->sum('total_amount');
        $amount_three = Invoice::where('type', 'Type 1')->where('is_paid', 1)->sum('cashback_amount');
        $cashbackAmount = Invoice::where('type', 'Type 2')->where('is_paid', 1)->sum('cashback_amount');
        $amount_four = $amount_two - $amount_three;
        return response()->json([
            'available_funds' => $amount_one + $amount_four + $cashbackAmount,
            'future_payments' => Invoice::where('type', 'Type 2')->where('is_paid', 0)->sum('cashback_amount'),
            'cashback_amount' => Invoice::where('type', 'Type 1')->where('is_paid', 0)->sum('cashback_amount'),
            'total_income' => $amount_four + $cashbackAmount,
            'recieved_amount' => Invoice::where('type', 'Type 2')->where('is_paid', 1)->sum('cashback_amount'),
            'pending_amount' =>  Invoice::where('type', 'Type 2')->where('is_paid', 0)->sum('cashback_amount'),
        ]);
    }
}
