<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Prescription;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class InvoiceController extends Controller
{
    public function fetchInvoices(Request $request)
    {
        if (Auth::user()->hasRole('admin')) {
            $role = $request->input('role');
            $type = $request->input('type');
            $payment_status = $request->input('payment_status');
            $search = $request->input('search');
            $month = $request->input('month');

            $invoices = Invoice::with('user')
                ->whereHas('user', function ($query) use ($role, $search) {
                    $query->role($role);
                    $query->when(!is_null($search), function ($query) use ($search) {
                        return $query->where('name', 'like', '%' . $search . '%');
                    });
                })
                ->where('type', $type)
                ->when(!is_null($payment_status), function ($query) use ($payment_status) {
                    return $query->where('is_paid', $payment_status);
                })
                ->when(!is_null($month), function ($query) use ($month) {
                    return $query->whereMonth('month', Carbon::parse($month)->month)
                                ->whereYear('month', Carbon::parse($month)->year);
                })
                ->get();

            return response()->json($invoices);
        } else {
            $type = $request->input('type');
            $user_id = $request->input('id');
            $payment_status = $request->input('payment_status');
            $search = $request->input('search');
            $month = $request->input('month');

            $invoices = Invoice::with('user')
                ->where('user_id', $user_id)
                ->where('type', $type)
                ->when(!is_null($payment_status), function ($query) use ($payment_status) {
                    return $query->where('is_paid', $payment_status);
                })
                ->when(!is_null($search), function ($query) use ($search) {
                    return $query->whereHas('user', function ($q) use ($search) {
                        $q->where('name', 'like', '%' . $search . '%');
                    });
                })
                ->when(!is_null($month), function ($query) use ($month) {
                    return $query->whereMonth('month', Carbon::parse($month)->month)
                                ->whereYear('month', Carbon::parse($month)->year);
                })
                ->get();

            return response()->json($invoices);
        }
    }

    public function adminInvoice()
    {
        $clinic_admins = Invoice::whereHas('user', function ($query) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'clinic_admin');
            });
        })->where('type', 'Type 1')->get();

        $doctors = Invoice::whereHas('user', function ($query) {
            $query->whereHas('roles', function ($q) {
                $q->where('name', 'doctor');
            });
        })->where('type', 'Type 1')->get();
        return view('dashboard.admin.admin-invoices', compact('clinic_admins', 'doctors'));
    }

    public function generateInvoice()
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        // $shouldGenerate = Carbon::now()->isLastOfMonth() && Carbon::now()->diffInMinutes(Carbon::now()->endOfDay()) <= 5;

        // if ($shouldGenerate) {
            $typeOneCount = $this->processInvoiceType('Type 1', $startOfMonth, $endOfMonth);
            $typeTwoCount = $this->processInvoiceType('Type 2', $startOfMonth, $endOfMonth);
            $transactions = $this->processTransaction($startOfMonth, $endOfMonth);
            $totalCount = $typeOneCount + $typeTwoCount;
            return [
                'invoice' => "{$totalCount} invoice(s) generated successfully.",
                'transaction' => "{$transactions} transaction(s) generated successfully.",
            ];
        // }
        return "No new invoices or transactions were generated.";
    }


    private function processInvoiceType($type, $startOfMonth, $endOfMonth)
    {
        $count = 0;
        $prescriptions = Prescription::where('type', $type)
       ->whereBetween('created_at', [$startOfMonth, $endOfMonth])
       ->where(function($query) {
           $query->where('is_dispensed', 1)
               ->orWhereHas('payments', function($q) {
                   $q->where('is_dispensed', 1);
               });
       })
       ->get()
       ->groupBy('clinic_id');

        foreach ($prescriptions as $clinicId => $clinicPrescriptions) {
            $totalAmount = 0;

            foreach ($clinicPrescriptions as $prescription) {
                if ($prescription->prescription_type === 'repeat') {
                    // For repeat prescriptions, sum up the amounts from dispensed payments
                    $totalAmount += $prescription->payments()
                        ->where('is_dispensed', 1)
                        ->whereBetween('payment_date', [$startOfMonth, $endOfMonth])
                        ->sum('total');
                } else {
                    // For one-time prescriptions, use the prescription total
                    $totalAmount += $prescription->total;
                }
            }

            $user = User::find($clinicId);
            $cashbackPercentage = $user?->cashback_percentage ?? 0;
            $cashbackAmount = ($totalAmount * $cashbackPercentage) / 100;

            $invoice = Invoice::create([
                'user_id' => $clinicId,
                'month' => now(),
                'type' => $type,
                'total_amount' => $totalAmount,
                'cashback_amount' => $cashbackAmount,
            ]);
            $prescriptionIds = $clinicPrescriptions->pluck('id');
            $invoice->prescriptions()->attach($prescriptionIds);
            $count++;
        }
        return $count;
    }

    public function processTransaction($startOfMonth, $endOfMonth)
    {
        $count = 0;
        $prescriptions = Prescription::whereBetween('created_at', [$startOfMonth, $endOfMonth])
            ->where('is_dispensed', 1)
            ->get()
            ->groupBy('clinic_id');
        foreach ($prescriptions as $clinicId => $clinicPrescriptions) {
            $doctorsPrescriptions = $clinicPrescriptions->groupBy('doctor_id');
            foreach ($doctorsPrescriptions as $doctorId => $doctorPrescriptions) {
                $totalAmount = $doctorPrescriptions->sum('total');
                $user = User::find($doctorId);
                $cashbackPercentage = $user->cashback_percentage ?? 0;
                $cashbackAmount = ($totalAmount * $cashbackPercentage) / 100;
                $transaction = Transaction::create([
                    'user_id' => $doctorId,
                    'clinic_id' => $clinicId,
                    'month' => now(),
                    'total_amount' => $totalAmount,
                    'cashback_amount' => $cashbackAmount,
                ]);
                $count++;
            }
        }
        return $count;
    }

    public function updateInvoiceStatus(Request $request)
    {
        // return $request->all();
        try {
            $invoice = Invoice::findOrFail($request->id);
            $message = '';
            $icon = 'success';

            if ($request->has('is_paid')) {
                $invoice->is_paid = $request->is_paid;
                $message .= 'Marked as Paid. ';
                $text = "is_paid";
            }

            if ($request->has('is_acknowledge')) {
                $invoice->is_acknowledge = $request->is_acknowledge;
                $message .= 'Marked as Acknowledged. ';
                $text = "is_acknowledge";
            }

            $invoice->save();
            return response([
                'status' => true,
                'icon' => $icon,
                'message' => trim($message),
                'text' => $text,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Something went wrong: ' . $e->getMessage(),
                'icon' => 'error'
            ]);
        }
    }

    public function getOverviewData()
    {
        $amount_one = Invoice::where('type', 'Type 1')->where('is_paid', 0)->sum('total_amount');
        $amount_two = Invoice::where('type', 'Type 1')->where('is_paid', 1)->sum('total_amount');
        $amount_three = Invoice::where('type', 'Type 1')->where('is_paid', 1)->sum('cashback_amount');
        $cashbackAmount = Invoice::where('type', 'Type 2')->where('is_paid', 1)->sum('cashback_amount');
        $amount_four = $amount_two - $amount_three;
        return response()->json([
            'available_funds' => $amount_one + $amount_four + $cashbackAmount,
            'future_payments' => Invoice::where('type', 'Type 2')->where('is_paid', 0)->sum('cashback_amount'),
            'cashback_amount' => Invoice::where('type', 'Type 1')->where('is_paid', 0)->sum('cashback_amount'),
            'total_income' => $amount_four + $cashbackAmount,
            'recieved_amount' => Invoice::where('type', 'Type 2')->where('is_paid', 1)->sum('cashback_amount'),
            'pending_amount' =>  Invoice::where('type', 'Type 2')->where('is_paid', 0)->sum('cashback_amount'),
        ]);
    }
}
