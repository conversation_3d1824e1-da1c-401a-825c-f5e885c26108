<?php

namespace App\Http\Controllers;

use App\Models\Prescription;
use App\Models\PrescriptionPayment;
use App\Services\DpdApiService;
use Exception;
use Illuminate\Http\Request;

class ShipmentController extends Controller
{
    protected $dpdApiService;

    public function __construct(DpdApiService $dpdApiService)
    {
        $this->dpdApiService = $dpdApiService;
    }

    public function createShipment()
    {
        // Provided static shipment data
        $shipmentData = [
            "collectionDate" => "2025-05-09T09:00:00",
            "consignment" => [
                [
                    "collectionDetails" => [
                        "contactDetails" => [
                            "contactName" => "Priyesh Bhogaita",
                            "telephone" => "07800963569"
                        ],
                        "address" => [
                            "street" => "16 CENTRAL STREET",
                            "town" => "LEICESER",
                            "postcode" => "LE8 5QJ",
                            "countryCode" => "GB"
                        ]
                    ],
                    "deliveryDetails" => [
                        "contactDetails" => [
                            "contactName" => "FAddy",
                            "telephone" => "0190 900 9000"
                        ],
                        "address" => [
                            "organisation" => "Dr strage",
                            "countryCode" => "GB",
                            "postcode" => "B66 1BY",
                            "street" => "Roebuck Lane",
                            "locality" => "Smethwick",
                            "town" => "Birmingham",
                            "county" => "West Midlands"
                        ],
                        "notificationDetails" => [
                            "email" => "<EMAIL>",
                            "mobile" => "07921000001"
                        ]
                    ],
                    "networkCode" => "1^12",
                    "numberOfParcels" => 1,
                    "totalWeight" => 5,
                    "shippingRef1" => "My Ref 1",
                    "shippingRef2" => "My Ref 2",
                    "shippingRef3" => "My Ref 3",
                    "customsValue" => null,
                    "deliveryInstructions" => "Please deliver with neighbour",
                    "parcelDescription" => "",
                    "liabilityValue" => null,
                    "liability" => false
                ]
            ]
        ];

        // try {
        // Get the geoSession
        $geoSession = $this->dpdApiService->login();

        // Create shipment with static data and geoSession
        $shipmentResponse = $this->dpdApiService->createShipment($shipmentData, $geoSession);

        //response sample
        // {
        //     "error": null,
        //     "data": {
        //       "shipmentId": 1547176042120945,
        //       "consolidated": false,
        //       "consignmentDetail": [
        //         {
        //           "consignmentNumber": "5471760421",
        //           "parcelNumbers": [
        //             "15505471760421"
        //           ]
        //         }
        //       ]
        //     }
        //   }
        //response sample
        if ($shipmentResponse['error'] === null) {
            // Extract relevant data
            $shipmentId = $shipmentResponse['data']['shipmentId'];
            $consignmentNumber = $shipmentResponse['data']['consignmentDetail'][0]['consignmentNumber'];
            $parcelNumbers = $shipmentResponse['data']['consignmentDetail'][0]['parcelNumbers'];

            // Assuming you have a Prescription model and you want to store the data in the prescriptions table
            // $prescription = Prescription::create([
            //     'shipment_id' => $shipmentId,
            //     'consignment_number' => $consignmentNumber,
            //     'parcel_numbers' => json_encode($parcelNumbers),  // Store parcel numbers as JSON
            // ]);

            return response()->json(['message' => 'Shipment created and data stored successfully', 'prescription' => 'pp']);
        }

        return response()->json(['error' => 'Shipment creation failed'], 400);
        // } catch (Exception $e) {
        //     return response()->json(['error' => $e->getMessage()], 500);
        // }
    }

    //response sample
    // {
    //     "error": null,
    //     "data": {
    //       "shipmentId": 1547176042120945,
    //       "consolidated": false,
    //       "consignmentDetail": [
    //         {
    //           "consignmentNumber": "5471760421",
    //           "parcelNumbers": [
    //             "15505471760421"
    //           ]
    //         }
    //       ]
    //     }
    //   }
    //response sample

    public function createAndPrintShipment($orderId = null, $scheduleId = null)
    {
        // try {
        // Fetch the prescription related to the orderId
        $prescription = Prescription::where('order_id', $orderId)->first();

        if (!$prescription) {
            return response()->json(['error' => 'Prescription not found for the given order ID'], 404);
        }

        // Fetch the user related to the prescription
        $user = $prescription->patient;

        if (!$user || !$user->profile) {
            return response()->json(['error' => 'User profile not found'], 404);
        }

        // Map user profile data to the DPD shipment address format
        $userProfile = $user->profile;
        $shipmentData = [
            "collectionDate" => now()->addDay()->format('Y-m-d\TH:i:s'),  // Set to next day
            "consignment" => [
                [
                    "collectionDetails" => [
                        "contactDetails" => [
                            "contactName" => "Priyesh Bhogaita",  // Adjust as needed
                            "telephone" => "07800963569"
                        ],
                        "address" => [
                            "street" => "16 CENTRAL STREET",  // Adjust as needed
                            "town" => "LEICESER",  // Adjust as needed
                            "postcode" => "LE8 5QJ",  // Adjust as needed
                            "countryCode" => "GB"
                        ]
                    ],
                    "deliveryDetails" => [
                        "contactDetails" => [
                            "contactName" => $user->name,  // Use user name or prescription details
                            "telephone" => $userProfile->phone  // Or use profile phone number
                        ],
                        "address" => [
                            "organisation" => $userProfile->organisation,  // Use profile organisation if available
                            "countryCode" => "GB",  // Assuming UK (GB)
                            "postcode" => $userProfile->delivery_postal,  // User profile postal code
                            "street" => $userProfile->delivery_address,  // User profile street
                            "locality" => $userProfile->delivery_city,  // User profile city
                            "town" => $userProfile->delivery_city,  // Town or city (use as town)
                            "county" => $userProfile->delivery_country  // County (state) from user profile
                        ],
                        "notificationDetails" => [
                            "email" => $user->email,
                            "mobile" => $userProfile->phone
                        ]
                    ],
                    "networkCode" => "1^12",
                    "numberOfParcels" => 1,
                    "totalWeight" => 5,
                    "shippingRef1" => "My Ref 1",
                    "shippingRef2" => "My Ref 2",
                    "shippingRef3" => "My Ref 3",
                    "customsValue" => null,
                    "deliveryInstructions" => "Please deliver with neighbour",
                    "parcelDescription" => "",
                    "liabilityValue" => null,
                    "liability" => false
                ]
            ]
        ];

        // Get the geoSession
        $geoSession = $this->dpdApiService->login();

        // Create shipment with dynamic data and geoSession
        $shipmentResponse = $this->dpdApiService->createShipment($shipmentData, $geoSession);
        // Check if there are errors in the response
        if (isset($shipmentResponse['error']) && !empty($shipmentResponse['error'])) {
            $errorMessages = [];
            foreach ($shipmentResponse['error'] as $error) {
                if (isset($error['errorMessage']) && isset($error['obj'])) {
                    $errorMessages[] = $error['obj'] . ': ' . $error['errorMessage'];
                }
            }
            return response()->json([
                'error' => 'Shipment creation failed',
                'details' => $errorMessages,
                'full_response' => $shipmentResponse
            ], 400);
        }
        if ($shipmentResponse['error'] === null) {
            // Extract relevant data from the response
            $shipmentId = $shipmentResponse['data']['shipmentId'];
            $consignmentNumber = $shipmentResponse['data']['consignmentDetail'][0]['consignmentNumber'];
            $parcelNumbers = $shipmentResponse['data']['consignmentDetail'][0]['parcelNumbers'];
            // Store shipment details in the prescription
            if ($scheduleId != null && $prescription->prescription_type == 'repeat') {
                $schedulePayment = PrescriptionPayment::where('order_id', $scheduleId)->first();
                $schedulePayment->shipment_id = $shipmentId;
                $schedulePayment->consignment_number = $consignmentNumber;
                $schedulePayment->parcel_numbers = json_encode($parcelNumbers);
            } else {
                $prescription->shipment_id = $shipmentId;
                $prescription->consignment_number = $consignmentNumber;
                $prescription->parcel_numbers = json_encode($parcelNumbers);
            }

            // Now, print the label using the same geoSession
            $labelResponse = $this->dpdApiService->printLabel($shipmentId, $geoSession);

            // If the label is successfully retrieved
            if ($labelResponse->successful()) {
                // Update prescription status to "is_dispensed" to 1 after successful shipment
                if ($scheduleId != null && $prescription->prescription_type == 'repeat') {
                    $schedulePayment->is_dispensed = 1;
                    $schedulePayment->save();
                } else {
                    $prescription->is_dispensed = 1;
                    $prescription->save();
                }
                // Return the label as a PDF
                return response($labelResponse->body(), 200)
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'inline; filename="shipment-label.pdf"');
            } else {
                return response()->json(['error' => 'Label could not be retrieved'], 400);
            }



            return response()->json(['message' => 'Shipment created, label printed and data stored successfully', 'prescription' => $prescription]);
        }

        return response()->json(['error' => 'Shipment creation failed'], 400);
        // } catch (Exception $e) {
        //     return response()->json(['error' => $e->getMessage()], 500);
        // }
    }


    public function printLabelForShipment($shipmentId)
    {
        try {
            // Retrieve geoSession for printing label
            $geoSession = $this->dpdApiService->login();

            // Call the service class method to get the label
            $labelResponse = $this->dpdApiService->printLabel($shipmentId, $geoSession);

            // If the label is returned successfully, you can return it as a PDF or other format
            if ($labelResponse->successful()) {
                return response($labelResponse->body(), 200)
                    ->header('Content-Type', 'application/pdf')
                    ->header('Content-Disposition', 'inline; filename="shipment-label.pdf"');
            }

            return response()->json(['error' => 'Label could not be retrieved'], 400);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }


    public function trackShipment($consignmentNumber)
    {
        // try {
        // Retrieve the consignmentNumber from the database
        // $prescription = Prescription::findOrFail($prescriptionId);
        // $consignmentNumber = $prescription->consignment_number;

        // Call the tracking API from the service class
        $trackingResponse = $this->dpdApiService->trackShipment($consignmentNumber);

        // Return the response (either parcelStatus or error message)
        if (isset($trackingResponse['parcelStatus'])) {
            return redirect()->back()->with([
                'type' => 'success',
                'title' => 'Tracking Successful',
                'message' => 'Parcel Status: ' . $trackingResponse['parcelStatus'] . '.',
            ]);
        } else {
            return redirect()->back()->with([
                'type' => 'error',
                'title' => 'Tracking',
                'message' => 'Something went wrong, please try again later.',
            ]);
        }
        // return redirect()->back()->with(['error' => $trackingResponse['error']], 400);
        // return response()->json(['error' => $trackingResponse['error']], 400);
        // } catch (Exception $e) {
        //     return response()->json(['error' => $e->getMessage()], 500);
        // }
    }
    // public function trackShipment($consignmentNumber)
    // {
    //     try {
    //         $trackingResponse = $this->dpdApiService->trackShipment($consignmentNumber);

    //         if (isset($trackingResponse['parcelStatus'])) {
    //             $message = "Status: {$trackingResponse['parcelStatus']}, Parcel Number: {$trackingResponse['parcelNumber']}, Consignment Number: {$trackingResponse['consignmentNumber']}";

    //             return redirect()->back()->with([
    //                 'type' => 'success',
    //                 'message' => $message,
    //             ]);
    //         }

    //         return redirect()->back()->with([
    //             'type' => 'error',
    //             'message' => $trackingResponse['error'] ?? 'Tracking error occurred.',
    //         ]);
    //     } catch (Exception $e) {
    //         return redirect()->back()->with([
    //             'type' => 'error',
    //             'message' => $e->getMessage(),
    //         ]);
    //     }
    // }
}
