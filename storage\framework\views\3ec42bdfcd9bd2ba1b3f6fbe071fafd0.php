<?php $__env->startSection('content'); ?>
    <section class="hero-section">
        <div class="container">
            <div class="row ">
                <div class="col-xl-6 col-12">
                    <div class="banner-text">
                    <?php echo $home_cms->title ?? ''; ?>

                    </div>
                    <div class="started-btn justify-content-xl-start justify-content-center d-flex">
                        <a href="<?php echo e($home_cms->btn_link ?? 'login'); ?>"
                            class="button_started py-3"><?php echo e(auth()->guest() ? ($home_cms->btn_title ?? '') : 'Dashboard'); ?></a>
                    </div>
                    <div class="hero-logos-parent d-flex column-gap-12 pt-10 justify-content-xl-start justify-content-center flex-wrap">
                        <div class="hero-logo">
                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/npa-img.png" alt="npa-img">
                        </div>
                        <div class="hero-logo ico-hero-logo">
                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/ico-img.png" alt="ico-img">
                        </div>
                        <div class="hero-logo">
                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/network-img.png" alt="network-img">
                        </div>
                        <div class="hero-logo">
                            <img src="<?php echo e(asset('website')); ?>/assets/media/images/council-img.png" alt="council-img">
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 position-relative ">
                    <div class="hero-banner-image position-absolute">
                        <img src="<?php echo e(asset('website/' . $home_cms->banner_image)); ?>" alt="dashboard-img">

                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="patients-data-sec py-20">
        <div class="container">
            <div class="row row-gap-10">
                <div class="col-xl-8 offset-xl-2 col-12 text-center position-relative">
                    <?php echo $home_cms->section_one_title ?? ''; ?>

                </div>
                <?php $__currentLoopData = $section_one; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-3  col-sm-6">
                        <h5 class="roboto Deep-Ocean-Blue patients-data-card-head ps-5 "><?php echo $section->title ?? ''; ?></h5>
                        <p class="fs-14 normal text-black ps-5 pt-5"><?php echo $section->description ?? ''; ?></p>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
        </div>
    </section>
    <section class="streamline-patient-sec py-20">
        <div class="container">
            <div class="row">
                <div class="col-xl-6 col-12 offset-xl-3 text-center">
                    <?php echo $home_cms->section_two_content ?? ''; ?>

                    
                </div>
                <div class="col-lg-12">
                    <div class="row row-gap-5 justify-content-center">
                        <?php $__currentLoopData = $section_two; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-lg-3  col-sm-6">
                                <div class="patient-care-logo-parent">
                                    <img src="<?php echo e(asset('website/' . $section->image)); ?>" alt="start-logo">
                                </div>
                                <p class="fs-14 normal white-color ps-xl-5 pt-xl-4 ps-0 pt-0 streamline-patient-text">
                                    <?php echo $section->description ?? ''; ?></p>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section>
        <div class="container my-20">
            <div class=" row mb-15">
                <div class="col-md-6">
                    <p class="Blue-Violet Roboto"><?php echo e($home_cms->section_three_title ?? ''); ?></p>
                    
                    <?php echo $home_cms->section_three_subtitle ?? ''; ?>

                </div>

                <div class="col-md-6">
                    
                    <?php echo $home_cms->section_three_description ?? ''; ?>

                </div>
            </div>

            <div class="row row-gap-5">
                <?php $__currentLoopData = $section_tree; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-lg-4 col-md-6 col-12">
                        <div class="card home-cards-electronic border-0">
                            <div class="card-header border-bottom-0">
                                <div class="purple-box">
                                    <img src="<?php echo e(asset('website/' . $section->image)); ?>" alt="start-logo">
                                </div>
                            </div>
                            <div class="card-body">

                                <h5 class="mb-4"><?php echo e($section->title ?? ''); ?></h5>

                                <?php echo $section->description ?? ''; ?>


                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
    <section class="custom-background doctor-choose-us">
        <div class="container">
            <div class="row">
                <div class="col-lg-5 m-auto">
                    
                    <?php echo $home_cms->section_four_title ?? ''; ?>

                </div>
                <div class="row row-gap-5">
                    <?php $__currentLoopData = $section_four; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6">
                            <div class="card card-doctor  position-relative card-bg1">
                                <div class="text-end p-7 image-border">
                                    <img src="<?php echo e(asset('website/' . $section->image)); ?>" alt="start-logo"
                                        class="img-fluid customer-service">
                                </div>

                                <div class="p-9">
                                    <h5 class="bold mb-5"><?php echo e($section->title ?? ''); ?></h5>

                                    <p><?php echo $section->description ?? ''; ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>
    <section class="prescribing-workflow">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class=" content-heading d-flex  flex-column align-items-center">
                        
                       <h2 class="text-center pb-5 text-white w-xl-50 w-100"> <?php echo $home_cms->section_five_title ?? ''; ?></h2>
                        <h6 class="w-md-75 w-100 ice-green text-center"> <?php echo $home_cms->section_five_description ?? ''; ?></h6>

                        <a href="<?php echo e($home_cms->section_five_btn_link ?? 'login'); ?>"
                            class="button_started py-3 mt-5"><?php echo e($home_cms->section_five_btn_title ?? ''); ?></a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="custom-background doctor-choose-us">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <?php echo $home_cms->section_six_title ?? ''; ?>

                </div>
                <div class="row row-gap-5">
                    <?php $__currentLoopData = $testimonials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $testimonial): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4">
                            <div class="card card-doctor h-100">
                                <div class="card-header border-0 pt-7 px-5">
                                    <div class="ratings">
                                        <?php for($stars = 0; $stars < $testimonial->rating; $stars++): ?>
                                            <?php echo $__env->make('svg.ratings', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                                <div class="card-body py-5 px-5 ">
                                    <?php echo $testimonial->description ?? ''; ?>

                                </div>
                                <div class="card-footer border-0 d-flex gap-5 pb-5 px-5">
                                    <div class="image-user">
                                        <img src="<?php echo e(asset('website/' . $testimonial->image)); ?>" alt="user-img"
                                            class="rounded-pill">
                                    </div>
                                    <div class="user-details">
                                        <h5 class="m-0"><?php echo e($testimonial->name ?? ''); ?></h5>
                                        <p class="Majesticpurple"><?php echo e($testimonial->designation ?? ''); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </section>
    <section class="benefit-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h2 class="green-text text-center">
                        <?php echo $home_cms->section_seven_title ?? 'Benefits of Using Direct RX'; ?>

                    </h2>
                </div>
            </div>
            <div class="row row-gap-5">
                <?php $__currentLoopData = $section_seven; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="col-xl-3 col-sm-6 col-12">
                        <div class="card benefit-card">
                            <div class="card-header border-0 d-flex align-items-center justify-content-center flex-column">
                                <div class="patient-care-logo-parent even mb-5">
                                    <img src="<?php echo e(asset('website/' . $feature->image)); ?>"
                                         class="top-rated-image h-75 w-75"
                                         alt="<?php echo e($feature->title); ?>">
                                </div>
                                <h5 class="text-center ms-3"><?php echo e($feature->title); ?></h5>
                            </div>
                            <div class="card-body">
                                <p class="streamline-patient-text">
                                    <?php echo $feature->description; ?>

                                </p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git\rx-direct\resources\views/website/index.blade.php ENDPATH**/ ?>