@extends('theme.layout.master')
@section('content')
{{-- @section('breadcrumb')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6 ps-4">
        <div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack m-0">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                    Contact Queries</h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ url('home') }}" class="text-muted text-hover-primary">Home</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-400 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">contacts</li>
                </ul>
            </div>
        </div>
    </div>
@endsection --}}
<div id="kt_app_content" class="app-content flex-column-fluid">
    <div id="kt_app_content_container" class="app-container container-xxl m-0">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="custom-dropdown card-header border-0 pt-6 justify-content-end gap-5 align-items-end">
                        <div class="card-title">
                            <!-- {{-- <div class="d-flex align-items-center position-relative my-1">
                        <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                            <span class="path1"></span>
                            <span class="path2"></span>
                        </i>
                        <input type="text" data-kt-customer-table-filter="search"
                            class="form-control form-control-solid w-250px ps-13" placeholder="Search Customers" />
                    </div> --}} -->
                        </div>
                        <div class="card-toolbar">
                            <div class="d-flex justify-content-end" data-kt-customer-table-toolbar="base">
                                @can('contacts-create')
                                    <a class="btn btn-primary" href="{{ route('contacts.create') }}">Add</a>
                                @endcan
                            </div>
                        </div>

                          <div class="custom-select">
                                            <select data-control="select2" class="statusFilter" data-hide-search="true"
                                                data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Patients</option>
                                                <option value="0">Pending</option>
                                                <option value="1">Resolved</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
 


                        {{-- <div class="custom-select">
                            <select id="statusFilter" class="status_filter">
                                <option value="">All Patients</option>
                                <option value="0">Pending</option>
                                <option value="1">Resolved</option>
                            </select>
                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg" alt="Filter Icon">
                        </div> --}}
                        <div class="result-container1">
                            <div class="range-options additional-options">
                                <div
                                    class="range-container d-flex justify-content-between align-items-end gap-5 date-range-group">
                                    <div class="start-range date-field">
                                        <label for="adminStartRange">Start Range:</label>
                                         <span class="error-message text-danger"
                                            style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                            select Start Range
                                            first.</span>
                                        <input type="date" class="start-range-input" name="adminStartRange"
                                            max="{{ \Carbon\Carbon::today()->toDateString() }}" value="">

                                    </div>
                                    <div class="end-range date-field">
                                        <label for="adminEndRange">End Range:</label>
                                        <input type="date" class="end-range-input" name="adminEndRange"
                                            max="{{ \Carbon\Carbon::today()->toDateString() }}"
                                            value="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <a class="button-gradient white-color roboto fs-14 fw-400 py-5"
                            href="{{ route('download.contacts') }}">Download</a>
                    </div>
                    <div class="card-body p-0">
                          <div class="table-responsive">
                        <table class="table align-middle table-row-dashed fs-6 gy-5 sortTable" id="kt_customers_table">
                            <thead>
                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">

                                    <!-- {{-- <th class="w-10px pe-2">
                                <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                    <input class="form-check-input" type="checkbox" data-kt-check="true"
                                        data-kt-check-target="#kt_customers_table .form-check-input" value="1" />
                                </div>
                            </th> --}} -->
                                    <th class="min-w-250px">first name</th>
                                    <th class="min-w-250px">last name</th>
                                    <th class="min-w-250px">email</th>
                                    <th class="min-w-250px">phone number</th>
                                    <th class="min-w-300px">message</th>
                                    <th class="min-w-250px">Status</th>
                                    <th class="text-start first-and-last min-w-50px">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="fw-semibold text-gray-600" id="activeTab">
                                @foreach ($contacts as $contact)
                                    <!-- {{-- <td> --}}
                                        {{-- <td>
                                                <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox" value="{{ $contact->id }}" />
                                                </div>
                                            </td> --}} -->
                                    <td>{{ $contact->first_name }}</td>
                                    <td>{{ $contact->last_name }}</td>
                                    <td>{{ $contact->email }}</td>
                                    <td>{{ $contact->phone_number }}</td>
                                    <td>{{ strlen($contact->message) > 50 ? substr($contact->message, 0, 15) . '...' : $contact->message }}
                                    </td>
                                    <td><span
                                            class="badge badge-{{ $contact->status == 0 ? 'inactive' : 'active' }} roboto fs-14 fw-400">{{ $contact->status == 0 ? 'Pending' : 'Resolved' }}</span>
                                    </td>
                                    <td class="text-end">
                                        <a href="#"
                                            class="btn btn-sm btn-light btn-flex btn-center btn-active-light-primary"
                                            data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">Actions
                                            <i class="ki-duotone ki-down fs-5 ms-1"></i>
                                        </a>
                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                            data-kt-menu="true">

                                            <div class="menu-item px-3">
                                                <a href="{{ route('contacts.show', [$contact->id]) }}"
                                                    class="menu-link px-3">View</a>
                                            </div>

                                            @can('contacts-edit')
                                                <div class="menu-item px-3">
                                                    <a href="{{ route('contacts.edit', [$contact->id]) }}"
                                                        class="menu-link px-3">Edit</a>
                                                </div>
                                            @endcan
                                            @can('contacts-delete')
                                                <div class="menu-item px-3">
                                                    {!! Form::open(['method' => 'DELETE', 'route' => ['contacts.destroy', $contact->id], 'class' => 'delete-form']) !!}
                                                    <a class="menu-link px-3" href="javascript:void(0)"
                                                        onclick="showDeleteConfirmation(this)">Delete</a>
                                                    {!! Form::close() !!}
                                            @endcan
                                            </div>
                                        </div>
                                    </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                        <div class="d-flex justify-content-between align-items-center mt-5">
                            <p class="ps-3 m-0">
                                Showing {{ $contacts->firstItem() }} to {{ $contacts->lastItem() }} of
                                {{ $contacts->total() }}
                                entries
                            </p>
                            <div class="pagination">
                                {{ $contacts->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
@stop
@push('js')
    <script>
        $(document).ready(function () {
            // $('.end-range-input').prop('disabled', true);

            // $('.start-range-input').on('change', function () {
            //     $('.end-range-input').prop('disabled', false);

            //     const startDate = $('.start-range-input').val();
            //     let endDate = $('.end-range-input').val();

            //     if (!endDate) {
            //         endDate = new Date().toISOString().split('T')[0]; // Default to today
            //     }

            //     fetchData(1, startDate, endDate);
            // });


            $('#statusFilter').on('change', function () {
                const status = $(this).val();
                fetchData(1, null, null, status);
            });

            function fetchData(page = 1, startDate = null, endDate = null, status = null) {
                $.ajax({
                    url: "{{ route('contact.search.filter') }}",
                    type: "POST",
                    data: {
                        page: page,
                        start_date: startDate,
                        end_date: endDate,
                        status: status,
                        _token: "{{ csrf_token() }}"
                    },
                    success: function (data) {
                        $('#activeTab').empty().html(
                            data); // You likely want to inject new content here

                    },
                    error: function (xhr) {
                        console.error("Error fetching data:", xhr.responseText);
                    },
                    complete: function () {
                        KTMenu.createInstances();
                    }
                });
            }
        });
    </script>
@endpush