<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class ProfileController extends Controller
{
    public function profileSetting()
    {
        return view('dashboard.profile_setting');
    }
    public function prescriberProfile($id)
    {
        $user = User::find($id);
        return view('dashboard.prescriber_profile' ,compact('user'));
    }
    public function profileSettingUpdate(Request $req){
        $req->validate([
            'name'=> 'required',
        ]);
        auth()->user()->update([
            'name' => $req->name,
        ]);
        return redirect()->back()->with(['type'=>'success', 'message'=> 'Profile Update Successfully!']);
    }
    public function passwordUpdate(Request $req){
        $req->validate([
            'password'=> 'required|confirmed',
        ]);
        auth()->user()->update([
            'password' => Hash::make($req->password),
        ]);
        return redirect()->back()->with(['type'=>'success', 'message'=> 'Password Update Successfully!']);
    }
    public function avatarUpdate(Request $req){
        $req->validate([
            'image'=> 'required',
        ]);
        $image = $this->storeImage('profile', $req->image);
        auth()->user()->profile->update([
            'pic' => $image,
        ]);
        return redirect()->back()->with(['type'=>'success', 'message'=> 'Avatar Update Successfully!']);
    }
}
