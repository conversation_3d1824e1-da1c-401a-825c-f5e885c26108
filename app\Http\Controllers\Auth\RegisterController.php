<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\VerificationCodeMail;
use App\Models\EmailVerification;
use App\Providers\RouteServiceProvider;
use App\Models\User;
use App\Rules\NotDisposableEmail;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = RouteServiceProvider::HOME;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'role' => ['required', 'in:doctor,clinic_admin'],
            'firstname' => ['required', 'string', 'max:255'],
            'surname' => ['required', 'string', 'max:255'],
            // 'email' => ['required', 'string', 'email', 'max:255', 'unique:users', new NotDisposableEmail],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'dob' => ['required'],
            'professional_type' => ['required'],
            'reg_no' => ['required'],
            'mobile_number' => ['required', 'regex:/^(\+\d{1,3}[- ]?)?\d{10,15}$/'],
            'street_name' => ['required'],
            'prescriber_code' => ['required'],
            'clinic_address' => ['required'],
            'type' => ['required'],
        ]);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @param  array  $data
     * @return \App\Models\User
     */
    protected function create(array $data)
    {
        // Create the user
        $user = User::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => Hash::make($data['password']),
            'status' => 0,
        ]);
        $user->profile()->create([
            'professional_type' => $data['professional_type'],
            'reg_no' => $data['reg_no'],
            'mobile_number' => $data['mobile_number'],
            'clinic_address' => $data['clinic_address'],
            'id_document' =>  $data['id_document'],
            'idemnity_certificate' =>  $data['idemnity_certificate'],
        ]);

        // Assign the user role
        $user->assignRole($data['role']);
        $admin = User::whereHas('roles', function($query) {
            $query->where('name', 'admin');
        })->first();
        $this->sendNotification($user->id, 'Welcome', $user->name .' registered in a platfrom as a '. ucwords(str_replace('_', ' ', $data['role'])));
        $this->sendNotification($admin->id, 'User Created', $user->name  .' registered in a platfrom as a '.ucwords(str_replace('_', ' ', $data['role'])));
        return $user;
    }

    public function storeSession(Request $request)
    {
        $data = $request->except('_token');

        // Combine firstname and surname for the name field
        $data['name'] = trim($data['firstname'] . ' ' . $data['surname']);

        $verification_code = rand(1000, 9999);
        // Handle file uploads
        if ($request->hasFile('idemnity_certificate')) {
            $idemnity_certificate_path = $this->storeImage('documents', $request->idemnity_certificate, 'website');
            $data['idemnity_certificate'] = $idemnity_certificate_path;
        }

        if ($request->hasFile('id_document')) {
            $id_document_path = $this->storeImage('documents', $request->id_document, 'website');
            $data['id_document'] = $id_document_path;
        }

        session(['registration_data' => $data, 'verification_code' => $verification_code]);
        $email_already_exists = EmailVerification::where('email', $request->email)->first();
        if ($email_already_exists) {
            $createdAt = $email_already_exists->created_at;
            // Check if 30 seconds have passed since the email was last created
            if (now()->diffInSeconds($createdAt) >= 30) {
                $email_already_exists->code = $verification_code;
                $email_already_exists->save();
                Mail::to($request->email)->send(new VerificationCodeMail($verification_code, $email_already_exists->email));
            }
        } else {
            $email_verification = new EmailVerification();
            $email_verification->email = $request->email;
            $email_verification->code = $verification_code;
            $email_verification->save();

            // Send email
            Mail::to($request->email)->send(new VerificationCodeMail($verification_code, $email_verification->email));
        }


        return response()->json(['success' => true, 'message' => 'Data stored in session']);
    }
    public function verifyOtp(Request $request)
    {
        $value = $request->otp;
        $email_verification = EmailVerification::latest()->first();
        if ($email_verification && $email_verification->code == $value) {
            $registrationData = session('registration_data');
            if ($registrationData) {
                $user = $this->create($registrationData);
                session()->forget('registration_data');
                $email_verification->delete();
                return response()->json([
                    'success' => true,
                    'message' => 'OTP Verified Successfully and User Registered',
                    'user' => $user,
                ]);
            }
            return response()->json([
                'success' => false,
                'message' => 'Session data not found',
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'The OTP you entered is incorrect. Please try again.',
            ]);
        }
    }

    public function checkEmail(Request $request)
    {
        $email = $request->email;
        $exists = User::where('email', $email)->exists();

        return response()->json([
            'exists' => $exists
        ]);
    }
}
