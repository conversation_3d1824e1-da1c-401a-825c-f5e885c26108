<div id="kt_app_sidebar" class="app-sidebar flex-column sidebar_dashboard" data-kt-drawer="true"
    data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true"
    data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
        <a href="{{ url('/') }}">
            <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->logo ?? '' }}"
                class="h-50px app-sidebar-logo-default" />
            <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->favicon ?? '' }}"
                class="h-30px app-sidebar-logo-minimize" />
        </a>
        <!--end::Logo image-->
        <!--begin::Sidebar toggle-->
        <!--begin::Minimized sidebar setup:
     if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
     1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
     2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
     3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
     4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
     }
     -->
        <!-- {{-- <div id="kt_app_sidebar_toggle" --}}
        {{-- class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" --}}
        {{-- data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" --}}
        {{-- data-kt-toggle-name="app-sidebar-minimize"> --}}
        {{-- <i class="ki-duotone ki-black-left-line fs-3 rotate-180"> --}}
        {{-- <span class="path1"></span> --}}
        {{-- <span class="path2"></span> --}}
        {{-- </i> --}}
        {{-- </div> --}} -->
    </div>
    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
            <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6  @if (Auth::user()->hasAnyRole(['admin', 'clinic_admin', 'doctor', 'staff', 'patients'])) nav-items @endif "
                    id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                    @if (Auth::user()->hasRole('developer'))
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                            </div>
                        </div>
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="title-menu">User Management</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    @can('crud-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">CRUD</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('crud_generator') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="title-menu">CRUD Generator</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    @can('user-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">Users</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('users') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="title-menu">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    @can('role-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Roles</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('roles') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="title-menu">Permissions</span>
                                    </a>
                                </div>
                                @can('settings-list')
                                    <div class="menu-item">
                                        <a class="menu-link" href="{{ url('settings') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">Settings</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <hr>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Menu</span>
                            </div>
                        </div>
                        @foreach ($crud as $item)
                            @can($item->url . '-list')
                                <!-- {{-- @can(\Illuminate\Support\Str::slug($item->name) . '-list') --}} -->
                                <div class="menu-item">
                                    <a class="menu-link {{ request()->is($item->url) ? 'active' : '' }}"
                                        href="{{ url($item->url ?? 'home') }}">
                                        <span class="menu-icon">
                                            <i class="ki-duotone ki-abstract-28 fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span
                                            class="title-menu">{{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name) }}</span>
                                    </a>
                                </div>
                            @endcan
                        @endforeach
                    @endif
                    @if (Auth::user()->hasRole('admin'))
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('home') ? 'active' : '' }}" href="{{ url('home') }}">
                                <span class="menu-icon">@include('svg.dashboard')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('admin.users') || request()->routeIs('users_details') ? 'active' : '' }}"
                                href="{{ route('admin.users') }}">
                                <span class="menu-icon">@include('svg.patients')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Users</span>
                            </a>
                        </div>
                        @can('inventories-list')
                            <div class="menu-item">
                                <a class="menu-link py-4 {{ request()->routeIs('inventories.index') ? 'active' : '' }}"
                                    href="{{ route('inventories.index') }}">
                                    <span class="menu-icon">@include('svg.medicine')</span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Medicines</span>
                                </a>
                            </div>
                        @endcan

                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('user.requests') ? 'active' : '' }}"
                                href="{{ route('user.requests') }}">
                                <span class="menu-icon">@include('svg.request')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Registration Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <!-- 'prescription-details' -->
                            <a class="menu-link py-4 {{ request()->routeIs('orders') ? 'active' : '' }}"
                                href="{{ route('orders') }}">
                                <span class="menu-icon">@include('svg.orders')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Prescription Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('subscriptions') ? 'active' : '' }}"
                                href="{{ url('/subscriptions') }}">
                                <span class="menu-icon">@include('svg.transactions')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Transactions</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('invoice', 'admin-invoices-details*') ? 'active' : '' }}"
                                href="{{ url('invoice') }}">

                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Invoice</span>
                            </a>
                        </div>





                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('analytics') ? 'active' : '' }}"
                                href="{{ url('/analytics') }}">
                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Reports</span>
                            </a>
                        </div>

                        <!-- {{-- <div class="menu-item">
                                        <a class="menu-link py-4 {{ request()->is('analytics') ? 'active' : '' }}"
                                            href="{{ url('/analytics') }}">
                                            <span class="menu-icon">@include('svg.analytics')</span>
                                            <span class="title-menu sidebar-green fs-16 fw-500">Analytics</span>
                                        </a>
                                    </div> --}} -->

                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('notification') ? 'active' : '' }}"
                                href="{{ url('notification') }}">
                                <span class="menu-icon">@include('svg.notifications')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('cms') ? 'active' : '' }}" href="{{ url('cms') }}">
                                <span class="menu-icon">@include('svg.cms')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">CMS</span>
                            </a>
                        </div>
                        <!-- hadia -->


                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('contacts.index', 'contacts.show') ? 'active' : '' }}"
                                href="{{ route('contacts.index') }}">
                                <span class="menu-icon">@include('svg.cms')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Contact Queries</span>
                            </a>
                        </div>
                        {{-- <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('subscribers') ? 'active' : '' }}"
                                href="{{ url('subscribers') }}">
                                <span class="menu-icon">@include('svg.cms')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Subcribers</span>
                            </a>
                        </div> --}}

                        <!-- {{-- <div class="drop_down menu-item">
                                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                            <a class="menu-link py-4" href="{{ route('cms') }}">
                                                <span class="menu-icon">@include('svg.cms')</span>
                                                <span class="title-menu sidebar-green fs-16 fw-500">CMS </span>
                                                <span class="menu-arrow"></span>
                                            </a>
                                            <div class="menu-sub menu-sub-accordion sidebar-menu">
                                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                                    <div class="menu-item">
                                                        <a class="menu-link {{ request()->is('/') ? 'active' : '' }}"
                                                            href="">
                                                            <span class="menu-icon">
                                                                <i class="fa-solid fa-pager fs-2">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                </i>
                                                            </span>
                                                            <span class="menu-title dropdown_menu">All Pages</span>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> --}} -->
                    @endif
                    @if (Auth::user()->hasRole('clinic_admin'))
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('home') ? 'active' : '' }}" href="{{ url('/home') }}">
                                <span class="menu-icon">@include('svg.dashboard')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('clinic.staff.list', 'clinic.staff.detail') ? 'active' : '' }}"
                                href="{{ route('clinic.staff.list') }}">
                                <span class="menu-icon">@include('svg.patients')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">My Staff</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('clinic.request', 'patients-view-details') ? 'active' : '' }}"
                                href="{{ route('clinic.request') }}">
                                <span class="menu-icon">@include('svg.request')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Patients</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('clinic.transaction') ? 'active' : '' }}"
                                href="{{ route('clinic.transaction') }}">
                                <span class="menu-icon">@include('svg.transactions')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Transactions</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('invoice') ? 'active' : '' }}"
                                href="{{ url('/invoice') }}">
                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Invoice</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('analytics') ? 'active' : '' }}"
                                href="{{ url('analytics') }}">
                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Report</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('notification') ? 'active' : '' }}"
                                href="{{ url('notification') }}">
                                <span class="menu-icon">@include('svg.notifications')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    @endif
                    <!-- {{-- for individual doctor, staff doctor and receptionist --}} -->
                    @if (auth()->user()->hasRole(['doctor', 'staff']))
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('home') ? 'active' : '' }}" href="{{ url('home') }}">
                                <span class="menu-icon">@include('svg.dashboard')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        @can('patient-list')
                            <div class="menu-item">
                                <a class="menu-link py-4 {{ request()->routeIs('patients') ? 'active' : '' }}"
                                    href="{{ route('patients') }}">
                                    <span class="menu-icon">@include('svg.patients')</span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Patients</span>
                                </a>
                            </div>
                        @endcan

                        @can('inventories-list')
                            <div class="menu-item">
                                <a class="menu-link py-4 {{ request()->routeIs('inventories.index') ? 'active' : '' }}"
                                    href="{{ route('inventories.index') }}">
                                    <span class="menu-icon">@include('svg.medicine')</span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Medicines</span>
                                </a>
                            </div>
                        @endcan

                        @can('prescriptions-list')
                            <div class="menu-item">
                                <a class="menu-link py-4 {{ request()->routeIs('prescriptions.index') ? 'active' : '' }}"
                                    href="{{ route('prescriptions.index') }}">
                                    <span class="menu-icon">@include('svg.prescription')</span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Prescription</span>
                                </a>
                            </div>
                            @if (auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Doctor')
                                <div class="menu-item">
                                    <a class="menu-link py-4 {{ request()->routeIs('prescriptions.approval') ? 'active' : '' }}"
                                        href="{{ route('prescriptions.approval') }}">
                                        <span class="menu-icon">@include('svg.prescription')</span>
                                        <span class="title-menu sidebar-green fs-16 fw-500">Prescriptions Requests</span>
                                    </a>
                                </div>
                            @endif
                        @endcan


                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('analytics') ? 'active' : '' }}"
                                href="{{ url('analytics') }}">
                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Analytics</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('invoice') ? 'active' : '' }}"
                                href="{{ url('/invoice') }}">
                                <span class="menu-icon">@include('svg.analytics')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Invoice</span>
                            </a>
                        </div>


                        <!-- {{-- <div class="menu-item">
                                        <a class="menu-link py-4 {{ request()->routeIs('prescription-requests') ? 'active' : '' }}"
                                            href="{{ route('prescription-requests') }}">
                                            <span class="menu-icon">@include('svg.prescription-requests')</span>
                                            <span class="title-menu sidebar-green fs-16 fw-500">Prescriptions Requests</span>
                                        </a>
                                    </div> --}} -->


                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('notification') ? 'active' : '' }}"
                                href="{{ url('notification') }}">
                                <span class="menu-icon">@include('svg.notifications')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    @endif
                    <!-- {{-- @if (Auth::user()->hasRole('staff'))
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('home') ? 'active' : '' }}"
                               href="{{ url('home') }}">
                                <span class="menu-icon">@include('svg.dashboard')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('patients_receptionist') ? 'active' : '' }}"
                               href="{{ url('patients_receptionist') }}">
                                <span class="menu-icon">@include('svg.patients')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Patients</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('prescriptions_receptionist') ? 'active' : '' }}"
                               href="{{ url('prescriptions_receptionist') }}">
                                <span class="menu-icon">@include('svg.prescription')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Prescription</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('notification') ? 'active' : '' }}"
                               href="{{ url('notification') }}">
                                <span class="menu-icon">@include('svg.notifications')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    @endif --}} -->
                    @if (Auth::user()->hasRole('patients'))
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('home') ? 'active' : '' }}" href="{{ url('home') }}">
                                <span class="menu-icon">@include('svg.dashboard')</span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->routeIs('orders.patient') ? 'active' : '' }}"
                                href="{{ route('orders.patient') }}">
                                <span class="menu-icon">@include('svg.orders')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Orders</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 {{ request()->is('notification') ? 'active' : '' }}"
                                href="{{ url('notification') }}">
                                <span class="menu-icon">@include('svg.notifications')</span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>


    <!-- {{--    <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-6" id="kt_app_sidebar_footer"> --}}
    {{--        <a href="{{ url('html/demo1/dist') }}" --}}
    {{--           class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100" --}}
    {{--           data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click" --}}
    {{--           title="200+ in-house components and 3rd-party plugins"> --}}
    {{--            <span class="btn-label">Docs & Components</span> --}}
    {{--            <i class="ki-duotone ki-document btn-icon fs-2 m-0"> --}}
    {{--                <span class="path1"></span> --}}
    {{--                <span class="path2"></span> --}}
    {{--            </i> --}}
    {{--        </a> --}}
    {{--    </div> --}} -->
</div>