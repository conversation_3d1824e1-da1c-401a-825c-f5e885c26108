<!DOCTYPE html>
<html lang="en">

<head>
    <base href="" />
    <title><?php echo e(App\Models\Setting::first()->title ?? ''); ?></title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="<?php echo e(App\Models\Setting::first()->title ?? ''); ?>" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon ?? ''); ?>" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.ckeditor.com/ckeditor5/44.2.1/ckeditor5.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/dashboard-responsive.css" rel="stylesheet" type="text/css" />
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" rel="stylesheet" />
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
    <link href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/css/intlTelInput.min.css" rel="stylesheet">
    <?php echo $__env->yieldPushContent('css'); ?>
</head>

<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true"
    data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <div id="secLoader">
        <div class="logo-loader">
            <div class="logo-container">
                <div class="circle">
                    <img src="<?php echo e(asset('website')); ?>/assets/media/images/rx-logo.png" alt="logo">
                </div>
                <span class="text">DIRECT</span>
            </div>
        </div>
    </div>
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
            } else {
                if (localStorage.getItem("data-bs-theme") !== null) {
                    themeMode = localStorage.getItem("data-bs-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-bs-theme", themeMode);
        }
    </script>
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
            <div id="kt_app_header" class="app-header" data-kt-sticky="true"
                data-kt-sticky-activate="{default: true, lg: true}" data-kt-sticky-name="app-header-minimize"
                data-kt-sticky-offset="{default: '200px', lg: '0'}" data-kt-sticky-animation="false">
                <div class="app-container container-fluid d-flex align-items-stretch justify-content-between"
                    id="kt_app_header_container">
                    <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
                        <div class="btn btn-icon btn-active-color-primary w-35px h-35px"
                            id="kt_app_sidebar_mobile_toggle">
                            <i class="ki-duotone ki-abstract-14 fs-2 fs-md-1">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                        <a href="<?php echo e(url('/')); ?>" class="d-lg-none">
                            <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon ?? ''); ?>"
                                class="h-30px" />
                        </a>
                    </div>
                    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1"
                        id="kt_app_header_wrapper">
                        <?php echo $__env->make('theme.layout.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                        <?php echo $__env->make('theme.layout.right_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
            <div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
                <?php echo $__env->make('theme.layout.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
                    <div class="d-flex flex-column flex-column-fluid">
                        <?php echo $__env->yieldContent('breadcrumb'); ?>
                        <?php echo $__env->yieldContent('content'); ?>
                    </div>
                    <div id="kt_app_footer" class="app-footer">
                        <div
                            class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
                            <div class="text-dark order-2 order-md-1">
                                <span
                                    class="text-muted fw-semibold me-1"><?php echo e(App\Models\Setting::first()->footer_text ?? ''); ?>&copy;</span>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up"></i>
    </div>
    <!--  -->
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/js/select2.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="text/javascript">
        <?php if(session()->has('message')): ?>
            Swal.fire({
                title: "<?php echo e(session()->get('title') ?? 'success!'); ?>",
                html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>
        <?php if(session()->has('flash_message')): ?>
            Swal.fire({
                title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>
        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to delete this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
    <script>
        var loader = document.getElementById("secLoader");
        window.addEventListener("load", function () {
            loader.style.display = "none"
        });
    </script>
    <!--  -->
    <script>
        $(document).ready(function () {
            $('#backButton').on('click', function () {
                if (document.referrer && document.referrer !== window.location.href) {
                    // If referrer exists and is not the same page
                    window.location.href = document.referrer;
                } else if (window.history.length > 1) {
                    // If browser has history
                    window.history.back();
                } else {
                    // Optional fallback if needed
                    alert('No previous page found.');
                }
            });
        });
    </script>
    <script>
        $('.select-all').on('click', function () {
            const isChecked = $(this).prop('checked');
            $('.row-select').prop('checked', isChecked);
        });

        $('.row-select').on('click', function () {
            if (!$('.row-select:checked').length) {
                $('.select-all').prop('checked', false);
            }
        });

    </script>
    <!--  -->

    <!-- reset-btn -->

    <script>
        jQuery(document).ready(function ($) {
            const $input = $('#searchMedicines');
            const $clearBtn = $('#clearSearchBtn');

            function toggleClearButton() {
                if ($.trim($input.val()) !== '') {
                    $clearBtn.show();
                } else {
                    $clearBtn.hide();
                }
            }
            $clearBtn.on('click', function () {
                $input.val('');
                toggleClearButton();
                $input.focus();
            });
            toggleClearButton();
            $input.on('input', toggleClearButton);
        });

    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const inputs = document.querySelectorAll(".searchMedicines");

            inputs.forEach(function (input) {
                const clearBtn = input.parentElement.querySelector(".clearSearchBtn");

                function toggleClearButton() {
                    if (input.value.trim() !== "") {
                        clearBtn.style.display = "block";
                    } else {
                        clearBtn.style.display = "none";
                    }
                }

                toggleClearButton();
                input.addEventListener("input", toggleClearButton);

                // Optional: Clear button par click karne par input reset
                clearBtn.addEventListener("click", function (e) {
                    e.preventDefault();
                    input.value = "";
                    toggleClearButton();
                    input.focus();
                });
            });
        });

    </script>


    <!-- start-end-range -->


    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Find all result containers (multiple instances)
            document.querySelectorAll('.result-container1').forEach(function (container) {
                const startRangeInput = container.querySelector('.start-range-input');
                const endRangeInput = container.querySelector('.end-range-input');
                const errorMessage = container.querySelector('.error-message');

                function openDatePicker(input) {
                    if (input.showPicker) {
                        input.showPicker();
                    } else {
                        input.focus();
                    }
                }

                endRangeInput.addEventListener('focus', function () {
                    if (!startRangeInput.value) {
                        errorMessage.style.display = 'block';
                        endRangeInput.blur();
                        startRangeInput.focus();
                    } else {
                        errorMessage.style.display = 'none';
                    }
                });

                startRangeInput.addEventListener('input', function () {
                    if (startRangeInput.value) {
                        errorMessage.style.display = 'none';
                    }
                });

                startRangeInput.addEventListener('click', function () {
                    openDatePicker(startRangeInput);
                });

                endRangeInput.addEventListener('click', function () {
                    if (startRangeInput.value) {
                        openDatePicker(endRangeInput);
                    } else {
                        errorMessage.style.display = 'block';
                        endRangeInput.blur();
                        startRangeInput.focus();
                    }
                });
            });
        });
    </script>



    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/intlTelInput.min.js"></script>
    <script>
        const input = document.querySelector("#phone");

        const iti = window.intlTelInput(input, {
            separateDialCode: true,
            initialCountry: "auto",
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js",
            geoIpLookup: function (success, failure) {
                fetch("https://ipinfo.io/json?token=YOUR_TOKEN")
                    .then(res => res.json())
                    .then(data => success(data.country))
                    .catch(() => success("us"));
            },
        });
        document.getElementById('phone').addEventListener('input', function (e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    </script>

    <script>
        let currentSortColumn = -1;
        let currentSortDirection = 'asc';
        function sortTableByColumn(table, columnIndex) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const headers = table.querySelectorAll('th');

            // Clear previous sort classes
            headers.forEach(th => th.classList.remove('sort-asc', 'sort-desc'));

            // Toggle direction
            if (currentSortColumn === columnIndex) {
                currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                currentSortColumn = columnIndex;
                currentSortDirection = 'asc';
            }

            headers[columnIndex].classList.add(currentSortDirection === 'asc' ? 'sort-asc' : 'sort-desc');

            rows.sort((a, b) => {
                const x = a.children[columnIndex].innerText.trim().toLowerCase();
                const y = b.children[columnIndex].innerText.trim().toLowerCase();

                if (!isNaN(x) && !isNaN(y)) {
                    return currentSortDirection === 'asc'
                        ? parseFloat(x) - parseFloat(y)
                        : parseFloat(y) - parseFloat(x);
                }

                return currentSortDirection === 'asc'
                    ? x.localeCompare(y)
                    : y.localeCompare(x);
            });

            rows.forEach(row => tbody.appendChild(row));
        }
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('table.sortTable').forEach(table => {
                const headers = table.querySelectorAll('thead th');
                headers.forEach((th, index) => {
                    th.style.cursor = 'pointer';
                    th.addEventListener('click', () => sortTableByColumn(table, index));
                });
            });
        });
    </script>

    <?php echo $__env->yieldPushContent('js'); ?>
</body>

</html><?php /**PATH D:\git\rx-direct\resources\views/theme/layout/master.blade.php ENDPATH**/ ?>