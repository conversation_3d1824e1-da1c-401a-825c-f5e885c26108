<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PrescriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $isStaff = auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Receptionist';

        return [
            'doctor_id' => $isStaff ? 'required|exists:users,id' : 'nullable', // Validate doctor_id if provided
            'patient_id' => 'required|exists:users,id', // Validate patient_id must exist
            'medicines' => 'required|array|min:1', // Ensure medicines is an array with at least one item
            'medicines.*.inventory_id' => 'required|exists:inventories,id', // Validate each inventory_id
            'medicines.*.prescribed_units' => 'required|integer|min:1', // Validate prescribed_units for each medicine
            'prescription_type' => 'required', // Validate prescription_type
            // 'prescription' => 'required', // Validate prescription_type
            'repetitions' => 'required_if:prescription_type,repeat',
        ];
    }

    public function messages()
    {
        return [
            'doctor_id.required' => 'Please Assign a Doctor.',
            'doctor_id.exists' => 'The selected doctor is invalid.',
            'patient_id.required' => 'The patient field is required.',
            'patient_id.exists' => 'The selected patient is invalid.',
            'medicines.required' => 'At least one medicine is required.',
            'medicines.array' => 'Medicines must be an array.',
            'medicines.min' => 'At least one medicine must be provided.',
            'medicines.*.inventory_id.required' => 'Each medicine must have a valid inventory ID.',
            'medicines.*.inventory_id.exists' => 'The selected inventory ID for one or more medicines is invalid.',
            'medicines.*.prescribed_units.required' => 'Each medicine must have a prescribed unit quantity.',
            'medicines.*.prescribed_units.integer' => 'The prescribed units must be a valid integer.',
            'medicines.*.prescribed_units.min' => 'The prescribed units must be at least 1.',
            'prescription_type.required' => 'Prescription type is required.',
            // 'prescription.required' => 'Please provide notes for Prescription.',
            'repetitions.required_if' => 'Duration is required when prescription type is "repeat".',
        ];
    }
}
