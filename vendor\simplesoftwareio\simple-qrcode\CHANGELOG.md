Simple QrCode
=============

##Change Log

#### 2.0.0
* Fixed a bug where the merge image was not reset correctly. (#93)
* Added Laravel auto discovery support. (#82) - Thanks [mazedlx](https://github.com/mazedlx)

#### 1.5.1
* Fixed a bug where a QrCode used within a loop would not generate correctly.

#### 1.5.0
* Added Portuguese translation. -Thanks [francisek](https://github.com/francisek) and [Varpie!](https://github.com/Varpie)
* Added BitCoin helper

#### 1.4.6
* Added Portuguese translation. -Thanks [felipewmartins!](https://github.com/felipewmartins)

#### 1.4.5
* Added Spanish translation. -Thanks [gtarraga!](https://github.com/gtarraga)
* Added Hindi translation. -Thanks [himanshu81494!](https://github.com/himanshu81494)

#### 1.4.4
* Added Italian translation. -Thanks [simocosimo!](https://github.com/simocosimo)

#### 1.4.3
* Updated the docs to our new format.
* Correct some typos in the Russian translation.

#### 1.4.2
* Added Russian translation. -Thanks Victoria!
* Added Chinese translation. -Thanks [blusewang!](https://github.com/blusewang)

#### 1.4.1
* Improved the quality of QrCodes with logos merge on them.  -Thanks [tuupke!](https://github.com/tuupke)

#### 1.4.0
* Added the `mergeString` method.  This method adds the ability to pass in a binary string to create a QrCode with an image on it.  -Thanks [tuupke!](https://github.com/tuupke)

#### 1.3.3
* Allow absolute paths to be used with the `merge` method.

#### 1.3.2
* Changed `bindShared` to `singleton` to support Laravel 5.2  -Thanks [lhdev!](https://github.com/lhdev)

#### 1.3.1
* Fixed a bug where `merge` did not work as expected when used with `size.`

#### 1.3.0
* Added `merge` method.
* Fixed a typo in the readme.

#### 1.2.0
* Added full support for Laravel 5
* Added the following helpers.
  * E-Mail
  * Geo
  * Phone Number
  * SMS
  * WiFi

#### 1.1.1
* Update dependencies.
* Corrected some composer file issues.
* Added some missing Laravel information to the QrCode Service Provider.

####1.1.0
* Added the ability to change the character encoding.
