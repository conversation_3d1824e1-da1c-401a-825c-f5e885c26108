@foreach ($prescriptions as $prescription)
    <tr>
        <!-- <td><input type="checkbox" class="row-select"></td> -->
        <td>
            <div class="d-flex gap-2 align-items-center">
                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg" alt="" height="40px"
                    width="40px">
                <div class="d-flex flex-column">
                    <h5 class="fw-500 number-gray-black">
                        {{ $prescription->patient->name ?? '' }}</h5>
                    <span class="input-text-gray fs-14 fw-400">{{ $prescription->patient->email ?? '' }}</span>
                </div>
            </div>
        </td>
        <td>{{ $prescription->prescribedBy->name ?? '' }}</td>
        <td>{{ $prescription->created_at->format('M d, Y') }}</td>
        <td>£{{ $prescription->total }}</td>
        <td><span
                class="badge badge-{{ $prescription->status == 0 ? 'inactive' : 'active' }} roboto fs-14 fw-400">{{ $prescription->status == 0 ? 'Pending' : 'Paid' }}</span>
        </td>

        <td><span
                class="badge badge-{{ $prescription->status == 0 ? 'pending-purple' : 'delivered-green' }} roboto fs-14 fw-400">{{ $prescription->status == 0 ? 'Pending' : 'Delivered' }}</span>
        </td>
        <td class="action_btn">
            <a href="#" class="btn btn-sm btn-flex btn-center button action_btn" data-kt-menu-trigger="click"
                data-kt-menu-placement="bottom-end">
                <i class="fa-solid fa-ellipsis-vertical"></i>
            </a>
            <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                data-kt-menu="true">
                <div class="menu-item px-3">
                    <a data-bs-toggle="modal" data-bs-target="#prescription_modal" class="menu-link px-3"
                        href="">View</a>
                </div>
            </div>
        </td>
    </tr>
@endforeach
