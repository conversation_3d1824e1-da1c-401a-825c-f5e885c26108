<div id="kt_app_sidebar" class="app-sidebar flex-column sidebar_dashboard" data-kt-drawer="true"
    data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true"
    data-kt-drawer-width="225px" data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">
        <a href="<?php echo e(url('/')); ?>">
            <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo ?? ''); ?>"
                class="h-50px app-sidebar-logo-default" />
            <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon ?? ''); ?>"
                class="h-30px app-sidebar-logo-minimize" />
        </a>
        <!--end::Logo image-->
        <!--begin::Sidebar toggle-->
        <!--begin::Minimized sidebar setup:
     if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
     1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
     2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
     3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
     4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
     }
     -->
        <!-- 
        
        
        
        
        
        
        
         -->
    </div>
    <div class="app-sidebar-menu overflow-hidden flex-column-fluid">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper">
            <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6  <?php if(Auth::user()->hasAnyRole(['admin', 'clinic_admin', 'doctor', 'staff', 'patients'])): ?> nav-items <?php endif; ?> "
                    id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                    <?php if(Auth::user()->hasRole('developer')): ?>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                            </div>
                        </div>
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="title-menu">User Management</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('crud-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">CRUD</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('crud_generator')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="title-menu">CRUD Generator</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">Users</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('users')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="title-menu">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role-list')): ?>
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Roles</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    <?php endif; ?>
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="<?php echo e(url('roles')); ?>">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="title-menu">Permissions</span>
                                    </a>
                                </div>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings-list')): ?>
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo e(url('settings')); ?>">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="title-menu">Settings</span>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <hr>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Menu</span>
                            </div>
                        </div>
                        <?php $__currentLoopData = $crud; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check($item->url . '-list')): ?>
                                <!--  -->
                                <div class="menu-item">
                                    <a class="menu-link <?php echo e(request()->is($item->url) ? 'active' : ''); ?>"
                                        href="<?php echo e(url($item->url ?? 'home')); ?>">
                                        <span class="menu-icon">
                                            <i class="ki-duotone ki-abstract-28 fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span
                                            class="title-menu"><?php echo e(preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name)); ?></span>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                    <?php if(Auth::user()->hasRole('admin')): ?>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('home') ? 'active' : ''); ?>" href="<?php echo e(url('home')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('admin.users') || request()->routeIs('users_details') ? 'active' : ''); ?>"
                                href="<?php echo e(route('admin.users')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.patients', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Users</span>
                            </a>
                        </div>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventories-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link py-4 <?php echo e(request()->routeIs('inventories.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('inventories.index')); ?>">
                                    <span class="menu-icon"><?php echo $__env->make('svg.medicine', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Medicines</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('user.requests') ? 'active' : ''); ?>"
                                href="<?php echo e(route('user.requests')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.request', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Registration Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <!-- 'prescription-details' -->
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('orders') ? 'active' : ''); ?>"
                                href="<?php echo e(route('orders')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.orders', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Prescription Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('subscriptions') ? 'active' : ''); ?>"
                                href="<?php echo e(url('/subscriptions')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.transactions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Transactions</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('invoice', 'admin-invoices-details*') ? 'active' : ''); ?>"
                                href="<?php echo e(url('invoice')); ?>">

                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Invoice</span>
                            </a>
                        </div>





                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('analytics') ? 'active' : ''); ?>"
                                href="<?php echo e(url('/analytics')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Reports</span>
                            </a>
                        </div>

                        <!--  -->

                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('notification') ? 'active' : ''); ?>"
                                href="<?php echo e(url('notification')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.notifications', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('cms') ? 'active' : ''); ?>" href="<?php echo e(url('cms')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.cms', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">CMS</span>
                            </a>
                        </div>
                        <!-- hadia -->


                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('contacts.index', 'contacts.show') ? 'active' : ''); ?>"
                                href="<?php echo e(route('contacts.index')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.cms', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Contact Queries</span>
                            </a>
                        </div>
                        

                        <!--  -->
                    <?php endif; ?>
                    <?php if(Auth::user()->hasRole('clinic_admin')): ?>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('home') ? 'active' : ''); ?>" href="<?php echo e(url('/home')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('clinic.staff.list', 'clinic.staff.detail') ? 'active' : ''); ?>"
                                href="<?php echo e(route('clinic.staff.list')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.patients', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">My Staff</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('clinic.request', 'patients-view-details') ? 'active' : ''); ?>"
                                href="<?php echo e(route('clinic.request')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.request', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Patients</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('clinic.transaction') ? 'active' : ''); ?>"
                                href="<?php echo e(route('clinic.transaction')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.transactions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Transactions</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('invoice') ? 'active' : ''); ?>"
                                href="<?php echo e(url('/invoice')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Invoice</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('analytics') ? 'active' : ''); ?>"
                                href="<?php echo e(url('analytics')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Report</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('notification') ? 'active' : ''); ?>"
                                href="<?php echo e(url('notification')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.notifications', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    <?php endif; ?>
                    <!--  -->
                    <?php if(auth()->user()->hasRole(['doctor', 'staff'])): ?>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('home') ? 'active' : ''); ?>" href="<?php echo e(url('home')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('patient-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link py-4 <?php echo e(request()->routeIs('patients') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('patients')); ?>">
                                    <span class="menu-icon"><?php echo $__env->make('svg.patients', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Patients</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventories-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link py-4 <?php echo e(request()->routeIs('inventories.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('inventories.index')); ?>">
                                    <span class="menu-icon"><?php echo $__env->make('svg.medicine', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Medicines</span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('prescriptions-list')): ?>
                            <div class="menu-item">
                                <a class="menu-link py-4 <?php echo e(request()->routeIs('prescriptions.index') ? 'active' : ''); ?>"
                                    href="<?php echo e(route('prescriptions.index')); ?>">
                                    <span class="menu-icon"><?php echo $__env->make('svg.prescription', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <span class="title-menu sidebar-green fs-16 fw-500">Prescription</span>
                                </a>
                            </div>
                            <?php if(auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Doctor'): ?>
                                <div class="menu-item">
                                    <a class="menu-link py-4 <?php echo e(request()->routeIs('prescriptions.approval') ? 'active' : ''); ?>"
                                        href="<?php echo e(route('prescriptions.approval')); ?>">
                                        <span class="menu-icon"><?php echo $__env->make('svg.prescription', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                        <span class="title-menu sidebar-green fs-16 fw-500">Prescriptions Requests</span>
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>


                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('analytics') ? 'active' : ''); ?>"
                                href="<?php echo e(url('analytics')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Analytics</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('invoice') ? 'active' : ''); ?>"
                                href="<?php echo e(url('/invoice')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.analytics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Invoice</span>
                            </a>
                        </div>


                        <!--  -->


                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('notification') ? 'active' : ''); ?>"
                                href="<?php echo e(url('notification')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.notifications', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    <?php endif; ?>
                    <!--  -->
                    <?php if(Auth::user()->hasRole('patients')): ?>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('home') ? 'active' : ''); ?>" href="<?php echo e(url('home')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu fs-16 fw-500 sidebar-green">Dashboard</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->routeIs('orders.patient') ? 'active' : ''); ?>"
                                href="<?php echo e(route('orders.patient')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.orders', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Orders</span>
                            </a>
                        </div>
                        <div class="menu-item">
                            <a class="menu-link py-4 <?php echo e(request()->is('notification') ? 'active' : ''); ?>"
                                href="<?php echo e(url('notification')); ?>">
                                <span class="menu-icon"><?php echo $__env->make('svg.notifications', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <span class="title-menu sidebar-green fs-16 fw-500">Notifications</span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>


    <!-- 
    
    
    
    
    
    
    
    
    
    
     -->
</div><?php /**PATH D:\git\rx-direct\resources\views/theme/layout/sidebar.blade.php ENDPATH**/ ?>