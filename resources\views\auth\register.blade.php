@extends('layouts.app')

@section('content')
    <div class="d-flex flex-column flex-column-fluid flex-lg-row register_page">
        <div class=" w-lg-75 py-15 pt-lg-0 px-10 hero-section_headings">
            <div class="d-flex flex-center flex-lg-start flex-column mt-20">
                <a href="{{ asset('/') }}" class="mb-7">
                    <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->logo ?? '' }}" />
                </a>
            </div>
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <form action="{{ route('register') }}" method="POST" enctype="multipart/form-data" id="registerForm">
                @csrf
                <div class="new-staff-modal login-header">
                    <div class="register_page_header">
                        <h4 class="modal-title white-color inter w-100 text-center">Register as a {{ request('role') ?? 'Member' }}</h4>
                    </div>
                    <div class="row mx-5 mt-5">
                        <input type="hidden" name="role" id="role" value="{{ request('role') ?? 'clinic_admin' }}">
                        {{-- <div class="col-md-12 my-8">
                            <div class="mb-5 border-line">
                                <label
                                    class="form-check  form-switch form-check-custom justify-content-center form-check-solid">
                                    <span class="fs-7 mb-3"> Register as Clinic </span>
                                    <input class="form-check form-check-input mb-4 mx-3 register_as" type="checkbox"
                                        value="1" checked="checked" />
                                    <input type="hidden" name="role" id="role" value="doctor">
                                    <span class="fs-7 mb-3"> Register as Prescriber</span>
                                </label>
                            </div>
                        </div> --}}


                        <!-- Title -->
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Title</h5>
                                <select class="form-select px-0 pt-0" name="professional_type">
                                    <option disabled selected>Type and Search</option>
                                    <option value="1">Dr
                                    </option>
                                    <option value="2">Dr
                                    </option>
                                </select>
                            </div>
                            @error('professional_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <!-- First Name -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">First Name</h5>
                                <input type="text" placeholder="As per official documents" class="w-100" name="firstname"
                                    value="{{ old('name') }}" />
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- surname -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Surname</h5>
                                <input type="text" placeholder="Enter your surname" class="w-100" name="surname"
                                    value="{{ old('name') }}" />
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Email -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Email</h5>
                                <input type="email" placeholder="Email address to create account" class="w-100" name="email"
                                    value="{{ old('email') }}" />
                                @error('email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Date of Birth -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Date of Birth</h5>
                                <input type="date" id="birthday" name="dob" value="{{ old('dob') }}"
                                    max="{{ date('Y-m-d') }}">
                                @error('dob')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Health Care Professional Type -->
                        <div class="col-lg-6">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Health Care Professional Type</h5>
                                <select class="form-select px-0 pt-0" name="professional_type">
                                    <option disabled selected>Type and Search</option>
                                    <option value="1" {{ old('professional_type') == 1 ? 'selected' : '' }}>Doctor
                                    </option>
                                    <option value="2" {{ old('professional_type') == 2 ? 'selected' : '' }}>Pharmacist
                                        Independent Prescriber
                                    </option>
                                    <option value="3" {{ old('professional_type') == 3 ? 'selected' : '' }}>Dentist
                                    </option>
                                    <option value="4" {{ old('professional_type') == 4 ? 'selected' : '' }}>Nurse
                                        Independent Prescriber
                                    </option>
                                    <option value="5" {{ old('professional_type') == 5 ? 'selected' : '' }}>
                                        Optometrist
                                        Independent Prescriber
                                    </option>
                                </select>
                            </div>
                            @error('professional_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        <!-- Registration Number -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Registration Number</h5>
                                <input type="text" placeholder="Enter your professional reg. no." class="w-100"
                                    name="reg_no" value="{{ old('reg_no') }}" />
                                @error('reg_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Phone Number -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Phone Number</h5>
                                <input type="tel" placeholder="Your mobile contact no." class="w-100" name="mobile_number"
                                    value="{{ old('mobile_number') }}" />
                                @error('mobile_number')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Clinic Address -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Clinic Address</h5>
                                <input type="text" placeholder="Clinic Address goes here" class="w-100"
                                    name="clinic_address" value="{{ old('clinic_address') }}" />
                                @error('clinic_address')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- password -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <div class="position-relative mb-3">
                                    <h5 class="deep-charcoal-blue inter pb-5">Password</h5>
                                    <input id="password" type="password" placeholder="Password"
                                        class="form-control p-0 bg-transparent @error('password') is-invalid @enderror"
                                        name="password" required autocomplete="new-password">
                                    @error('password')
                                        <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                    @enderror
                                    <a id="toggle-password"
                                        class="btn btn-sm btn-icon position-absolute  top-50 end-0 me-n2">
                                        <i class="far fa-eye"></i>
                                        <i class="far fa-eye-slash d-none"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <!-- Confirm Password -->
                        <div class="col-lg-6">
                            <div class="position-relative patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Confirm Password</h5>
                                <input id="confirm_password" type="password" placeholder="Confirm Password"
                                    class="form-control p-0 bg-transparent" name="password_confirmation" required
                                    autocomplete="new-password">
                                <a id="toggle-password-confirm"
                                    class="btn btn-sm btn-icon position-absolute top-50 end-0 me-n2">
                                    <i class="far fa-eye"></i>
                                    <i class="far fa-eye-slash d-none"></i>
                                </a>
                            </div>
                        </div>
                        <!-- Clinic Postcode or Street Name -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Clinic Postcode or Street Name</h5>
                                <input type="text" placeholder="Clinic's Name goes here" class="w-100" name="street_name"
                                    value="{{ old('street_name') }}" />
                                @error('street_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Unique Prescriber Code -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Unique Prescriber Code</h5>
                                <input type="number" placeholder="Enter unique prescriber code" class="w-100"
                                    name="prescriber_code" value="{{ old('prescriber_code') }}" />
                                @error('prescriber_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <!-- Address -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Home Address </h5>
                                <input type="text" placeholder="Enter Home Address" class="w-100" />
                            </div>
                        </div>
                        <!-- City -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">City </h5>
                                <input type="text" placeholder="Enter your City " class="w-100" />
                            </div>
                        </div>
                        <!-- Country -->
                        <div class="col-lg-6">
                            <div class="patients-email mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Country </h5>
                                <input type="text" placeholder="Enter your Country" class="w-100" />
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="patients-email mb-7">
                                        <label class="grey-input-text checked-box" for="termsCheckbox">
                                            <input type="checkbox" id="termsCheckbox" name="termsCheckbox" required="">
                                            I have read and agree to the <a href="/terms-and-conditions" target="_blank">T
                                                &amp; Cs</a>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="patients-email mb-7">
                                        <label class="grey-input-text checked-box" for="termsCheckbox">
                                            <input type="checkbox" id="termsCheckbox" name="termsCheckbox" required="">
                                            I understand and agree to the
                                             <a href="/terms-and-conditions" target="_blank">Terms of use</a>
                                        </label>
                                    </div>
                                </div>
                            </div>


                        </div>
                        <div class="col-lg-6 border-line">
                            <div class="clinic-certificate mb-7 pt-4">
                                <div class="upload-container upload_doc pt-5">
                                    <img src="{{ asset('website') }}/assets/media/images/upload_doc.svg" alt="">
                                    <!-- <p class="fs-14 fw-500 text-center my-3">Upload Photo ID document (Passport or
                                                            Drivers Licence)</p> -->
                                    <p class="fs-14 fw-500 text-center my-3">A copy of your UK passport or current UK photo
                                        driving license:</p>
                                    <p class="drag-text text-center fs-12 fw-500">
                                        Drag a File here or
                                        <label class="uploadLabel">Upload a Document</label>
                                    </p>
                                    <input type="file" class="fileUpload" accept=".pdf" style="display: none;"
                                        name="id_document" value="{{ old('id_document') }}">
                                </div>
                                @error('id_document')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-lg-6 border-line">
                            <div class="clinic-certificate mb-7 pt-4">
                                <div class="upload-container upload_doc pt-5">
                                    <img src="{{ asset('website') }}/assets/media/images/upload_doc.svg" alt="">
                                    <p class="fs-14 fw-500 text-center my-3">Upload Signature </p>
                                    <p class="drag-text text-center fs-12 fw-500">
                                        Drag a File here or
                                        <label class="uploadLabel">Upload Signature</label>
                                    </p>
                                    <input type="file" class="fileUpload" accept=".pdf" style="display: none;"
                                        name="idemnity_certificate" value="{{ old('idemnity_certificate') }}">
                                </div>
                                @error('idemnity_certificate')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="d-grid my-10">
                                <button type="button" id="submit_registeration_form"
                                    class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save w-md-50 m-auto">
                                    <span class="indicator-label">Sign up</span>
                                    <span class="indicator-progress">Please wait...
                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                            </div>
                            {{-- <div class="text-gray-500 text-center fw-semibold fs-6 pb-5">Already have an Account?
                                <a href="{{ route('login') }}" class="uploadLabel">Sign in</a>
                            </div> --}}

                        </div>

                        {{-- <div class="col-lg-6"> --}}
                            {{-- <div class="patients-email mb-7"> --}}
                                {{-- <h5 class="deep-charcoal-blue inter pb-5">Payment Type</h5> --}}
                                {{-- <select name="type" class="form-select px-0 pt-0"> --}}
                                    {{-- <option value="Type 1" {{ old('type')=='Type 1' ? 'selected' : '' }}>Type 1 --}}
                                        {{-- </option> --}}
                                    {{-- <option value="Type 2" {{ old('type')=='Type 2' ? 'selected' : '' }}>Type 2 --}}
                                        {{-- </option> --}}
                                    {{-- </select> --}}
                                {{-- @error('type') --}}
                                {{-- <div class="invalid-feedback">{{ $message }}</div> --}}
                                {{-- @enderror --}}
                                {{-- </div> --}}
                            {{-- </div> --}}
                    </div>
                </div>
            </form>
        </div>
        <div
            class="d-flex w-md-700px flex-lg-row-auto  flex-column align-items-center register-bg-img justify-content-center">
            <div><img class="login_image w-100 h-100 img_fluid"
                    src="{{ asset('website') }}/assets/media/images/login_image.png" alt="image" /></div>
        </div>
    </div>
    {{-- otp modal --}}
    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">New message</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="verify_otp" method="POST">
                    @csrf
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="recipient-name" class="col-form-label">Verify OTP:</label>
                            <input type="number" class="form-control" id="otp" name="otp" min="0"
                                oninput="validity.valid||(value='');">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Send message</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function () {
            $(".register_as").on("change", function () {
                var value = $(this).prop("checked") ? 'doctor' : 'clinic_admin';
                $('#role').val(value);
            });
            $("#submit_registeration_form").on("click", function (event) {
                event.preventDefault(); // Prevent default form submission

                var form = $("#registerForm")[0]; // Get the form element
                var formData = new FormData(form); // Create FormData object

                // First check if email exists
                $.ajax({
                    url: "{{ route('check.email') }}",
                    method: "POST",
                    data: {
                        email: formData.get('email'),
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.exists) {
                            $("#registerForm").find("[name='email']").after("<span class='error-message text-danger'>This email is already registered</span>");
                            return;
                        }

                        // If email doesn't exist, proceed with form validation
                        if (!validateForm($("#registerForm"), rules)) return;

                        $("#exampleModal").modal("show");
                        document.getElementById('otp').value = '';

                        $.ajax({
                            url: "{{ route('registration_session') }}",
                            method: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function (response) {
                                console.log(response.message);
                            },
                            error: function (error) {
                                console.log("Error:", error);
                                if (error.responseJSON && error.responseJSON.errors) {
                                    // Handle validation errors
                                    Object.keys(error.responseJSON.errors).forEach(function(key) {
                                        var input = $("#registerForm").find("[name='" + key + "']");
                                        input.after("<span class='error-message text-danger'>" + error.responseJSON.errors[key][0] + "</span>");
                                    });
                                }
                            }
                        });
                    },
                    error: function(error) {
                        console.log("Error checking email:", error);
                    }
                });
            });


            // General Validation Function
            function validateForm(form, rules) {
                $(".error-message").remove(); // Remove previous errors
                var isValid = true;

                $.each(rules, function (field, rule) {
                    var input = form.find("[name='" + field + "']");
                    if (input.length === 0) return; // Skip validation if the field is missing

                    var value = input.val() ? input.val().trim() : "";

                    // Required field validation
                    if (rule.required && !value) {
                        if (field === 'id_document') {
                            input.after("<span class='error-message text-danger'>Valid photo ID document is required</span>");
                        } else if (field === 'idemnity_certificate') {
                            input.after("<span class='error-message text-danger'>Signature upload is required</span>");
                        } else if (field === 'firstname') {
                            input.after("<span class='error-message text-danger'>First name is required</span>");
                        } else if (field === 'surname') {
                            input.after("<span class='error-message text-danger'>Surname is required</span>");
                        } else {
                            input.after("<span class='error-message text-danger'>This field is required</span>");
                        }
                        isValid = false;
                        return;
                    }

                    // Skip further validation if value is empty
                    if (!value) return;

                    // File type validation
                    if (field === 'id_document' || field === 'idemnity_certificate') {
                        var file = input[0].files[0];
                        if (file) {
                            var validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
                            if (!validTypes.includes(file.type)) {
                                input.after("<span class='error-message text-danger'>Please upload only JPG, PNG or PDF files</span>");
                                isValid = false;
                            }
                        }
                    }

                    // Email validation
                    if (rule.email && !/^\S+@\S+\.\S+$/.test(value)) {
                        input.after("<span class='error-message text-danger'>Enter valid email address</span>");
                        isValid = false;
                    }

                    // Disposable email validation
                    if (rule.notDisposable && field === 'email' && value) {
                        var disposableEmailDomains = [
                            'yopmail.com', 'mailinator.com', 'tempmail.com', 'temp-mail.org',
                            'guerrillamail.com', 'sharklasers.com', 'guerrillamail.net',
                            'guerrillamail.org', 'guerrillamail.de', 'guerrillamail.info',
                            'guerrillamail.biz', 'spam4.me', 'trashmail.com', 'trashmail.net',
                            'trashmail.me', 'dispostable.com', 'maildrop.cc', 'mailnesia.com',
                            'mailcatch.com', 'tempr.email', 'temp-mail.io', 'temp-mail.ru',
                            '10minutemail.com', '10minutemail.net', '10minutemail.org',
                            '10minutemail.de', '10minutemail.info', '10minutemail.biz',
                            'mailinator2.com', 'mailinator3.com', 'mailinator4.com', 'mailinator5.com'
                        ];

                        var emailDomain = value.split('@')[1];
                        if (emailDomain && disposableEmailDomains.includes(emailDomain.toLowerCase())) {
                            input.after("<span class='error-message text-danger'>Disposable email addresses are not allowed. Please use a valid email address.</span>");
                            isValid = false;
                        }
                    }

                    // Phone number validation
                    if (rule.phone && field === 'mobile_number' && value) {
                        // Remove all non-digit characters except + at the beginning
                        var cleanedValue = value.replace(/[^\d+]/g, '');

                        // Check if it starts with + (international format)
                        var phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10,15}$/;

                        if (!phoneRegex.test(value)) {
                            input.after("<span class='error-message text-danger'>Please enter a valid mobile number (10-15 digits, optionally with country code).</span>");
                            isValid = false;
                        }
                    }

                    // Password validation
                    if (field === 'password') {
                        if (value.length < rule.minlength) {
                            input.after("<span class='error-message text-danger'>Minimum 8 characters required</span>");
                            isValid = false;
                        }
                        else if (value.length > rule.maxlength) {
                            input.after("<span class='error-message text-danger'>Maximum 20 characters allowed</span>");
                            isValid = false;
                        }
                    }

                    // Password confirmation validation
                    if (field === 'password_confirmation') {
                        var password = form.find("[name='password']").val();
                        if (value !== password) {
                            input.after("<span class='error-message text-danger'>Passwords do not match</span>");
                            isValid = false;
                        }
                    }

                    // Terms checkbox validation
                    if (field === 'termsCheckbox') {
                        var termsCheckboxes = form.find('input[name="termsCheckbox"]');
                        var allChecked = true;
                        termsCheckboxes.each(function() {
                            if (!$(this).prop('checked')) {
                                allChecked = false;
                                return false; // break the loop
                            }
                        });

                        if (!allChecked) {
                            termsCheckboxes.first().closest('.row').after("<span class='error-message text-danger d-block'>You must accept both Terms & Conditions and Terms of Use to proceed</span>");
                            isValid = false;
                        }
                    }
                });

                return isValid;
            }


            $("#verify_otp").on("submit", function (event) {
                event.preventDefault(); // Prevent form submission

                var otp = $("#otp").val().trim();

                // Remove any previous error message
                $(".error-message").remove();
                if (!otp) {
                    $("#otp").after(
                        "<span class='error-message text-danger'>OTP is required. Please enter the OTP sent to your email.</span>"
                    );
                    return; // Stop the submission if OTP is missing
                }

                // If OTP is valid, proceed with AJAX request
                var formData = $(this).serialize(); // Serialize the form data

                $.ajax({
                    url: '{{ route('verify_otp') }}',
                    method: 'POST',
                    data: formData,
                    success: function (response) {
                        $("#exampleModal").modal("hide");

                        if (response.success) {
                            console.log('OTP Verified Successfully!');

                            Swal.fire({
                                icon: 'success',
                                title: 'OTP Verified',
                                text: 'Your OTP was successfully verified.'
                            }).then(() => {
                                // Redirect to the home page after closing the Swal message
                                window.location.href =
                                    '/'; // Adjust the URL if you have a different home page URL
                            });

                        } else {
                            Swal.fire({
                                icon: 'error',
                                title: 'OTP Verification Failed',
                                text: 'The OTP you entered is incorrect. Please try again.'
                            });
                        }
                    },
                    error: function (error) {
                        $("#exampleModal").modal("hide");
                        console.log('Error:', error);
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Something went wrong. Please try again later.'
                        });
                    }
                });
            });

        });
    </script>
    <script>
        $('#toggle-password').on('click', function () {
            var passwordField = $('#password');
            var passwordFieldType = passwordField.attr('type');
            if (passwordFieldType === 'password') {
                passwordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                passwordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
        $('#toggle-password-confirm').on('click', function () {
            var confirmPasswordField = $('#confirm_password');
            var confirmPasswordFieldType = confirmPasswordField.attr('type');
            if (confirmPasswordFieldType === 'password') {
                confirmPasswordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                confirmPasswordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
    </script>

    <script>
        document.querySelectorAll('.fileUpload').forEach((fileInput) => {
            const uploadLabel = fileInput.closest('.upload-container').querySelector('.uploadLabel');

            fileInput.addEventListener('change', function () {
                if (fileInput.files.length > 0) {
                    uploadLabel.textContent = `File uploaded: ${fileInput.files[0].name}`;
                    uploadLabel.style.color = "#197C48"; // Optional: Change color to indicate success
                }
            });

            // Allow label to trigger file upload
            uploadLabel.addEventListener('click', () => fileInput.click());
        });
    </script>
@endpush
