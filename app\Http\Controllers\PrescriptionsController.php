<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Models\Prescription;
use App\Http\Requests\PrescriptionRequest;
use App\Models\Inventory;
use App\Models\PrescriptionInventory;
use App\Models\PrescriptionPayment;
use App\Models\Setting;
use App\Models\Transaction;
use App\Models\User;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PrescriptionsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $doctors = null;
        if ($user->hasRole('staff') && $user->profile->role == 'Receptionist') {
            $doctors = User::where('user_id', $user->createdBy->id)->whereHas('profile', function ($q) {
                $q->where('role', 'doctor');
            })->get();
        }
        $patients = User::where('user_id', $user->id)->whereHas('roles', function ($q) {
            $q->where('name', 'patients');
        })->get();

        $user = auth()->user();
        $query = Prescription::orderBy('created_at', 'DESC')->where('user_id', $user->id);

        // Add type filter
        if ($request->has('type') && $request->input('type') !== null) {
            $query->where('type', $request->input('type'));
        }

        $perPage = $request->has('per_page') ? (int) $request->per_page : 5;
        $prescriptions = $query->paginate($perPage);

        $prescriptionCount = $user->ownPrescriptions->count();

        return view('prescriptions.index', compact('doctors', 'patients', 'prescriptionCount', 'prescriptions'));
    }
    public function prescriptionsApproval()
    {
        $user = auth()->user();
        $prescriptions = Prescription::orderBy('created_at', 'DESC')->where('doctor_id', $user->id)->where('user_id', '!=', $user->id)->paginate(5);
        return view('prescriptions.approvals', compact('prescriptions'));
    }

    public function getStaffPrescriptions(Request $request)
    {
        $user = auth()->user();
        $query = Prescription::orderBy('created_at', 'DESC')->where('user_id', $user->id);

        // Add type filter
        if ($request->has('type') && $request->input('type') !== null) {
            $query->where('type', $request->input('type'));
        }
        // Add status filter
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('admin_approval', $request->input('status'));
        }
        // Set fixed per_page to 5
        $prescriptions = $query->paginate(5)->withQueryString();
        return response()->json([
            'rows' => view('dashboard.staff.prescription-list', compact('prescriptions'))->render(),
            'pagination' => view('dashboard.components.pagination', [
                'paginator' => $prescriptions,
                'type' => 'prescription'
            ])->render()
        ]);
    }
    public function getClinicprescription(Request $request)
    {
        $user = auth()->user();
        $query = Prescription::orderBy('created_at', 'DESC')->where('clinic_id', $user->id);

        // Add search functionality
        if ($request->has('search') && $request->input('search') != null) {
            $search = $request->input('search');
            $query->whereHas('patient', function ($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%');
            });
        }

        // Add status filter
        if ($request->has('status') && $request->input('status') !== null) {
            $query->where('status', $request->input('status'));
        }

        // Add prescription type filter
        if ($request->has('prescription_type') && $request->input('prescription_type') !== null) {
            $query->where('prescription_type', $request->input('prescription_type'));
        }

        $perPage = $request->has('per_page') ? (int) $request->per_page : 30;
        $prescriptions = $query->paginate($perPage);

        return response()->json([
            'rows' => view('dashboard.clinic-admin.prescription-list', compact('prescriptions'))->render(),
            'pagination' => view('dashboard.components.pagination', [
                'paginator' => $prescriptions,
                'type' => 'prescription',
            ])->render()
        ]);
    }

    public function clinicTransactions()
    {
        $transactions = Transaction::where('user_id', auth()->user()->id)->paginate(10);
        return view('dashboard.clinic-admin.transactions', compact('transactions'));
    }
    public function getClinicprescriptionByType(Request $request)
    {
        $type = $request->role;
        $user = auth()->user();
        $query = Prescription::where('clinic_id', $user->id)->whereHas('patient', function ($q) use ($type) {
            $q->where('type', $type);
        });
        $perPage = $request->has('per_page') ? (int) $request->per_page : 10;
        $prescriptions = $query->paginate($perPage);
        return response()->json([
            'rows' => view('dashboard.clinic-admin.transaction-list', compact('prescriptions'))->render(),
            'pagination' => view('dashboard.components.pagination', ['paginator' => $prescriptions, 'type' => $type])->render()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('prescriptions.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  PrescriptionRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(PrescriptionRequest $request)
    {
        // return $request->all();
        DB::beginTransaction();
        $user = auth()->user();
        try {
            $minimum_charge = Setting::first()->minimum_charge ?? 14.95;
            // Retrieve doctor, patient, and medicines from the request
            $medicineDetails = $request->input('medicines');

            $doctorId = $request->input('doctor_id') ?? null;
            $patientId = $request->input('patient_id');
            $doctorApprval = 0;
            if ($user->hasRole('doctor')) {
                $type = $user->type;
                $doctorId = $user->id;
                $clinicId = $user->id;
                $doctorApprval = 1;
            } elseif ($user->hasRole('staff')) {
                $type = $user->createdBy->type;
                $clinicId = $user->user_id;
                if ($user->profile->role == 'Doctor') {
                    $doctorId = $user->id;
                    $doctorApprval = 1;
                } else {
                    $doctorId = $request->input('doctor_id') ?? null;
                }
            }

            // Create a new prescription
            $prescription = Prescription::create([
                'user_id' => $user->id,
                'type' => $type,
                'prescription_type' => $request->input('prescription_type') ?? 'one_time',
                'duration' => $request->input('prescription_type') == 'repeat' ? 21 : null,
                'repetitions' => $request->input('repetitions') ?? null,
                'clinic_id' => $clinicId,
                'doctor_id' => $doctorId,
                'doctor_approval' => $doctorApprval,
                'patient_id' => $patientId,
                'prescription' => $request->input('prescription'),
                'total' => 0, // We will calculate the total later
            ]);

            $totalPrice = 0; // Initialize total price

            // Loop through each prescribed medicine
            foreach ($medicineDetails as $medicine) {
                $inventory = Inventory::findOrFail($medicine['inventory_id']);
                $prescribedUnits = $medicine['prescribed_units'];

                $pack_size = $inventory->pack_size;
                $cost_price = $inventory->cost_price;
                $sale_price_per_pack = $cost_price * 2;

                // Ensure enough inventory is available
                // if ($inventory->stock_quantity < $medicine['prescribed_units']) {
                //     return response()->json(['error' => 'Not enough stock available'], 400);
                // }

                // Calculate total sale price
                $cost_per_tablet = $sale_price_per_pack / $pack_size;
                $totalMedicinePrice = round($cost_per_tablet * $medicine['prescribed_units'], 2);

                // // Deduct stock
                // $inventory->stock_quantity -= $medicine['prescribed_units'];
                // $inventory->save();

                PrescriptionInventory::create([
                    'prescription_id' => $prescription->id,
                    'name' => $inventory->name,
                    'inventory_id' => $medicine['inventory_id'],
                    'dosage' => $medicine['dosage'],
                    'prescribed_units' => $prescribedUnits,
                    'total_price' => $totalMedicinePrice,
                ]);
                $totalPrice += $totalMedicinePrice;
            }
            $totalPrice = max($totalPrice, $minimum_charge);

            $prescription->update(['total' => $totalPrice]);
            // Commit the transaction
            DB::commit();

            return redirect()->route('add_prescription')->with(['type' => 'success', 'message' => 'Prescription created successfully', 'title' => 'Done']);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $prescription = Prescription::with(['doctor', 'patient', 'inventories'])->where('order_id', $id)->firstOrFail();

        if (!$prescription) {
            return response()->json(['error' => 'Prescription not found'], 404);
        }

        return response()->json([
            'prescription' => $prescription,
            'inventories' => $prescription->inventories,
            'total' => $prescription->total
        ]);
    }
    public function showOLD($id)
    {
        $prescription = Prescription::where('order_id', $id)->firstOrFail();
        return view('prescriptions.show', ['prescription' => $prescription]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $prescription = Prescription::findOrFail($id);
        return view('prescriptions.edit', ['prescription' => $prescription]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  PrescriptionRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(PrescriptionRequest $request, $prescriptionId)
    {
        // return $request->all();
        DB::beginTransaction();
        try {
            $minimum_charge = Setting::first()->minimum_charge ?? 14.95;
            // Retrieve doctor, patient, and medicines from the request
            $medicineDetails = $request->input('medicines');
            // Retrieve the existing prescription
            $prescription = Prescription::findOrFail($prescriptionId);

            $prescription->update([
                'prescription_type' => $request->input('prescription_type') ?? 'one_time',
                'repetitions' => $request->input('repetitions') ?? 0,
            ]);

            $existingMedicines = PrescriptionInventory::where('prescription_id', $prescription->id)
                ->pluck('inventory_id'); // Get the IDs of the medicines already associated with the prescription

            // Find medicines that were in the prescription but are not in the new request
            $medicinesToRemove = $existingMedicines->diff(collect($medicineDetails)->pluck('inventory_id'));

            // Remove medicines that are no longer in the request (i.e., those that are removed from the prescription)
            PrescriptionInventory::where('prescription_id', $prescription->id)
                ->whereNotIn('inventory_id', collect($medicineDetails)->pluck('inventory_id')->toArray())
                ->delete();

            $totalPrice = 0; // Initialize total price

            // Loop through each prescribed medicine
            foreach ($medicineDetails as $medicine) {
                $inventory = Inventory::findOrFail($medicine['inventory_id']);
                $prescribedUnits = $medicine['prescribed_units'];

                $pack_size = $inventory->pack_size;
                $cost_price = $inventory->cost_price;
                $sale_price_per_pack = $cost_price * 2;

                // Calculate total sale price
                $cost_per_tablet = $sale_price_per_pack / $pack_size;
                $totalMedicinePrice = round($cost_per_tablet * $medicine['prescribed_units'], 2);

                // Update or create new prescription inventory record
                PrescriptionInventory::updateOrCreate(
                    ['prescription_id' => $prescription->id, 'inventory_id' => $medicine['inventory_id']],
                    [
                        'name' => $inventory->name,
                        'dosage' => $medicine['dosage'],
                        'prescribed_units' => $prescribedUnits,
                        'total_price' => $totalMedicinePrice,
                    ]
                );
                $totalPrice += $totalMedicinePrice;
            }
            $totalPrice = max($totalPrice, $minimum_charge);

            // Update the total price for the prescription
            $prescription->update(['total' => $totalPrice]);

            // Delete the medicines that are no longer in the request
            PrescriptionInventory::where('prescription_id', $prescription->id)
                ->whereIn('inventory_id', $medicinesToRemove)
                ->delete();

            DB::commit();

            return redirect()->back()->with(['type' => 'success', 'message' => 'Prescription updated successfully', 'title' => 'Done']);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    public function updateOLD(PrescriptionRequest $request, $id)
    {
        $prescription = Prescription::findOrFail($id);
        $prescription->user_id = $request->input('user_id');
        $prescription->patient_id = $request->input('patient_id');
        $prescription->prescription = $request->input('prescription');
        $prescription->doctor_id = $request->input('doctor_id');
        $prescription->doctor_approval = $request->input('doctor_approval');
        $prescription->admin_approval = $request->input('admin_approval');
        $prescription->total = $request->input('total');
        $prescription->status = $request->input('status');
        $prescription->save();

        return to_route('prescriptions.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $prescription = Prescription::findOrFail($id);
        $prescription->delete();

        return to_route('prescriptions.index');
    }


    public function ordersPatient()
    {
        // $prescription = Prescription::find(69);
        // return $prescription->payments;
        $user = auth()->user();
        $orders = Prescription::where('patient_id', $user->id)->orderBy('created_at', 'DESC')->paginate(5);
        // $orders = Prescription::where('patient_id', $user->id)->where('doctor_approval', 1)->where('admin_approval', 1)->orderBy('created_at', 'DESC')->paginate(5);
        return view('dashboard.patients.orders', compact('orders'));
    }

    public function orderDetails($id)
    {
        $prescription = Prescription::with(['inventories', 'doctor', 'prescribedBy', 'payments'])->where('order_id', $id)->firstOrFail();
        return view('dashboard.patients.order-details', compact('prescription'));
    }



    public function generatePdf()
    {
        $data = ['title' => 'Test PDF']; // Data to pass to the view

        $pdf = Pdf::loadView('website.templates.pdfs.pdf_view', $data); // Replace 'pdf.view' with your actual view

        return $pdf->download('filename.pdf'); // Download the generated PDF
    }

    public function toggleStatus(Request $request, $slug)
    {
        $prescription = Prescription::whereOrderId($slug)->firstOrFail();
        $status = $request->status === 'Approve' ? 1 : 2;

        // Check if the role is admin and update fields accordingly
        if (auth()->user()->hasRole('admin')) {
            if ($status === 2 && $request->filled('note')) {
                $prescription->admin_reject_comment = $request->note; // Change column name to admin_reject_comment
            }
            $prescription->admin_approval = $status; // Change column name to admin_approval
        } else {
            if ($status === 2 && $request->filled('note')) {
                $prescription->reject_comment = $request->note; // Original column for non-admin roles
            }
            $prescription->doctor_approval = $status; // Original column for non-admin roles
        }

        $prescription->save();
        // If the status is 1 (Approve), call updatePrescriptionSchedule
        if ($status === 1) {
            $this->updatePrescriptionSchedule($request, $prescription->order_id); // Call the function and pass the order_id
        }
        return response()->json(['status' => 'success', 'new_status' => $status]);
    }


    public function toggleStatusOLD(Request $request, $slug)
    {
        $prescription = Prescription::whereOrderId($slug)->firstOrFail();
        $status = $request->status === 'Approve' ? 1 : 2;
        if ($status === 2 && $request->filled('note')) {
            $prescription->reject_comment = $request->note; // Make sure this column exists
        }
        $prescription->doctor_approval = $status;
        $prescription->save();
        return response()->json(['status' => 'success', 'new_status' => $prescription->doctor_approval]);
    }

    public function getPrescriptionData($id)
    {
        $prescription = Prescription::with('inventories', 'patient')->where('order_id', $id)->firstOrFail();

        // Return the data as JSON for the AJAX response
        return response()->json([
            'id' => $prescription->id,
            'prescription' => $prescription->prescription,
            'patient_id' => $prescription->patient_id,
            'notes' => $prescription->prescription,
            'prescription_type' => $prescription->prescription_type,
            'repetitions' => $prescription->repetitions,
            'patient_name' => $prescription->patient->name,
            'patient_email' => $prescription->patient->email,
            'doctor_id' => $prescription->doctor_id,
            'medicines' => $prescription->inventories->map(function ($medicine) {
                return [
                    'inventory_id' => $medicine->id,
                    'name' => $medicine->name,
                    'dosage' => $medicine->pivot->dosage,
                    'prescribed_units' => $medicine->pivot->prescribed_units,
                    'sale_price' => $medicine->sale_price,
                    'cost_price' => $medicine->cost_price,
                    'pack_size' => $medicine->pack_size,
                ];
            })
        ]);
    }

    public function prescriptionRequestDetails($id)
    {
        $user = auth()->user();
        $doctors = null;
        if ($user->hasRole('staff') && $user->profile->role == 'Receptionist') {
            $doctors = User::where('user_id', $user->createdBy->id)->whereHas('profile', function ($q) {
                $q->where('role', 'doctor');
            })->paginate(10);
        }
        $prescription = Prescription::with(['patient'])->where('order_id', $id)->firstOrFail();
        $inventories = $prescription->inventories()->paginate(10);
        $payments = $prescription->payments()->paginate(10);
        return view('prescription-requests.prescription-details', compact('prescription', 'doctors', 'inventories', 'payments'));
    }



    public function updatePrescriptionSchedule(Request $request, $id)
    {
        // Get the prescription and its associated payments ordered by payment_date
        $prescription = Prescription::where('order_id', $id)->firstOrFail();
        $repetitions = $prescription->repetitions;  // Get updated repetitions from the request
        $duration = $request->has('duration') && $request->duration !== null ? $request->duration : $prescription->duration;
        $prescriptionId = $prescription->id;
        // if ($request->isNotEmpty()) {
        //     $prescription->update($request->all());
        // }

        // Fetch all existing payments ordered by payment_date
        $existingPayments = $prescription->payments()->orderBy('payment_date')->get();

        // If there are existing payments, delete the ones with status 'pending' and payment_date > today
        if ($existingPayments->isNotEmpty()) {
            $existingPayments->where('status', 'pending')
                ->where('payment_date', '>', Carbon::today())
                ->each(function ($payment) {
                    $payment->delete(); // Delete the payments that are in the future and pending
                });
        }

        // Set the paymentDate based on whether there are existing records
        $paymentDate = $existingPayments->isEmpty() ? Carbon::today() : Carbon::parse($prescription->payments->last()->payment_date)->copy()->addDays($duration);

        // Create new records starting from the last valid payment date or today
        for ($i = $prescription->payments()->count(); $i < $repetitions; $i++) {
            $validUntil = $paymentDate->copy()->addDays(7); // valid until is 7 days after the payment date
            PrescriptionPayment::create([
                'prescription_id' => $prescriptionId,
                'payment_date' => $paymentDate,
                'valid_until' => $validUntil,
                'status' => 'pending', // Default to pending
                'total' => $prescription->total, // Default to pending
            ]);
            $paymentDate = $paymentDate->copy()->addDays($duration); // Add the duration to the payment date for next repetition
        }
    }

    public function generatePrescriptionPdf($orderId)
    {
        $prescription = Prescription::with(['doctor', 'patient', 'inventories', 'prescribedBy'])
            ->where('order_id', $orderId)
            ->firstOrFail();
        $qrUrl = route('prescriber_profile', ['id' => $prescription->doctor->id]); // Example route

        $data = [
            'prescription' => $prescription,
            'doctor' => $prescription->doctor,
            'patient' => $prescription->patient,
            'medicines' => $prescription->inventories,
            'created_at' => $prescription->created_at->format('m d Y'),
            'order_id' => $prescription->order_id,
            'admin_id' => $prescription->prescribedBy->id ?? '',
            'token_id' => $prescription->order_id,
            'doctor_name' => $prescription->doctor->name ?? '',
            'chamber_name' => $prescription->doctor->profile->chamber_name ?? '',
            'chamber_address' => $prescription->doctor->profile->chamber_address ?? '',
            'patient_name' => $prescription->patient->name ?? '',
            'patient_age' => $prescription->patient->profile->age ?? '',
            'patient_dob' => $prescription->patient->profile->dob ? date('d/m/Y', strtotime($prescription->patient->profile->dob)) : '',
            'patient_address' => $prescription->patient->profile->address ?? '',
            'notes' => $prescription->prescription ?? '',
            'doctor_gmc' => $prescription->doctor->profile->gmc_number ?? '',
            'e_sign_id' => $prescription->doctor->profile->e_sign_id ?? '',
            'e_sign_date' => $prescription->created_at->format('d M Y h:i:s A'),
            'qrUrl' => $qrUrl,
            'unique_id' => $prescription->doctor->profile->doctor_id ?? '',
        ];

        //        $pdf = PDF::loadView('pdf.prescription', $data);
        $pdf = PDF::loadView('pdf.prescription', $data)
            ->setPaper('A4', 'portrait')
            ->setWarnings(false)
            ->setOptions([
                'isPhpEnabled' => true
            ]);

        return $pdf->download('prescription-' . $orderId . '.pdf');
    }

    public function approveOrders(Request $request)
    {

        $values = $request->input('values'); // This will be an array
        $status = $request->input('action'); // This will be an array
        $duration = $request->input('duration', 30); // Default duration of 30 days if not provided

        foreach ($values as $id) {
            $order = Prescription::find($id);
            if ($order) {
                $order->admin_approval = $status;
                $order->save();

                // Handle repeat prescriptions
                if ($order->prescription_type === 'repeat' &&  $order->type == 'Type 1') { // Only process if approved
                    $repetitions = $order->repetitions;
                    $prescriptionId = $order->id;

                    // Fetch all existing payments ordered by payment_date
                    $existingPayments = $order->payments()->orderBy('payment_date')->get();

                    // If there are existing payments, delete the ones with status 'pending' and payment_date > today
                    if ($existingPayments->isNotEmpty()) {
                        $existingPayments->where('status', 'pending')
                            ->where('payment_date', '>', Carbon::today())
                            ->each(function ($payment) {
                                $payment->delete();
                            });
                    }

                    // Set the paymentDate based on whether there are existing records
                    $paymentDate = $existingPayments->isEmpty() ? Carbon::today() : Carbon::parse($order->payments->last()->payment_date)->copy()->addDays($duration);

                    // Create new records starting from the last valid payment date or today
                    for ($i = $order->payments()->count(); $i < $repetitions; $i++) {
                        $validUntil = $paymentDate->copy()->addDays(7); // valid until is 7 days after the payment date
                        PrescriptionPayment::create([
                            'prescription_id' => $prescriptionId,
                            'payment_date' => $paymentDate,
                            'valid_until' => $validUntil,
                            'status' => 'pending', // Default to pending
                        ]);
                        $paymentDate = $paymentDate->copy()->addDays($duration); // Add the duration to the payment date for next repetition
                    }
                }
            }
        }
        $message = $status === 2 ? 'All orders rejected.' : 'Orders approved.';
        return response()->json(['status' => 'success', 'message' => $message]);
    }

    public function getPatientOrders(Request $request)
    {
        $user = auth()->user();
        $query = Prescription::where('patient_id', $user->id);

        // Apply billing status filter
        if ($request->billingStatus) {
            $query->where(function ($q) use ($request) {
                if ($request->billingStatus === 'paid') {
                    $q->where(function ($q) {
                        $q->where(function ($q) {
                            $q->where('type', 'Type 1')
                                ->where('prescription_type', 'one_time')
                                ->where('status', 1);
                        })->orWhere(function ($q) {
                            $q->where('type', 'Type 1')
                                ->where('prescription_type', 'repeat')
                                ->whereHas('comingPayments', function ($q) {
                                    $q->where('status', 'paid');
                                });
                        });
                    });
                } elseif ($request->billingStatus === 'pending') {
                    $q->where(function ($q) {
                        $q->where('type', 'Type 1')
                            ->where(function ($q) {
                                $q->where('prescription_type', 'one_time')
                                    ->where('status', 0);
                            })->orWhere(function ($q) {
                                $q->where('prescription_type', 'repeat')
                                    ->where(function ($q) {
                                        $q->whereHas('comingPayments', function ($q) {
                                            $q->where('status', 'pending')
                                                ->orderBy('id', 'asc')
                                                ->limit(1);
                                        })
                                            ->orWhereDoesntHave('payments'); // Add this line
                                    });
                            });
                    });
                }
            });
        }

        // Apply order status filter
        if ($request->orderStatus) {
            $query->where('status', $request->orderStatus);
        }

        $orders = $query->orderBy('created_at', 'DESC')->paginate(5);

        $response = [
            'rows' => view('dashboard.patients.order-list', compact('orders'))->render(),
            'pagination' => view('dashboard.components.pagination', [
                'paginator' => $orders,
                'type' => 'order'
            ])->render()
        ];

        return response()->json($response);
    }
}
