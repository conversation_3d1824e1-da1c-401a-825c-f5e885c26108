@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css"> -->
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="users-main-div pb-5">
                        <h4 class="roboto heading-gary"> All Medicines</h4>
                        <!-- <span id="sortByName" style="cursor:pointer;">
                                            <i class="fa fa-sort"></i>
                                        </span> -->
                        </h4>

                        <div class="d-flex justify-content-end gap-5 flex-wrap align-items-center">
                            <div class="d-flex align-item-center justify-content-end gap-2 flex-wrap">
                                <div class="search_box position-relative">
                                    <input type="text" id="searchMedicines" value="{{ old('search', $search) }}"
                                        name="search" class="form-control search" placeholder="Search medicines...">
                                    <a class="reset-btn position-absolute" id="clearSearchBtn" href="javascript:void(0);">
                                        <i class="fa-solid fa-xmark"></i>
                                    </a>
                                </div>
                            </div>
                            @can('inventories-create')
                                <button type="button" class="button-gradient white-color roboto fs-14 fw-400"
                                    onclick="openImportModal()">Import Medicines</button>
                                <button type="button" class="button-gradient white-color roboto fs-14 fw-400"
                                    onclick="openModal()">Add
                                    Medicine</button>
                            @endcan
                        </div>
                    </div>
                    <div class="table-patients table-border-all">
                        <div class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5">
                            <h3>Medicines
                                <!-- <span class="input-text-gray">({{ $inventories->total() }})</span> -->
                            </h3>
                        </div>
                        <div class="table-responsive">
                            <table id="staffPrescritionTable" class="table display custom-table gy-5 gs-5 sortTable">
                                <thead>
                                    <tr>
                                        <!-- {{-- <th class="w-20px"><input type="checkbox" id="select-all" class="select-all "></th> --}} -->
                                        <th class="min-w-300px">Medicine</th>
                                        <th class="min-w-200px">Pip Code</th>
                                        <th class="min-w-150px">Cost Price</th>
                                        <th class="min-w-150px">Sale Price</th>
                                        <th class="min-w-150px">Pack Size</th>
                                        <!-- {{-- <th class="w-300px">Stock Quantity</th> --}} -->
                                        @if (auth()->user()->hasRole('admin'))
                                            <th class="min-w-50px first-and-last"></th>
                                        @endif
                                    </tr>
                                </thead>
                                <tbody id="inventoriesTableBody">
                                    @foreach ($inventories as $item)
                                        <tr>
                                            {{-- <td><input type="checkbox" class="row-select"></td> --}}
                                            <td>
                                                <div class="d-flex gap-2 align-items-center">
                                                    <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                        alt="" height="40px" width="40px">
                                                    <div class="d-flex flex-column">
                                                        <h5 class="fw-500 number-gray-black">
                                                            {{ $item->name ?? '' }}</h5>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $item->pip_code ?? '' }}</td>
                                            <td>{{ $item->cost_price ?? '' }}</td>
                                            <td>{{ $item->sale_price ?? '' }}</td>
                                            <td>{{ $item->pack_size ?? '' }}</td>
                                            {{-- <td>{{$item->stock_quantity??''}}</td> --}}
                                            @if (auth()->user()->hasRole('admin'))
                                                <td class="action_btn text-end">
                                                    <div class="dropdown">
                                                        <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="true">
                                                            <i class="fa-solid fa-ellipsis-vertical"></i>
                                                        </a>
                                                        <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                                                            <li><a class="dropdown-item Satoshi" onclick="openModal({{ json_encode($item) }})">View</a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            @endif
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>

                            <div class="d-flex justify-content-between align-items-center mt-5">
                                <p class="m-0 ps-5" id="paginationInfo">
                                    Showing {{ $inventories->firstItem() }} to {{ $inventories->lastItem() }} of
                                    {{ $inventories->total() }}
                                    entries
                                </p>
                                <div class="pagination" id="paginationLinks">
                                    {{ $inventories->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </section>
    @can('inventories-create')
        <!-- Import Modal -->
        <div class="modal fade" id="importModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Import Medicines</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3 d-flex gap-3 justify-content-between background-modal">
                            <p class="m-0">Pip Code</p>
                            <p class="m-0">Cost Price</p>
                            <p class="m-0">Sale Price</p>
                            <p class="m-0">Pack Size</p>
                        </div>
                        <form id="importForm" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3">
                                <label class="form-label">Upload CSV</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".csv"
                                    required>
                            </div>
                            <button type="submit" class="button-gradient white-color roboto fs-14 fw-400">Upload</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <!-- Modal Form -->
        <div class="modal fade" id="inventoryModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Manage Medicines</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="inventoryForm">
                            @csrf
                            <input type="hidden" id="id" name="id">

                            <div class="mb-3">
                                <label class="form-label">PIP Code</label>
                                <input type="text" class="form-control" id="pip_code" name="pip_code" required maxlength="255">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Name</label>
                                <input type="text" class="form-control" id="name" name="name" required maxlength="255" pattern="[A-Za-z0-9\s\-\.]+" title="Please enter a valid name (letters, numbers, spaces, hyphens and periods only)">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Pack Size</label>
                                <input type="number" class="form-control" id="pack_size" name="pack_size" required min="1" step="1" oninput="validity.valid||(value='');">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Cost Price</label>
                                <input type="number" step="0.01" class="form-control" id="cost_price" name="cost_price"
                                    required min="0" oninput="calculateSalePrice(); validity.valid||(value='');">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Sale Price</label>
                                <input type="number" step="0.01" class="form-control" id="sale_price" name="sale_price"
                                    required min="0" readonly>
                            </div>
                            <button type="submit" class="button-gradient white-color roboto fs-14 fw-400">Save</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endcan
@endsection
@push('js')
    <script>
        function openImportModal() {
            $('#importModal').modal('show');
        }

        $('#importForm').submit(function(e) {
            e.preventDefault();

            let formData = new FormData(this);

            $.ajax({
                url: "{{ route('inventories.import') }}",
                method: "POST",
                data: formData,
                contentType: false,
                processData: false,
                beforeSend: function() {
                    Swal.fire({
                        title: "Importing...",
                        text: "Please wait while your file is being processed.",
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                },
                success: function(response) {
                    Swal.fire({
                        title: "Success!",
                        text: response.message,
                        icon: "success"
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    let errorMessage = "An error occurred while importing.";
                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        errorMessage = Object.values(xhr.responseJSON.errors).join('<br>');
                    }
                    Swal.fire({
                        title: "Error!",
                        html: errorMessage,
                        icon: "error"
                    });
                }
            });
        });

        function calculateSalePrice() {
            let costPrice = parseFloat($('#cost_price').val()) || 0;
            let salePrice = (costPrice * 2).toFixed(2); // Ensure 2 decimal places

            $('#sale_price').val(salePrice);
        }

        function openModal(data = null) {
            $('#inventoryModal').modal('show');
            if (data) {
                $('#id').val(data.id);
                $('#pip_code').val(data.pip_code);
                $('#name').val(data.name);
                $('#pack_size').val(data.pack_size);
                $('#cost_price').val(data.cost_price);
                $('#sale_price').val(data.sale_price);
            } else {
                $('#inventoryForm')[0].reset();
                $('#id').val('');
            }
        }

        $('#inventoryForm').submit(function(e) {
            e.preventDefault();
            $.ajax({
                url: "{{ route('inventories.store') }}",
                method: "POST",
                data: $(this).serialize(),
                success: function(response) {
                    Swal.fire({
                        title: "Success!",
                        text: response.message,
                        icon: "success"
                    }).then(() => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    let errors = xhr.responseJSON.errors;
                    let errorMessage = "";
                    $.each(errors, function(key, value) {
                        errorMessage += value + "<br>";
                    });

                    Swal.fire({
                        title: "Error!",
                        html: errorMessage,
                        icon: "error"
                    });
                }
            });
        });

        // AJAX Search and Pagination functionality
        $(document).ready(function() {
            let typingTimer;
            let doneTypingInterval = 500;

            // Handle search input with debounce
            $('.search').on('input', function() {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    handleSearch();
                }, doneTypingInterval);
            });

            // Handle search clear button (X) click
            $(document).on('search', '.search', function() {
                handleSearch();
            });

            // Handle clear button (X) click
            $(document).on('click', '#clearSearchBtn', function(e) {
                e.preventDefault();
                $('.search').val(''); // Clear the search input
                handleSearch(); // Trigger search with empty value
            });

            // Handle pagination clicks
            $(document).on('click', '.pagination a', function(e) {
                e.preventDefault();
                var page = $(this).attr('href').split('page=')[1];
                handleSearch(page);
            });

            function handleSearch(page = 1) {
                var search = $('.search').val();

                $.ajax({
                    url: "{{ route('inventories.search') }}",
                    method: "POST",
                    data: {
                        search: search,
                        page: page,
                        _token: "{{ csrf_token() }}",
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update table body
                            $('#inventoriesTableBody').html(response.html);

                            // Update pagination
                            $('#paginationLinks').html(response.pagination);

                            // Update pagination info
                            $('#paginationInfo').html(response.pagination_info);

                            // Re-attach pagination click events
                            $('#paginationLinks a').on('click', function(e) {
                                e.preventDefault();
                                var page = $(this).attr('href').split('page=')[1];
                                handleSearch(page);
                            });
                        }
                    },
                    error: function(xhr) {
                        console.error("AJAX Error: ", xhr.responseText);
                    }
                });
            }
        });
    </script>
@endpush
