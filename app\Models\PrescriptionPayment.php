<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;


class PrescriptionPayment extends Model
{
    use HasFactory;
    protected $fillable = [
        'order_id',
        'prescription_id',
        'payment_date',
        'valid_until',
        'status',
        'total',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prescription) {
            $prescription->order_id = self::generateUniqueOrderId();
        });
    }

    public static function generateUniqueOrderId()
    {
        do {
            $orderId = strtoupper(Str::random(3)) . rand(100, 999) . strtoupper(Str::random(2));
        } while (self::where('order_id', $orderId)->exists());

        return $orderId;
    }

    public function prescription()
    {
        return $this->belongsTo(Prescription::class);
    }
}
