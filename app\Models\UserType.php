<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserType extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the user type.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if the user can change their payment type (3 months restriction)
     */
    public static function canChangeType($userId)
    {
        $latestTypeChange = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$latestTypeChange) {
            return true; // No previous type change, allow change
        }

        $monthsSinceLastChange = $latestTypeChange->created_at->diffInMonths(now());
        return $monthsSinceLastChange >= 3;
    }

    /**
     * Get the last change date for a user
     */
    public static function getLastChangeDate($userId)
    {
        $latestTypeChange = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $latestTypeChange ? $latestTypeChange->created_at : null;
    }

    /**
     * Create a new type change record
     */
    public static function createTypeChange($userId, $type)
    {
        return self::create([
            'user_id' => $userId,
            'type' => $type,
        ]);
    }

    /**
     * Get the latest type change date for a user
     */
    public static function getLatestChangeDate($userId)
    {
        $latestTypeChange = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $latestTypeChange ? $latestTypeChange->created_at : null;
    }

    /**
     * Get the current type for a user
     */
    public static function getCurrentType($userId)
    {
        $latestTypeChange = self::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        return $latestTypeChange ? $latestTypeChange->type : null;
    }
}
