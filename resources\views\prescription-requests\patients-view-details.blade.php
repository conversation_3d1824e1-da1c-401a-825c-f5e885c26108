@extends('theme.layout.master')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-9">
                    <div class="table-patients table-border-all">
                        <div class="custom-dropdown px-5 roboto d-flex justify-content-between align-items-center pb-5">
                            <h3>Patients<span class="input-text-gray"></span></h3>
{{--                            @if (auth()->user()->hasRole('doctor') || auth()->user()->hasRole('staff'))--}}
{{--                                <div>--}}
{{--                                    <button type="button" class="medicines-edit-btn roboto modal-save"--}}
{{--                                            id="edit-prescription" data-id="{{ $prescription->order_id }}">Edit--}}
{{--                                    </button>--}}
{{--                                </div>--}}
{{--                            @endif--}}

                        </div>
                        <div class="table-responsive">
                            <table id="staffPrescritionTable" class="table display custom-table gy-5 gs-5">
                                <thead>
                                <tr>
                                    <th class="w-200px">Medicine</th>
                                    <th class="w-200px">Instruction to patients</th>
                                    {{-- <th class="w-200px">Type</th> --}}
                                    <th class="w-200px">Qty</th>
                                    <th class="w-200px">Price</th>

                                </tr>
                                </thead>
                                <tbody class="ps-5">
{{--                                @foreach ($prescription->inventories as $itemlist)--}}
{{--                                    <tr>--}}
{{--                                        <td class="w-200px">{{ $itemlist->name ?? '' }}</td>--}}
{{--                                        <td class="w-200px">{{ $itemlist->pivot->dosage ?? '' }}</td>--}}
{{--                                        --}}{{-- <td class="w-200px">Tablets</td> --}}
{{--                                        <td class="w-200px">{{ $itemlist->pivot->prescribed_units ?? '' }}</td>--}}
{{--                                        <td class="w-200px">£{{ $itemlist->pivot->total_price ?? '' }}</td>--}}

{{--                                    </tr>--}}
{{--                                @endforeach--}}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-4">
                    <div class="card shadow-sm border-0 rounded-4 h-100">
                        <div class="card-body p-0">

                            <!-- Header -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="d-flex align-items-center py-3 ">
                                    @include('svg.prescriber-name')
                                    <span class="inter fs-12 fw-400 ps-2 deep-charcoal-blue">{{ $patient->name }}</span>
                                </div>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $prescription->prescribedBy->name ?? '' }}</h6>

                            <!-- Billing Type -->
                            <div class="d-flex justify-content-between">
                                <p class="fs-12 fw-500 table-gary-text mt-3">No. of prescriptions</p>
                                <p class="fs-12 fw-500 table-gary-text mt-3">{{ $prescription->type ?? '' }}</p>
                            </div>

                            <!-- Patient -->
                            {{-- <div class="d-flex align-items-center py-3">
                                @include('svg.patient-name')
                                <span class="roboto fs-12 fw-400 ps-2 deep-charcoal-blue">Patient Name</span>
                            </div>
                            <h6 class="fs-18 fw-500 text-border">{{ $prescription->patient->name ?? '' }}</h6> --}}

                            <!-- Notes -->
                            {{-- <p class="fs-12 fw-500 table-gary-text">Prescription Notes</p>
                            <p class="fs-13 fw-400 number-gray-black">
                                {{ $prescription->prescription ?? '' }}
                            </p> --}}


                            <!-- Prescription Type -->
                            {{-- <p class="fs-12 fw-500 table-gary-text">Prescription Type</p>
                            <p class="fs-14 fw-500 number-gray-black">
                                {{ strtoupper(str_replace('_', ' ', $prescription->prescription_type ?? '')) }}</p> --}}
                            {{-- <select class="form-select fs-14 fw-500 number-gray-black rounded-3" name="prescription_type"
                                id="prescriptionType">
                                <option value="one-time">One-Time</option>
                                <option value="recurring">Recurring</option>
                            </select> --}}
{{--                            @if ($prescription->prescription_type == 'repeat')--}}
{{--                                <p class="fs-12 fw-500 table-gary-text">Prescription Duration</p>--}}
{{--                                <p class="fs-14 fw-500 number-gray-black">{{ $prescription->repetitions ?? '' }} month</p>--}}
{{--                                <!-- Recurring Section (Hidden by Default) -->--}}
{{--                                @if (auth()->user()->hasRole('admin'))--}}
{{--                                    <div id="recurringSection" class="mt-3">--}}
{{--                                        <!-- Assign Schedule Button -->--}}
{{--                                        <button class="btn  assign-btn" id="assignScheduleBtn">Assign Schedule in--}}
{{--                                            Days</button>--}}
{{--                                        <!-- Schedule Form (Hidden Initially) -->--}}
{{--                                        <form id="scheduleForm"--}}
{{--                                              action="{{ route('update.schedule', $prescription->order_id) }}"--}}
{{--                                              class="mt-3 d-none">--}}
{{--                                            @csrf--}}
{{--                                            @method('PUT')--}}
{{--                                            <!-- Days & Frequency -->--}}
{{--                                            <label class="fs-12 fw-500 table-gary-text">Add Schedule</label>--}}

{{--                                            <div class="d-flex align-items-center mb-3">--}}
{{--                                                <!-- Counter Input -->--}}
{{--                                                <input type="text" id="daysCount"--}}
{{--                                                       value="{{ $prescription->duration ?? 0 }}" name="duration"--}}
{{--                                                       placeholder="Add days frequency" readonly class="form-control me-2" />--}}

{{--                                                <!-- + / - Black Icon Buttons -->--}}
{{--                                                <button type="button" id="decreaseDays"--}}
{{--                                                        class="btn btn-sm me-1 d-flex align-items-center justify-content-center"--}}
{{--                                                        style="width: 32px;height: 32px;font-size: 25px;">--}}
{{--                                                    −--}}
{{--                                                </button>--}}

{{--                                                <button type="button" id="increaseDays"--}}
{{--                                                        class="btn btn-sm d-flex align-items-center justify-content-center"--}}
{{--                                                        style="width: 32px;height: 32px;font-size: 25px;">--}}
{{--                                                    +--}}
{{--                                                </button>--}}
{{--                                            </div>--}}
{{--                                            <!-- Save / Cancel Buttons -->--}}
{{--                                            <div class="d-flex justify-content-center mt-3">--}}
{{--                                                <button type="button" class="btn assign-btn btn-sm me-2">Cancel</button>--}}
{{--                                                <button type="button" class="btn assign-btn btn-sm"--}}
{{--                                                        id="schedule_form">Save</button>--}}
{{--                                            </div>--}}

{{--                                        </form>--}}
{{--                                    </div>--}}
{{--                                @endif--}}
{{--                            @endif--}}


                            <!-- Billed -->
                            {{-- <p class="fs-12 fw-500 table-gary-text pt-5">Billed Amount</p>
                            <div class="d-flex align-items-baseline justify-content-between">
{{--                                <span class="fs-14 fw-400 number-gray-black">{{ $prescription->total }}</span>-
                                <span class="fs-5 fw-semibold me-1">£</span>
                            </div> --}}

{{--                            @if (--}}
{{--                                $prescription->status == 0 &&--}}
{{--                                    ((auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Doctor') ||--}}
{{--                                        auth()->user()->hasRole('admin')))--}}
{{--                                <div class="d-flex mt-4 gap-5 flex-wrap">--}}
{{--                                    <button--}}
{{--                                            class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save toggle-status"--}}
{{--                                            data-status="Approve" data-slug="{{ $prescription->order_id }}">Approve</button>--}}
{{--                                    <button--}}
{{--                                            class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400 toggle-status"--}}
{{--                                            data-status="Reject" data-slug="{{ $prescription->order_id }}">Reject</button>--}}
{{--                                </div>--}}
{{--                            @endif--}}
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="modal fade" id="rejectionNoteModal" tabindex="-1" role="dialog" aria-labelledby="rejectionNoteModalLabel"
         aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Reject Prescription</h5>
                </div>
                <div class="modal-body">
                    <textarea id="rejection-note" class="form-control" placeholder="Enter rejection reason..."></textarea>
                    <input type="hidden" id="reject-prescription-id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="modal_grey_reject_btn deep-forest-green roboto fs-14 fw-400"
                            data-bs-dismiss="modal">Cancel</button>
                    <button type="button" id="confirm-reject"
                            class="gradient_modal_approve white-color roboto fs-14 fw-400">Reject</button>
                </div>
            </div>
        </div>
    </div>
    </section>
@endsection