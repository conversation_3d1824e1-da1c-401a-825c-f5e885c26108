<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <style>
        body {font-family: Arial, sans-serif;margin: 20px;overflow-x: hidden;page-break-inside: avoid; padding: 0}
        h2 {text-align: center;}
        p {font-size: 16px;}
        .head-sec { text-align: center; }
        .token-id {text-align: right;}
        .info {overflow: hidden;margin-bottom: 10px;margin-left: 25px;}
        .info img {float: left;width: 100px;margin: 48px;
        ;}
        .info-text { overflow: hidden;}
        .paitent-info { margin-left: 12.5rem; }
        .section {margin-top: 40px;padding-top: 20px;margin: 32px;}
        table { width: 100%; border-collapse: collapse;margin-top: 10px;}
        th,td {  border: 2px solid transparent; padding: 0; text-align: left; font-size: 20px;  }
        .signed {margin-top: 10px;text-align: right;}

        .footer {overflow: hidden;margin-bottom: 10px;margin-left: 10px;border-top: 1px dashed #ccc;padding-top: 50px; position: relative;}
        .footer img {float: left;height: 60px; margin: 1rem;padding-right: 20px;padding-top: 10px; padding-bottom: 10px;}
        .footer .logo-img {border-right: 2px solid black;}
        .footer .info-text {float: left; max-width: 60%;}
        .footer .info-text p{font-size: 20px;}
        .footer .qr {float: right; margin-top: 1rem;}
        .footer .qr img {width: 80px;  height: auto;}
        .footer-text {text-align: left;line-height: 1.5; page-break-inside: avoid;}
        body .clinic-dr th,.clinic-dr td{ border: 2px solid #000; padding: 8px; text-align: left; font-size: 16px;  }

    </style>
</head>

<body>
    <div class="token-id">
        <p><strong>Admin ID:</strong><span>{{ $admin_id }}</span></p>
        <p><strong>Token ID: </strong><span>{{ $token_id }}</span></p>
    </div>
    <div class="head-sec">
        <h2>PRESCRIPTION DETAILS</h2>
        <p>DISPENSING TOKEN for an electronic prescription - To be printed for record keeping purposes only.</p>
    </div>
{{--    <div class="info">--}}
{{--        <img src="{{ public_path('website/assets/media/images/rx-logo.png') }}" alt="img">--}}
{{--        <img src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('website/assets/media/images/rx-logo.png'))) }}" alt="img">--}}
{{--        <div class="info-text">--}}
{{--            <p><strong>{{ $doctor_name }}</strong></p>--}}
{{--            <p><strong>Chamber -</strong> {{ $chamber_name }}</p>--}}
{{--            <p><strong>Chamber Address:</strong> {{ $chamber_address }}</p>--}}
{{--        </div>--}}
{{--    </div>--}}
    <table style="width:100%" >
        <tr>
            <td style="width: 120px;"><img src="https://rx-direct.democustomprojects.com/AdminDashboard/PaBmowv9LYRnjuru7jJvxYXdFBmaSLgwkhIcA1BK.png" alt="img" style="width: 60px;"></td>
            <td>
            <p><strong>{{ $doctor_name }}</strong></p>
            <p><strong>Chamber -</strong> {{ $chamber_name }}</p>
            </td>
        </tr>
    </table>
    <table style="width:100%" >
        <tr>
            <td style="width: 120px;">
            <td>
                <p style="text-decoration: underline;"><strong>Patient:</strong> {{ $patient_name }}, {{ $patient_age }} years old ({{ $patient_dob }})</p>
            <p><strong>Address:</strong> {{ $patient_address }}</p>
            </td>
        </tr>
    </table>


    <div class="info">
        <p><strong>Notes:</strong></p>
        <p>{{ $notes }}</p>
    </div>

    <div class="section">
        <p><strong>MEDICINES:</strong></p>
        <table class="clinic-dr">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Dosage Instruction</th>
                    <th>Quantity</th>
                </tr>
            </thead>
            <tbody>
                @foreach($medicines as $medicine)
                <tr>
                    <td>{{ $medicine->name }}</td>
                    <td>{{ $medicine->pivot->dosage }}</td>
                    <td>{{ $medicine->pivot->prescribed_units }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    <div class="signed">
        <p style="text-decoration: underline;"><strong>Signed:</strong></p>
        <p>{{ $doctor_name }} (GMC: {{ $doctor_gmc }})</p>
    </div>

    <table width="100%" style="margin-top: 10px; border-top: 1px dashed #ccc;">
        <tr>
            <td style="vertical-align: top; width: 100px; border-right: 2px solid #000;">
                <img src="{{ public_path('website/assets/media/images/rx-logo.png') }}" style="height: 80px;" alt="E-sign">
            </td>
            <td style="vertical-align: top; padding-left: 15px;">
                <p style="font-size: 16px;"><strong>{{ $doctor_name }}</strong></p>
                <a href="{{ route('prescriber_profile', ['id' => $doctor->id]) }}" style="font-size: 16px;">E-Sign ID: {{ $unique_id }}</a>
            </td>
            {{-- <td style="vertical-align: top; text-align: right;">
                <img src="{{ public_path('website/assets/media/images/qr-code-img.png') }}" style="width: 80px;" alt="QR Code">
            </td> --}}
            <td style="vertical-align: top; text-align: right;">
                {{-- <img src="data:image/png;base64,{{ $qrCode }}" style="width: 80px;" alt="QR Code"> --}}
                <img src="data:image/png;base64, {!! base64_encode(QrCode::size(100)->generate($qrUrl)) !!} ">



            </td>
        </tr>
    </table>


    <div class="footer-text">
        <p>For e-signature verification or queries Contact: <EMAIL> or +44 (0) 330 113 7894</p>
    </div>
</body>

</html>
