<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Log;

class NotDisposableEmail implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure  $fail
     * @return void
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $blockedDomains = [
            'yopmail.com',
            'mailinator.com',
            'tempmail.com',
            'temp-mail.org',
            'guerrillamail.com',
            'sharklasers.com',
            'guerrillamail.net',
            'guerrillamail.org',
            'guerrillamail.de',
            'guerrillamail.info',
            'guerrillamail.biz',
            'guerrillamail.com',
            'spam4.me',
            'trashmail.com',
            'trashmail.net',
            'trashmail.me',
            'dispostable.com',
            'maildrop.cc',
            'mailnesia.com',
            'mailcatch.com',
            'tempr.email',
            'temp-mail.io',
            'temp-mail.ru',
            '10minutemail.com',
            '10minutemail.net',
            '10minutemail.org',
            '10minutemail.de',
            '10minutemail.info',
            '10minutemail.biz',
            '10minutemail.com',
            'mailinator2.com',
            'mailinator3.com',
            'mailinator4.com',
            'mailinator5.com',
            'mailinator6.com',
            'mailinator7.com',
            'mailinator8.com',
            'mailinator9.com',
            'mailinator10.com',
            'mailinator11.com',
            'mailinator12.com',
            'mailinator13.com',
            'mailinator14.com',
            'mailinator15.com',
            'mailinator16.com',
            'mailinator17.com',
            'mailinator18.com',
            'mailinator19.com',
            'mailinator20.com'
        ];

        // Log the incoming email for debugging
        Log::info('Validating email: ' . $value);

        $domain = substr(strrchr($value, "@"), 1);
        Log::info('Extracted domain: ' . $domain);

        if (in_array(strtolower($domain), array_map('strtolower', $blockedDomains))) {
            Log::info('Blocked domain detected: ' . $domain);
            $fail('Disposable email addresses are not allowed. Please use a valid email address.');
        } else {
            Log::info('Domain allowed: ' . $domain);
        }
    }
}
