<?php $__env->startSection('content'); ?>
<div class="d-flex flex-column flex-column-fluid flex-lg-row register_page">
    <div class="d-flex flex-center w-lg-50 px-10">
        <div class="w-100 px-10">
            <div class="mb-20">
                <a href="<?php echo e(asset('/')); ?>" class="mb-7">
                    <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo??''); ?>"/>
                </a>
            </div>

            <div class="new-staff-modal login-header pb-0 w-100">
                <div class="register_page_header">
                    <h4 class="modal-title white-color inter w-100 text-center">Clinic Admin Login</h4>
                </div>

                <div class="row py-10 mx-5">
                    <div class="col-lg-12">
                        <form class="form w-100" novalidate="novalidate" method="POST" action="<?php echo e(route('clinic.login.submit')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="patients-email fv-row mb-8">
                                <h5 class="deep-charcoal-blue inter">Email <span style="color:var(input-text-gray);">*</span></h5>
                                <input id="email" type="email" placeholder="Email"
                                    class="form-control bg-transparent px-0 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                    name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus>

                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="invalid-feedback" role="alert">
                                    <strong><?php echo e($message); ?></strong>
                                </span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <span class="invalid-feedback" role="alert">
                                    <strong><?php echo e($message); ?></strong>
                                </span>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="position-relative mb-3">
                                <div class="pas">
                                    <label for="password">Password <span style="color:var(input-text-gray)">*</span></label>
                                    <input id="password" type="password" placeholder="Password" maxlength="20"
                                        class="form-control bg-transparent px-0 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                        name="password" required autocomplete="current-password">
                                    <span id="toggle-password" class="btn-sm btn-icon position-absolute translate-middle top-50 end-0 right-10 pt-9">
                                        <i class="fa-solid fa-eye"></i>
                                        <i class="fa-solid fa-eye-slash d-none"></i>
                                    </span>
                                    <?php if(!session('error')): ?>
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="invalid-feedback" role="alert">
                                            <strong><?php echo e($message); ?></strong>
                                        </span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mb-8">
                                <div></div>
                                <a href="<?php echo e(route('password.request')); ?>" class="gray-text">Forgot Password ?</a>
                            </div>

                            <div class="d-grid mb-10">
                                <button type="submit" id="kt_sign_in_submit" class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save w-md-50 m-auto">
                                    <span class="indicator-label">Sign In</span>
                                    <span class="indicator-progress">Please wait...
                                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                    </span>
                                </button>
                            </div>

                            <div class="text-gray-500 text-center fw-semibold fs-18">
                                Not a Member yet?
                                <a href="<?php echo e(route('registration-instruction')); ?>" class="uploadLabel">Sign up</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex w-md-50 flex-column-fluid flex-lg-row-auto flex-column align-items-center register-bg-img justify-content-center">
        <div>
            <img class="login_image w-100 h-100 img_fluid" src="<?php echo e(asset('website')); ?>/assets/media/images/banner-img.png" alt="image" />
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    document.getElementById('toggle-password').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const passwordType = passwordField.type;

        if (passwordType === 'password') {
            passwordField.type = 'text';
            this.querySelector('.fa-eye-slash').classList.remove('d-none');
            this.querySelector('.fa-eye').classList.add('d-none');
        } else {
            passwordField.type = 'password';
            this.querySelector('.fa-eye').classList.remove('d-none');
            this.querySelector('.fa-eye-slash').classList.add('d-none');
        }
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git\rx-direct\resources\views/auth/clinic/login.blade.php ENDPATH**/ ?>