* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Istok+Web:ital,wght@0,400;0,700;1,400;1,700&family=Prompt:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');



:root {
    --white: #fff;
    --black: #000;
    --text-white: #F8FBFA;
    --light-white: #F6F6F6;
    --bg-white: #F4F9F9;
    --black: #000;
    --dark-black: #323232;
    --gary-black: #404040;
    --text-black: #3D3D3D;
    --light-black: #464646;
    --charcoal: #4A4A4A;
    --light-green: #24A562;
    --blue: #2B73B7;
    --light-blue: #78B8FA;
    --background-color: #F4F9F9;
    --bg-gary: #7E7E7E;
    --input-gary: #A7A7A7;
    --form-bg-gary: #CBCBCB;
    --para-gary: #3C3C3C;
    --para-border: #4E4E4E;
    --bg-gradient: linear-gradient(90deg, #24A562 0%, #2A76B8 100%);
    --timeline-green: linear-gradient(90deg, #2B75BB 13.51%, rgba(108, 85, 255, 0.00) 89.18%, rgba(99, 101, 255, 0.00) 89.18%);
    --timeline-blue: linear-gradient(90deg, #24A562 13.51%, rgba(108, 85, 255, 0.00) 89.18%, rgba(99, 101, 255, 0.00) 89.18%);
    /*dashboard*/


    --border-gray-label: #E5E7EB;
    --icon-green: #45966C;
    --sidebar-green: #45976C;
    --heading-gary: #484B49;
    --input-text-gray: #8D928E;
    --number-gray-black: #292B29;
    --brown: #A56224;
    --user-black: #0E0E0E;
    --purple: #8C3EE4;
    --heading-black: #101110;
    --text-gray: #696D6A;
    --active-green: #2fcf7c;
    --dark-green: #197C48;
    --panding-gary: #D9E0DB;
    --light-purple: #E2D8FA;
    --dark-purple: #6224A5;
    --scroll-purple: #C2A8F4;
    --table-gary: #21242a;
    --modal-green: #30D07D;
    --radio-purple: #A677EE;
    --table-black: #0E1724;
    --wood-black: #454545;
    --button-bg: radial-gradient(158.94% 127.69% at 23.3% 19.94%, #E6EFF2 8.45%, #E2D8FA 83.33%);
    --woodsmoke: #E7E7E7;
    --border-grey: #E6EFF2;
    --deep-forest-green: #05311A;
    --button-gradient: linear-gradient(90deg, #24A562 0%, #2A76B8 100%);
    --gray-green: #B2B8B3;
    --light-frost: #E5E9EB;
    --bg-body: radial-gradient(115.99% 85.22% at 26.48% 46.38%, rgba(217, 224, 219, 0.24) 0%, rgba(226, 216, 250, 0.05) 100%);
    --Blue-Dianne: #C9FFF8;
    --Silver-gray: #C1C1C1;
    --soft-silver: #E2E2E2;
    --green: #004722;
    --red: #EF4444;
    --dark-jade-green: #0E5530;
    --lavender-blush: #FBF9FF;
    --very-light-gray: #ddd;
    --gray-text: #767676;
    --eye-icon: #9a9999;
    --order-gray: #4F4F4F;
    --billing-head-gray: #344054;
    --whisper-gray: #EDEDED;
    --gradient-button: linear-gradient(90deg, var(--primary-200, #30D07D) 45.64%, var(--secondary-300, #A677EE) 130.63%);

}

/*BORDER*/
.btop-blue {
    border: 1px solid var(--Blue-Dianne);
}
.border_bottom{border-bottom: 1px solid var(--border-grey);}

/* Scrollbar */
/* .app-container::-webkit-scrollbar {  width: 10px; background-color: transparent;  }
.app-container::-webkit-scrollbar-track {  background-color: transparent;  }
.app-container::-webkit-scrollbar-thumb {  background:#C2A8F4;  border-radius: 20px;  } 
.app-container::-webkit-scrollbar-thumb:hover {  background-color: #C2A8F4;  }
.scrollbar::-webkit-scrollbar { width: 16px;  }
.scrollbar {overflow-y: scroll;max-height: 700px;border-radius: 8px;}  */







/*buttons*/

.modal_grey_btn {
    border: 1px solid var(--woodsmoke);
    padding: 8px 18px;
    border-radius: 12px;
    border: 1px solid var(--border-grey);
    background: transparent;
}

.button-gradient {
    background: var(--button-gradient);
    padding: 10px 19px;
    border: 0;
    border-radius: 12px;
}

.certificate-btn {
    border-radius: 5px;
    border: 1px solid var(--light-frost);
    background: var(--light-purple);
    padding: 10px 15px;
}

.modal_grey_reject_btn {
    border: 1px solid var(--woodsmoke);
    padding: 8px 25px;
    border-radius: 12px;
    border: 1px solid var(--border-grey);
    background: transparent;
}

.gradient_modal_approve {
    background: var(--button-gradient);
    padding: 10px 50px;
    border: 0;
    border-radius: 12px;
}

.white-btn {
    border-radius: 5px;
    border: 1px solid var(--white);
    background: var(--white);
    padding: 10px 30px;
}

.green-btn {
    border-radius: 5px;
    border: 1px solid var(--white);
    background: var(--light-green);
    padding: 10px 30px;
}

.gradient-button {
    background: linear-gradient(90deg, var(--primary-200, #30D07D) 45.64%, var(--secondary-300, #A677EE) 130.63%);
}

i.bi.bi-chevron-right {
    color: var(--black);
}

/*  common color classes */
.table-gary-text {
    color: var(--table-gary);
}

.gray-text {
    color: var(--gray-text);
}

.white-color {
    color: var(--white);
}

.text-white {
    color: var(--text-white);
}

.light-white {
    color: var(--light-white);
}

.black-color {
    color: var(--black);
}

.dark-black {
    color: var(--dark-black);
}

.light-black {
    color: var(--light-black);
}

.text-black {
    color: var(--text-black);
}

.light-green {
    color: var(--light-green)
}

.charcol-text {
    color: var(--charcoal);
}

.light-blue {
    color: var(--light-blue);
}

.blue {
    color: var(--blue);
}

.bg-gary {
    color: var(--bg-gary);
}

.gary-black {
    color: var(--gary-black);
}

.input-gary {
    color: var(--input-gary);
}

.form-bg-gary {
    color: var(--form-bg-gary);
}

.para-gary {
    color: var(--para-gary);
}

.para-border {
    color: var(--para-border);
}

.deep-charcoal-blue {
    color: var(--table-black);
}

.deep-forest-green {
    color: var(--deep-forest-green);
}

.panding-gary {
    color: var(--panding-gary);
}

.gray-green {
    color: var(--gray-green);
}

.bg-body {
    background-color: var(--bg-body)
}

.dark-purple-text {
    color: var(--dark-purple);
}

.silver-gray {
    color: var(--Silver-gray);
}

.clinic_index .light-green-bg {
    background: var(--button-gradient);
}

.red {
    color: var(--red);
}

.dark-jade-green {
    color: var(--dark-jade-green);
}

.input-text-gray {
    color: var(--input-text-gray);
}

.text-gray {
    color: var(--text-gray);
}

.table-gray {
    color: var(--table-gary);
}



/*dashboard*/
.icon-green {
    color: var(--icon-green);
}

.sidebar-green {
    color: var(--sidebar-green);
}

.heading-gary {
    color: var(--heading-gary);
}

.input-text-gray {
    color: var(--input-text-gray);
}

.number-gray-black {
    color: var(--number-gray-black);
}

.brown {
    color: var(--brown);
}

.user-black {
    color: var(--user-black);
}

.purple {
    color: var(--purple);
}

.heading-black {
    color: var(--heading-black);
}


.order-text {
    color: var(--order-gray);
}

.billing-text {
    color: var(--billing-head-gray);
}

/*family*/
.inter {
    font-family: 'Inter', sans-serif;
}

.roboto {
    font-family: 'Roboto', sans-serif;
}

/* Font Sizes */
h1,
h2,
h3,
h4,
h5,
h6 {
    line-height: 1.2;
    word-break: break-word;
}

p,
a,
span {
    line-height: 1.5;
    word-break: break-word;
}

h1 {
    font-size: 21px;
    font-weight: 700;
}

h2 {
    font-size: 20px;
    font-weight: 500;
}

h3 {
    font-size: 18px;
    font-weight: 500;
}

h4 {
    font-size: 16px;
    font-weight: 500;
}

h5 {
    font-size: 14px;
    font-weight: 400;
}

h6 {
    font-size: 12px;
    font-weight: 400;
}

p {
    font-size: 16px;
    font-weight: 400;
}

/*  Extra Font sizes  */
.fs-24 {
    font-size: 24px;
}

.fs-42 {
    font-size: 42px;
}

.fs-20 {
    font-size: 20px;
}

.fs-18 {
    font-size: 18px;
}

.fs-16 {
    font-size: 16px;
}

.fs-14 {
    font-size: 14px;
}

.fs-13 {
    font-size: 13px;
}

.fs-21 {
    font-size: 21px;
}

.fs-12 {
    font-size: 12px;
}


/*  Font weight  */
.fw-700 {
    font-weight: 700;
}

.fw-600 {
    font-weight: 600;
}

.fw-500 {
    font-weight: 500;
}

.fw-400 {
    font-weight: 400;
}



/*  Global Setting  */
a {
    text-decoration: none;
}

li {
    list-style: none;
}

/*background-image*/
.bg-img {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

/*  Global Classes  */
.p_top {
    padding-top: 7em;
}

.no_border {
    border: 0;
}

.p_bottom {
    padding-bottom: 7em;
}

.p_inline {
    padding-inline: 5em;
}

.p_block {
    padding-block: 5rem;
}

/*img{height: 100%;width: 100%;object-fit: contain;}*/
a {
    text-decoration: none;
}

.img-fluid {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.img {
    width: 100%;
    height: 100%;
}

.container {
    max-width: 1440px;
    width: 94%;
}

/*---------------------------Admin-Dashboard---------------------------*/
/*Index-Card*/
.card h1 {
    margin: 10px 0 0;
}

.overview-sec {
    padding: 20px 0;
}

.percentage i.bi.bi-arrow-down-short {
    color: #EF4444;
    font-size: 17px;
}

.percentage.light-green i.bi.bi-arrow-up-short {
    color: var(--light-green);
    font-size: 17px;
}

.card-container {
    display: flex;
    gap: 20px;
}

#kt_app_content {
    background-color: #f8f8f8;
}

.card-head span {
    letter-spacing: 1px;
    text-transform: uppercase;
}

.card-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card .percentage {
    padding: 5px 10px;
    position: absolute;
    right: 0;
    bottom: 8px;
}

.card {
    background: var(--white);
    border-radius: 10px;
    padding: 15px 13px;
    width: 100%;
    position: relative;
    gap: 15px;
}

.clinic_index .fa-long-arrow-alt-down {
    color: var(--red);
}

/*User-Tabs-And-Tables*/
div#kt_app_content_container {
    padding: 30px 0;
}

.tabs-sec {
    background: var(--bg-body);
    padding: 15px 0;
}

.tabs-sec .nav-line-tabs .nav-item {
    color: var(--input-text-gray);
}

.tabs-sec .nav-line-tabs .nav-item:hover .nav-link:hover {
    border-bottom: 1px solid var(--radio-purple);
}

.tabs-sec .nav-line-tabs .nav-item .nav-link.active {
    color: var(--dark-purple);
    border-bottom: 1px solid var(--radio-purple);
}

/*.tabs-sec .tab-content {padding: 20px;border: 1px solid var(--border-grey);background: var(--white);border-radius: 8px;}*/

.tab-content .custom_tabs,
.doctor-table {
    padding: 20px;
    background: var(--white);
    border-radius: 8px;
}

tabs-sec .tab-content .custom-dropdown h4 {
    margin: 0;
}

tabs-sec .tab-content .custom-dropdown {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-dropdown #statusFilter,
.custom-dropdown #monthFilter {
    padding: 0 !important;
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: var(--white);
}

.custom-dropdown #statusFilter:focus,
.custom-dropdown #monthFilter:focus {
    border: 1px solid var(--border-grey);
}

.custom-dropdown #statusFilter:focus-visible,
.custom-dropdown #monthFilter:focus-visible {
    outline-color: var(--border-grey);
    outline-width: 1px solid var(--border-grey);
}

body #kt_app_content_container table thead th{
    padding: 10px;
    border-bottom: 0;
    background-color: var(--white);
    color: var(--table-gary);
    font-family: Inter;
    font-size: 17px;
    font-weight: 600;
}
body #kt_app_content_container table thead td {
    padding: 10px;
    border-bottom: 0;
    background-color: var(--white);
    color: var(--table-gary);
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
}
.background-modal {
    background: var(--sidebar-green);
    color: white;
    padding: 10px;
    border-radius: 10px;
}

.badge-active {
    color: var(--number-gray-black);
    padding: 0.8em 30px;
    border-radius: 40px;
    background-color: var(--active-green);
}
.badge-active-small {
    color: var(--number-gray-black);
    padding: 3px 19px;
    border-radius: 12px;
    background-color: var(--active-green);
}
.badge-inactive-small {
    color: var(--number-gray-black);
     padding: 3px 19px;
    border-radius: 40px;
    background-color: var(--panding-gary);
}
.badge-inactive {
    color: var(--number-gray-black);
    padding: 0.8em 30px;
    border-radius: 40px;
    background-color: var(--panding-gary);
}

.inactive-grey-badge {
    padding: 0px 8px;
    border-radius: 40px;
   background-color: var(--panding-gary);
    font-size: 12px;
    width: 70px;
    text-align: center;
    color: var(--number-gray-black);
}

.badge-passed {
    color: var(--black);
    padding: 0.8em 30px;
    border-radius: 40px;
    background-color: var(--input-text-gray);
}

.badge-paid {
    border-radius: 12px;
    color: var(--black);
    border: 1px solid var(--border-grey);
    padding: 10px;
}

.badge-gradient {
    border-radius: 12px;
    border: 1px solid var(--border-grey);
    background: linear-gradient(90deg, #24A562 0%, #2A76B8 100%);
    color: var(--white);
    font-size: 16px;
    padding: 10px;
}

.assigned-badge {
    width: fit-content;
    color: var(--number-gray-black);
    padding: 4px 9px;
    border-radius: 40px;
    background-color: var(--panding-gary);
}

table.dataTable.no-footer {
    border: 0;
}

div.dataTables_wrapper div.dataTables_paginate {
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 20px;
}

.table tbody tr td {
    vertical-align: middle;
}

.table-background {
    border-radius: 20px;
    border: 1.5px solid var(--border-grey) !important;
    background: white;
    padding: 10px 2px;
}

.table:not(.table-bordered) thead tr th,
.table:not(.table-bordered) tbody tr td {
    color: var(--table-gary);
}

.table-border-all {
    border-radius: 12px;
    border: 1.5px solid var(--border-grey) !important;
    background: white;
    padding: 9px 10px;
}

.dark-green-badge {
    background: var(--dark-green);
    padding: 0.8em 30px;
    border-radius: 40px;
    color: var(--panding-gary);
}

/*Badge-Styling*/
.deliverd-badge {
    background-color: var(--dark-green);
    color: var(--white);
}

.pending-badge {
    background-color: var(--light-purple);
}

/*admin-modals*/
.prescription-edit .modal-header {
    background: var(--gradient-button);
}

.modal-dialog .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: transparent;
    transition: 0.4s;
    border-radius: 120px;
    border: 1.5px solid var(--panding-gary);
}

.modal-dialog .slider:before {
    position: absolute;
    content: "";
    height: 23px;
    width: 23px;
    left: -1px;
    bottom: 0px;
    transition: 0.4s;
    border-radius: 50%;
    border: 2px solid var(--panding-gary);
    background: var(--gray-green);
}

input:checked+.slider {
    background-color: var(--panding-gary);
}

input:checked+.slider:before {
    transform: translateX(26px);
}

.register_page_header,
.new-staff .modal-header,
.new-prescription .modal-header,
.new-patient .modal-header,
.clinic-request .modal-header,
.prescription-edit .modal-header,
.prescription-save .modal-header,
.patient-prescription-modal .modal-header,
.payment-gateway-modal .modal-header {
    background: var(--gradient-button);
    padding: 20px 10px;
}

.prescription-edit .table:not(.table-bordered).table-row-bordered thead tr,
.prescription-save .table:not(.table-bordered).table-row-bordered thead tr,
.patient-prescription-modal .table:not(.table-bordered).table-row-bordered tfoot tr {
    border-block: 1px solid var(--input-text-gray) !important;
}

.prescription-edit .table:not(.table-bordered) .input-text-gray,
.prescription-save .table:not(.table-bordered) .input-text-gray {
    color: var(--input-text-gray);
}

table.dataTable.stripe>tbody>tr.odd>*,
table.dataTable.display>tbody>tr.odd>* {
    box-shadow: none !important;
}

.table>:not(caption)>*>* {
    box-shadow: none !important;
}

table.dataTable.display>tbody>tr.even>.sorting_1,
table.dataTable.order-column.stripe>tbody>tr.even>.sorting_1 {
    box-shadow: none !important;
}

table.dataTable.row-border tbody th,
table.dataTable.row-border tbody td,
table.dataTable.display tbody th,
table.dataTable.display tbody td {
    border: 0;
    padding: 15px 10px;
}

table tbody tr:nth-child(even) {
    background-color: var(--white) !important;
}

table tbody tr {
    background-color: var(--lavender-blush) !important;
    color: var(--table-gary);
    font-family: Inter;
    font-size: 14px;
}

input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: var(--sidebar-green);
    border-radius: 20%;
    vertical-align: middle;
    border: 1px solid var(--very-light-gray);
    outline: none;
    cursor: pointer;
}

.custom-select {
    position: relative;
    display: inline-block;
    width: 200px;
    padding: 12px 40px;
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: var(--white);
}

.custom-select.search-bar { padding: 0;}
.custom-select select {
    width: 100%;
}
.select2-container .select2-selection--single{height:auto !important; }

.custom-select img {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    pointer-events: none;
    left: 11px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:active {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: var(--purple);
    color: var(--white) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: var(--purple);
    box-shadow: none;
    color: var(--white) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: transparent;
    box-shadow: none;
    color: var(--black) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: transparent;
    box-shadow: none;
    color: var(--black) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
    border-radius: 8px;
    border: 1px solid #E2D8FA;
    background: #E2D8FA;
    box-shadow: none;
    color: #000 !important;
    cursor: pointer;
}

a#clinicTable_previous {
    display: flex;
    align-items: center;
}

a#clinicTable_next {
    display: flex;
    align-items: center;
}

/*.patients-name select {font-size: 14px;font-weight: 400;outline: none;border-bottom: 1px solid var(--woodsmoke);border-top: 0;border-inline: 0;border-radius: 0;}*/
.cashback .input-group-text {
    border: 0;
    background: transparent;
}

.cashback .form-control {
    border: 0;
    padding-inline: 0;
}

/*doctor-modal*/
.register_page input,
.new-staff input,
.patients-modal input,
.prescription-modal input {
    outline: none;
    border-bottom: 1px solid var(--woodsmoke);
    border-radius: 0;
    border-top: 0;
    border-inline: 0;
    padding-bottom: 11px;
}

.register_page input::placeholder,
.new-staff input::placeholder,
.patients-modal input::placeholder,
.prescription-modal input::placeholder {
    color: var(--Silver-gray);
    font-size: 14px;
    font-weight: 400;
}

.register_page select,
.new-staff select,
.prescription-modal select,
.prescription-modal input {
    color: black;
    font-size: 14px;
    font-weight: 400;
    outline: none;
    border-bottom: 1px solid var(--woodsmoke) !important;
    border-top: 0;
    border-inline: 0;
    border-radius: 0;
}

/* .new-prescription {max-width: 700px;} */
.quantity-container .quantity-btn {
    background-color: transparent;
    border: 0;
    font-size: 25px;
    height: 33px;
    cursor: pointer;
    color: #000;
}

.register_page,
.quantity-container {
    border-bottom: 1px solid var(--woodsmoke);
}

.prescription-modal input.quantity-value {
    border-bottom: 0;
    padding-bottom: 5px;
}

.new-prescription .modal-body {
    max-height: 40em;
    overflow-y: scroll;
}

/*------------------------------  Sidebar ------------------------------ */
[data-kt-app-layout=dark-sidebar] .app-sidebar {
    border-right: 1px solid var(--soft-silver);
    background: var(--white);
}

.nav-items .menu-item:hover .menu-link {
    background-color: #77d4a3;
}

.nav-items .menu-item:hover .menu-link .title-menu {
    color: var(--white);
}

.nav-items .menu-item:hover .menu-link .menu-icon .icon-svg path {
    stroke: var(--white);
    fill: white;
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .app-sidebar-logo {
    border-bottom: 1px solid var(--soft-silver);
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu.nav-items>.menu-item .menu-link.active,
[data-kt-app-layout=dark-sidebar] .app-sidebar .menu.nav-items>.menu-item .menu-link.active .title-menu {
    transition: color .2s ease;
    background: var(--button-gradient);
    color: var(--white);
}

[data-kt-app-layout=dark-sidebar] .app-sidebar .menu.nav-items>.menu-item .menu-link.active .menu-icon .icon-svg path {
    stroke: var(--white);
    fill: white;
}

/*Clinic_admin*/
.purple-card {
    border-radius: 10px;
    border: 1.5px solid var(--border-grey);
    background: var(--white);
    padding: 15px 12px;
}

.staff_percent i {
    color: var(--red);
}

.light-green-icon i {
    color: var(--light-green);
}

.add-member-group i {
    color: var(--light-green);
    cursor: pointer;
    border: 1px solid var(--active-green);
    padding: 6px 10px;
    border-radius: 5px;
    background: var(--white);
}


.light-green-badge {
    padding: 0px 8px;
    border-radius: 40px;
    background: var(--active-green);
    font-size: 12px;
    width: 70px;
}

.purple-card img {
    height: 16px;
    width: 16px;
}

.upload_doc img {
    height: 50px;
    width: 100%;
}

.upload_doc input[type="file"] {
    display: none;
}

.border-line {
    border-bottom: 1px solid var(--woodsmoke);
}

body .new-staff .form-switch.form-check-custom .form-check-input {
    height: 15px !important;
}

.form-check-input:checked {
    background-color: var(--radio-purple);
    border-color: var(--radio-purple);
}

.form-switch.form-check-custom .form-check-input {
    height: 1.25rem;
}




/*loader*/
.logo-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--whisper-gray);
    position: fixed;
    height: 100%;
    width: 100%;
    position: fixed;
    z-index: 999;
}

.logo-loader .logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo-loader .circle {
    width: 100px;
    height: 80px;
    animation: spin 2s linear infinite;
}

.logo-loader .text {
    font-size: 34px;
    font-weight: bold;
    color: var(--black);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}


/*admin-index*/
.sales-report-button {
    border-radius: 5px;
    border: 1px solid var(--dark-jade-green);
    background: var(--white);
    padding: 10px 29px;
    height: 40px;
}

.sales-report-button .export-file-btn i {
    font-size: 16px;
    color: var(--black);
}

.Sales-report .nav-item .nav-link {
    color: var(--text-gray);
    font-size: 11px;
    font-weight: 700;
    padding: 10px 20px;
    margin: 0;
}

.Sales-report.nav-line-tabs .nav-item .nav-link:hover {
    border-bottom: 0;
}

.Sales-report.nav.nav-tabs {
    border: 0;
}

.Sales-report.nav-line-tabs .nav-item .nav-link.active {
    border-radius: 5px;
    border: 1px solid var(--scroll-purple);
    background: transparent;
    padding: 10px 20px;
}


/*doctor-index*/
.badge-pending-purple {
    color: var(--number-gray-black);
    padding: .8em 2em;
    border-radius: 40px;
    background-color: var(--light-purple);
}

.badge-delivered-green {
    color: var(--white);
    padding: .8em 2em;
    border-radius: 40px;
    background-color: var(--dark-green);
}

/*notification*/

.noti-heading {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
}

.noti-heading h1 {
    color: var(--black);
}

.noti-content {
    border-bottom: 1px solid var(--border-grey);
}

.notification-bg {
    border-radius: 10px;
    border: 1px solid var(--woodsmoke);
    background: var(--white);
    padding: 30px;
}


/*scroll*/
[data-kt-scrolltop=on] .scrolltop {
    background: var(--dark-green);
}

/*profile-setting*/
.dashboard-profile-setting .profile-img-parent {
    height: 150px;
    width: 150px;
}

.dashboard-profile-setting .image-upload-btn i {
    font-size: 33px;
    color: var(--dark-green);
}

.dashboard-profile-setting .label-parent {
    display: flex;
    gap: 136px;
}

.dashboard-profile-setting input.profile-seting-input {
    background: none;
    border: none;
    outline: none;
}

.dashboard-profile-setting .label-parent {
    padding-block: 29px;
}

.dashboard-profile-setting .parent-profile-image-setting {
    background: var(--white);
    border-radius: 20px;
    padding: 13px 13px 10em 9px;
}

.dashboard-profile-setting .label-parent.border-divider {
    border-block: 1px solid var(--border-gray-label);
}

.dashboard-profile-setting button.edit-password-btn {
    background: var(--white);
    color: var(--deep-forest-green);
    padding: 9px 0;
    border-radius: 12px;
    border: 1px solid var(--border-grey);
    width: 138px;
}

.dashboard-profile-setting span.eye-icon.field-icon.toggle-password {
    color: var(--eye-icon);
    position: absolute;
    right: 6px;
    top: 49%;
    transform: translateY(-40%);
    cursor: pointer;
}

/*patient-modals*/
.patient-prescription-modal .modal-body {
    padding: 0 25px;
}

.patient-prescription-modal .modal-footer .gradient_modal_approve {
    width: 295px;
    padding: 13px 0;
}

.patient-prescription-modal .table td,
.patient-prescription-modal .table th {
    padding: 12px 0;
}

.profile-input span.remove-icon-btn {
    position: absolute;
    margin: auto;
}

.wallet-img-parent {
    height: 48px;
    width: 48px;
}

.payment-method-parent .payment-modal-btns .modal_grey_btn,
.payment-method-parent .payment-modal-btns .button-gradient {
    width: 187px;
}

.payment-method-parent .modal-btns.payment-modal-btns {
    display: flex;
    gap: 15px;
}

.modal-dialog.payment-gateway-modal {
    max-width: 843px;
}

.payment-method-parent .payment-card-section {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: #FFF;
    padding: 20px 21px;
}

.payment-method-parent .billing-info-section {
    border-radius: 8px;
    border: 1px solid ar(--border-grey);
    background: #FFF;
    padding: 20px 21px;
    width: 331px;
}

.name-profile {
    border-block: 1px solid var(--number-gray-black);
    padding-block: 19px;
    display: flex;
    gap: 14px;
}

.contact-img-parent {
    height: 40px;
    width: 43px;
}

#new-patient-modal .modal-content {
    min-width: 750px;
}

.uploadLabel {
    cursor: pointer;
    color: #00a859;
    font-weight: 700;
}

.custom-dropdown {
    background: #FFF;
    padding: 0 10px;
    border-radius: 10px 10px 0 0;
}

.profile_image img {
    height: 30px;
    width: 30px;
}

.profile_image {
    padding: 8px 10px;
    border-radius: 50px;
    border: 1px solid #24A562;
    background: radial-gradient(158.94% 127.69% at 23.3% 19.94%, #E6EFF2 8.45%, #E2D8FA 83.33%);
}

.magnifier-icon i {
    font-size: 25px;
}

.menu-state-color .menu-item:not(.here) .menu-link:hover:not(.disabled):not(.active):not(.here) {
    transition: color .2s ease;
    color: var(--light-green);
}

.new-staff input#birthday,
#new-patient-modal input#birthday {
    border: unset;
    text-transform: uppercase;
    color: var(--Silver-gray);
    font-size: 14px;
    border-bottom: 1px solid var(--woodsmoke);
    width: 100%;
    padding-right: 10px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

table.dataTable.display tbody td *:not(li, a),
table.dataTable.display thead th * {
    width: max-content;
}

table.dataTable.display tbody td,
table.dataTable.display thead th {
    width: max-content;
}

.menu-gray-600 .menu-item .menu-link {
    width: 100px;
}

.approved-btn {
    display: none !important;
}

body:has(.prescription_modal_btn) .approved-btn {
    display: block !important;
}

.register-bg-img {
    background-image: url("../media/images/login_bg.png");
    width: 100%;
    height: 100%;
}

.login-header {
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: #FFF;
}

/* .custom-pagination {
    list-style: none;
    padding: 0;
    display: flex;
    gap: 8px;
} */

/* .custom-pagination .page-item {
    display: inline-block;
}

.custom-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin-left: 2px;
    text-align: center;
    min-width: 1.5em;
    border-radius: 8px;
    text-decoration: none;
    font-size: 16px;
    font-weight: bold;
    color: #6c757d; 
    background-color: #ffffff; 
    border: 2px solid #6c757d; 
    transition: all 0.3s ease-in-out;
}

.custom-pagination .page-link:hover {
    background-color: #f0f0f0;
}

.custom-pagination .page-item.active .page-link {
    background-color: var(--button-gradient);
    color: white;
    border:none;
}

.custom-pagination .page-item.disabled .page-link {
    background-color: #e0e0e0; 
    color: #aaa;
    cursor: not-allowed;
    border-color: #e0e0e0;
} */

.prescription-modal span.select2-selection.select2-selection--single {
    border-inline: 0;
    border-top: 0;
    border-radius: 0;
}
.select2-selection.select2-selection--single.form-select.patient-select{
    border-bottom: 1px solid transparent;
}
.select2-selection.select2-selection--single.form-select.patient-select::selected{
    
}

.cursor-pointer {
    cursor: pointer;
}


/*prescription-requests-details*/
.medicines-edit-btn {
    border: 1px solid var(--border-grey);
    padding: 10px 20px;
    border-radius: 12px;

}

.medicines-status {
    background-color: #b7b5b5b0;
    padding: 10px 15px;
    border-radius: 20px;
}

.text-border {
    border-bottom: 1px solid var(--border-grey);
    padding-bottom: 15px;
}

.assign-btn {
    background: white;
    border: 1px solid var(--border-grey) !important;
    width: 100%;
}

.assign-btn:hover {
    background: var(--border-grey);
    border: 1px solid transparent;
    width: 100%;
}

input#daysCount {
    border: none;
}

#scheduleForm {
    border-bottom: 1px solid var(--border-grey);
    padding-bottom: 15px;
}

.reason-rejected textarea {
    resize: none;
    border: 0;
    outline: 0;
}

.subscription-box .subscription-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
    margin-bottom: 20px;
}

.subscription-box .subscription-item {
    display: flex;
    flex-direction: column;
}

.subscription-box .label {
    font-size: 14px;
    color: #999;
    margin-bottom: 6px;
}

.subscription-box .value {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
}

.subscription-box .tooltip-wrapper {
    position: relative;
    display: inline-block;
}

.subscription-box .tooltip-wrapper .tooltip {
    visibility: hidden;
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 6px 10px;
    border-radius: 6px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    /* top of the icon */
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    white-space: nowrap;
}

.subscription-box .tooltip-wrapper:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

.subscription-box .tooltip-wrapper .tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.subscription-box .status-badge {
    background-color: #e5f7ec;
    color: #0f9d58;
    font-size: 14px;
    padding: 4px 12px;
    border-radius: 6px;
    display: inline-block;
    font-weight: 500;
}

.subscription-box .cashback {
    font-size: 20px;
    font-weight: 600;
}

.subscription-box .border-left {
    border-left: 1px solid #ddd;
    height: 40px;
    margin: 0 20px;
}

/*filters*/
.action_btn .dropdown-toggle::after {
    content: unset;
}

.date-range-group .date-field {
    flex: 1;
    min-width: 220px;
}

.date-range-group label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
    color: #333;
}

.date-range-group input[type="date"] {
    width: 100%;
    box-shadow: none;
    transition: border-color 0.3s, box-shadow 0.3s;
    padding: 12px 25px;
    border-radius: 8px;
    border: 1px solid var(--border-grey);
    background: var(--white);
}

.date-range-group input[type="date"]:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

body .table-responsive .pagination {
    display: flex;
    flex-wrap: wrap;
    justify-content: end;
    margin: 5px;
}

.search_box a.reset-btn {
    top: 29%;
    right: 9px;
}

body .table-responsive .pagination .page-item.active .page-link {
    background: var(--button-gradient);
}

body .table-responsive .pagination .page-item.active .page-link:hover {
    color: var(--white);
}

.page-item:hover:not(.active):not(.offset):not(.disabled) .page-link {
    color: var(--sidebar-green);
}

.patients-phone .iti.iti--allow-dropdown.iti--show-flags.iti--inline-dropdown {
    width: 100%;
    padding-top: 0.2rem;
    margin-top: .75rem;
}

.right_side_bar .menu-sub-dropdown {
    border-radius: 20px;
}

/* table */
table.dataTable>thead .sorting:after,
table.dataTable>thead .sorting:before {
    display: block !important;
    padding: 2px;
}

table.dataTable thead>tr>th.sorting_asc:before {
    display: none !important;
}

table.dataTable thead>tr>th.sorting_asc:after {
    display: none !important;
}

body table.dataTable>thead [aria-sort="descending"]::after,
body table.dataTable>thead [aria-sort="descending"]::before {
    display: none !important;
}

/* body table.dataTable.display>tbody>tr.odd>.sorting_1, table.dataTable.order-column.stripe>tbody>tr.odd>.sorting_1{box-shadow: none !important;}
body table.dataTable.display>tbody>tr.even>.sorting_1, table.dataTable.order-column.stripe>tbody>tr.even>.sorting_1{box-shadow: none !important;} */

.pre-search a.reset-btn {
    right: 9px;
    top: 13px;
    padding-left: 2px;
}

table.dataTable>thead .sorting:after,
table.dataTable>thead .sorting:before {
    display: block !important;
    padding: 2px;
}

table.dataTable thead>tr>th.sorting_asc:before,
table.dataTable thead>tr>th.sorting_asc:after {
    display: none !important;
}

.app-main {
    background: #F4F4F4;
}


table.dataTable.display>tbody>tr.odd>.sorting_1,
table.dataTable.order-column.stripe>tbody>tr.odd>.sorting_1 {
    box-shadow: none !important;
}

/* Hide sorting arrows ONLY if the <th> contains a checkbox */
/* th:has(input[type="checkbox"])::before,
th:has(input[type="checkbox"])::after {
    display: none !important;
}


td:has(.action_btn)::before,
td:has(.action_btn)::after {
    display: none !important;
} */

.new-prescription .input-group-text,
#prescription-form .input-group-text,
#prescription-form .form-control {
    background-color: var(--bs-gray-200);
    /* border-radius: 10px 0 10px 0  !important; */
    border-bottom: 1px solid var(--woodsmoke);

}
.new-prescription .input-group-text,#prescription-form .input-group-text{    border-radius: 10px 0px 0px 10px !important;}
.new-prescription .input-group-text, #prescription-form .form-control{border-radius: 0px 10px 10px 0px !important;}
body .medicines-container .quantity-value {
    border-radius: 17px;
    border: 1px solid var(--black);
}

.accordion-button:not(.collapsed),
.accordion-button {
    color: var(--white);
    background: var(--button-gradient);
    font-size: 20px;
    font-weight: 500;

}

.accordion-button:not(.collapsed)::after {
    content: "\f077";
    font-family: "Font Awesome 6 Free";
    font-weight: 700;
    color: var(--white);
    font-size: 20px;
    background-image: none;
}

.accordion-button::after {
    content: "\f078";
    font-family: "Font Awesome 6 Free";
    font-weight: 700;
    color: var(--white);
    font-size: 20px;
    background-image: none;
}

.accordion-item {
    box-shadow: 0 0 12px -4px black;
    border: 1px solid var(--bg-gradient);
}
.medicine-entry {
    border: 1px solid var(--bs-green);
    padding: 10px;
    border-radius: 12px;
    margin-bottom: 11px;
}
body .quantity-value {width: 50%;
    border-radius: 10px;
    border: 1px solid var(--bg-gary);
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 10px;
    padding: 3px;
}
body .quantity-value:focus-visible {
    outline: none;
}
body .select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 10px;
}
body .select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid var(--bg-gary);
    border-radius: 10px;
}


body span.select2-dropdown.select2-dropdown--below {
            background: white;
            border-radius: 10px;
            color: black;
            box-shadow: 4px 4px 12px -4.5px black;
            margin-top: 7px;
                padding: 10px;
    font-size: 14px;
}
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    font-size: 14px;
    color: #444;
}
.clinic-name .input-group-text{
    border-radius: 10px;
}

/* sortTable */
/* body .checkbox-action th::after {
    content: "";
} */
.sortTable th::after {
    content: " ▲▼";
    font-size: 0.75em;
    color: #aaa; 
}

.sortTable th.sort-asc::after {
    content: " ▲";
    color: #666;
}


.sortTable th.sort-desc::after {
    content: " ▼";
    color: #666;
}


    /* th.sort-asc::after {content: " ▲";font-size: 0.75em;color: #666;}
    th.sort-desc::after {content: " ▼";font-size: 0.75em; color: #666;} */
    body .select2-container--default .select2-results__option--highlighted[aria-selected]  {background: var(--sidebar-green);color: white;}
body div#kt_app_content_container {margin-left: 10px;}

/*select2 */
body .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected , body .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected:hover {background-color: var(--sidebar-green);color: var(--white);}
body .select2-results__option.select2-results__option--selectable.select2-results__option--selected.select2-results__option--highlighted{color:var(--white);}
body .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected:after{background-color: var(--white);}
body .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--highlighted {color:var(--sidebar-green);}
body .select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selectable.select2-results__option--selected.select2-results__option--highlighted{color:white;}


.select-style .select2-container {
    position: relative;
    display: inline-block;
    width: 130px !important;
    padding: 12px 20px;
    border-radius: 8px; 
     border: 1px solid var(--border-grey);
    background: var(--white);
}
.custom-select .select2-container {
    width: 0 ;
    padding: 0;
    border-radius: 0;
    border: 0;
}

.prescription-modal .select2-container{width: 100% !important;}
.first-and-last::after {display:none;}
input[type="search"]::-webkit-search-cancel-button {cursor: pointer;}
.sidebar_dashboard #kt_app_sidebar_menu_scroll.scroll-y {   scrollbar-width: none;}
body:has(.patients-phone .invalid-feedback) .iti__country-container{bottom: 43px !important;}