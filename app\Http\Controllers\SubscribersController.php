<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Subscriber;
use App\Http\Requests\SubscriberRequest;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Validator;
use App\Models\Prescription;

class SubscribersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $subscribers = Subscriber::all();
        return view('subscribers.index', ['subscribers' => $subscribers]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('subscribers.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  SubscriberRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SubscriberRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'sub_email' => 'required|email|unique:subscribers,email',
        ]);
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        Subscriber::create([
            'email' => $request->sub_email,
        ]);
        return back()->with(['type' => 'success', 'message' => 'Subcribe Successfully!']);
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $subscriber = Subscriber::findOrFail($id);
        return view('subscribers.show', ['subscriber' => $subscriber]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $subscriber = Subscriber::findOrFail($id);
        return view('subscribers.edit', ['subscriber' => $subscriber]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  SubscriberRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(SubscriberRequest $request, $id)
    {
        $subscriber = Subscriber::findOrFail($id);
        $subscriber->email = $request->input('email');
        $subscriber->save();

        return to_route('subscribers.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $subscriber = Subscriber::findOrFail($id);
        $subscriber->delete();

        return to_route('subscribers.index');
    }
    public function download()
    {
        $subscribers = Subscriber::all(['email']);

        $csvHeader = ['Email'];
        $filename = 'subscribers.csv';

        $callback = function () use ($subscribers, $csvHeader) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $csvHeader);

            foreach ($subscribers as $subscriber) {
                fputcsv($file, [$subscriber->email]);
            }

            fclose($file);
        };
        return Response::stream($callback, 200, [
            "Content-Type" => "text/csv",
            "Content-Disposition" => "attachment; filename=$filename",
        ]);
    }
    public function downloadPrescription()
    {
        $prescriptions = Prescription::where('clinic_id', auth()->user()->id)->get();

        $csvHeader = [
            'Patient Name',
            'Created By',
            'Approved By',
            'Billed Amount',
            'Date Requested',
            'Status'
        ];

        $filename = 'prescriptions.csv';

        $callback = function () use ($prescriptions, $csvHeader) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $csvHeader);

            foreach ($prescriptions as $prescription) {
                $status = match($prescription->status) {
                    0 => 'Pending',
                    1 => 'Approved',
                    2 => 'Rejected',
                    default => 'Unknown'
                };

                fputcsv($file, [
                    $prescription->patient->name ?? 'Outsourced',
                    $prescription->prescribedBy->name ?? 'Outsourced',
                    $prescription->approvedBy == 1  ? 'Prescriber' : 'Pending',
                    $prescription->total ?? '0',
                    $prescription->created_at->format('M d, Y'),
                    $status
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, [
            "Content-Type" => "text/csv",
            "Content-Disposition" => "attachment; filename=$filename",
        ]);
    }
}
