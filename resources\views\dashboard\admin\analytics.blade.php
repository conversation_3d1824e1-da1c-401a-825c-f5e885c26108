@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    <!-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> -->
    <style>
        .nav-btn {
            border: 1px solid #ccc;
            background: white;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        .month-text {
            font-weight: 500;
        }

        .filter-btn {
            background: white;
            border: 1px solid #ccc;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
        }

        .dropdown {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 6px;
            box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
            min-width: 150px;
            padding: 12px;
            z-index: 1000;
        }

        .dropdown.show {
            display: block;
        }

        .dropdown label {
            display: block;
            margin-bottom: 8px;
        }

        .dropdown input[type="checkbox"] {
            margin-right: 6px;
        }


        /* Style for the select and input fields */
        select,
        input[type="date"] {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: #fff;
        }

        /* Styling for the selected method dropdown */
        #method-select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
            background-color: #fff;
        }

        /* Adjustments for the 'By Range' input fields */
        .range-container .start-range,
        .range-container .end-range {
            width: 48%;
            /* Ensures both fields share equal space */
        }
    </style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <h2 class="mb-3">Analytics Reporting</h2>
                    <div class="table-invoices ">
                        <div class="overview">
                            <h2 class=" fs-16 fw-700  mb-3">Overview</h2>
                            <div class="row clinic_index row-gap-3">
                                @if (!Auth::user()->hasRole('doctor'))
                                    @if (!Auth::user()->hasRole('clinic_admin') && !Auth::user()->hasRole('staff'))
                                        <div class="col-xl-3 col-6">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-12 fw-600 input-text-gray"> TOTAL CLINIC</p>
                                                </div>
                                                <div class="d-flex justify-content-between staff_percent">
                                                    <div class="fs-21 fw-700 input-text-gray" data-kt-countup="true"
                                                        data-kt-countup-value="{{ $clinic_admins->total() ?? '0' }}"
                                                        data-kt-countup-prefix="">0
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="col-xl-3 col-6">
                                        <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                            <div class="d-flex justify-content-between flex-column">
                                                <p class="fs-12 fw-600 input-text-gray"> TOTAL STAFF </p>
                                            </div>

                                            <div class="d-flex justify-content-between ">
                                                <div class="fs-21 fw-700 input-text-gray" data-kt-countup="true"
                                                    data-kt-countup-value="{{ $staffs->total() ?? '0' }}"
                                                    data-kt-countup-prefix="">0
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                                @if (Auth::user()->hasRole('doctor') || Auth::user()->hasRole('clinic_admin'))
                                    <div class="col-xl-3 col-6">
                                        <a href="/inventories">
                                            <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                                <div class="d-flex justify-content-between flex-column">
                                                    <p class="fs-12 fw-600 input-text-gray"> TOTAL MEDICINE </p>
                                                </div>

                                                <div class="d-flex justify-content-between ">
                                                    <div class="fs-21 fw-700 input-text-gray" data-kt-countup="true"
                                                        data-kt-countup-value="{{ $medicines->total() ?? '0' }}"
                                                        data-kt-countup-prefix="">0
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                    </div>
                                @endif
                                <div class="col-xl-3 col-6">
                                    <a href="/patients">
                                        <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                            <div class="d-flex justify-content-between flex-column">
                                                <p class="fs-12 fw-600 input-text-gray"> TOTAL PATIENTS </p>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <div class="fs-21 fw-700 input-text-gray" data-kt-countup="true"
                                                    data-kt-countup-value="{{ $patients->total() ?? '0' }}"
                                                    data-kt-countup-prefix="">0
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-xl-3 col-6">
                                    <a href="/prescriptions">
                                        <div class="purple-card h-100 d-flex justify-content-between flex-column">
                                            <div class="d-flex justify-content-between flex-column">
                                                <p class="fs-12 fw-600 input-text-gray"> TOTAL PRESCRIPTIONS </p>
                                            </div>

                                            <div class="d-flex justify-content-between">
                                                <div class="fs-21 fw-700 input-text-gray" data-kt-countup="true"
                                                    data-kt-countup-value="{{ $prescriptions->total() ?? '0' }}"
                                                    data-kt-countup-prefix="">0
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="table-all-invoices pt-3 ">
                        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#patients">Patients</a>
                            </li>
                            @if (!Auth::user()->hasRole('clinic_admin') && !Auth::user()->hasRole('doctor') && !Auth::user()->hasRole('staff'))
                                <li class="nav-item">
                                    <a class="nav-link " data-bs-toggle="tab" href="#Clinic-admin">Clinic Admin</a>
                                </li>
                            @endif
                            @if (!Auth::user()->hasRole('doctor') && !Auth::user()->hasRole('clinic_admin') && !Auth::user()->hasRole('staff'))
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#Prescribers">Individual doctor</a>
                                </li>
                            @endif
                            @if (!Auth::user()->hasRole('staff') && !Auth::user()->hasRole('doctor'))
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#Receptionist">Clinic Staff </a>
                                </li>
                            @endif
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#Prescriptions">Prescriptions </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#Medicines">Medicines </a>
                            </li>
                        </ul>
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane show fade active" id="patients" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1 ">
                                            <div class="dropdown-container select-style d-flex flex-column">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="left-side-tabs">
                                    <div class="custom_tabs">
                                        <div
                                            class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                            <h3>Patients</h3>
                                            <div
                                                class="patients-tabs-main d-flex justify-content-end column-gap-2 align-items-center flex-wrap">
                                                <div class="custom-select search-bar">
                                                    <input type="search" class="search form-control "
                                                        value="{{ old('search', $search) }}" name="search"
                                                        placeholder="Search Patients...">
                                                </div>

                                                <div class="custom-select">
                                                    <select id="" class="status_filter" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="" selected disabled>Select</option>
                                                        <option value="1">Active</option>
                                                        <option value="0">Deactive</option>
                                                    </select>
                                                    <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                        alt="Filter Icon">
                                                </div>
                                                <a href="{{ route('report.export.csv', ['type' => 'patients']) }}"
                                                    class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                                </a>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table id="staffPrescritionTable"
                                                class="table display custom-table gy-5 gs-5 sortTable">
                                                <thead>
                                                    <tr>
                                                        <th class="min-w-200px">Name</th>
                                                        <th class="min-w-200px">Created By</th>
                                                        <th class="min-w-200px">Date requested</th>
                                                        <th class="min-w-200px">Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody class="ps-5">
                                                    @forelse($patients as $user)
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex gap-2 align-items-center">
                                                                    <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                        alt="" height="40px" width="40px">
                                                                    <div class="d-flex flex-column">
                                                                        <h5 class="fw-500 number-gray-black">
                                                                            {{ $user->name ?? '' }}
                                                                        </h5>
                                                                        <span
                                                                            class="input-text-gray fs-14 fw-400">{{ $user->email ?? '' }}</span>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td class="w-200px">{{ $user->createdBy->name ?? '' }}</td>
                                                            <td class="w-200px">{{ $user->created_at->format('M d Y') }}</td>
                                                            <td class="w-200px"><span
                                                                    class="badge badge-{{ $user->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400 ">{{ $user->statusText ?? '' }}</span>
                                                            </td>
                                                        </tr>
                                                    @empty
                                                        <tr>
                                                            <td colspan="4" class="text-center">No patients found</td>
                                                        </tr>
                                                    @endforelse
                                                </tbody>
                                            </table>
                                            <div class="d-flex justify-content-between align-items-center mt-5">
                                                <p>
                                                    Showing {{ $patients->firstItem() }} to {{ $patients->lastItem() }} of
                                                    {{ $patients->total() }}
                                                    entries
                                                </p>
                                                <div class="pagination">
                                                    {{ $patients->links() }}
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade " id="Clinic-admin" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1">
                                            <div class="dropdown-container d-flex flex-column select-style">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom_tabs">
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <h3>Clinic Admins</span>
                                        </h3>
                                        <div
                                            class="patients-tabs-main d-flex justify-content-end column-gap-2 align-items-center flex-wrap">
                                            <div class="custom-select search-bar">
                                                <input type="search" class="search form-control"
                                                    value="{{ old('search', $search) }}" name="search"
                                                    placeholder="Search Clinic admins...">
                                            </div>


                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Select</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Deactive</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>
                                            <a href="{{ route('report.export.csv', ['type' => 'clinic_admins']) }}"
                                                class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                            </a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table id="staffPrescritionTable"
                                            class="table display custom-table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <th class="min-w-200px">Name</th>
                                                    <th class="min-w-200px">Type</th>
                                                    <th class="min-w-200px">Date requested</th>
                                                    <th class="min-w-200px">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody class="ps-5">
                                                @forelse($clinic_admins as $user)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $user->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $user->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="w-200px">{{ $user->type ?? '' }}</td>
                                                        <td class="w-200px">{{ $user->created_at->format('M d Y') ?? '' }}</td>
                                                        <td class="w-200px"><span
                                                                class="badge badge-{{ $user->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $user->statusText ?? '' }}</span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="4" class="text-center">No clinic Admins found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $clinic_admins->firstItem() }} to
                                                {{ $clinic_admins->lastItem() }} of
                                                {{ $clinic_admins->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $clinic_admins->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Prescribers" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1">
                                            <div class="dropdown-container d-flex flex-column select-style">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom_tabs">
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <h3>Prescribers</span>
                                        </h3>
                                        <div
                                            class="patients-tabs-main d-flex justify-content-end column-gap-2 align-items-center flex-wrap">
                                            <div class="custom-select search-bar">
                                                <input type="search" class="search form-control"
                                                    value="{{ old('search', $search) }}" name="search"
                                                    placeholder="Search Prescribers...">
                                            </div>


                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Select</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Deactive</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>
                                            <a href="{{ route('report.export.csv', ['type' => 'doctors']) }}"
                                                class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                            </a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table id=""
                                            class="table staffPrescritionTable display custom-table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <th class="min-w-200px">Name</th>
                                                    <th class="min-w-200px">Type</th>
                                                    <th class="min-w-200px">Date requested</th>
                                                    <th class="min-w-200px">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody class="ps-5">
                                                @forelse($doctors as $user)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $user->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $user->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="w-200px">{{ $user->type ?? 'Type 1' }}</td>
                                                        <td class="w-200px">{{ $user->created_at->format('M d Y') ?? '' }}</td>
                                                        <td class="w-200px">
                                                            <span
                                                                class="badge badge-{{ $user->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $user->statusText ?? '' }}</span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="7" class="text-center">No Individual Doctors found
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $doctors->firstItem() }} to {{ $doctors->lastItem() }} of
                                                {{ $doctors->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $doctors->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Receptionist" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1">
                                            <div class="dropdown-container d-flex flex-column select-style">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom_tabs">
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <h3>Receptionists</h3>
                                        <div
                                            class="patients-tabs-main d-flex justify-content-end column-gap-2 align-items-center flex-wrap">
                                            <div class="custom-select search-bar">
                                                <input type="search" class="search form-control"
                                                    value="{{ old('search', $search) }}" name="search"
                                                    placeholder="Search Receptionists...">
                                            </div>
                                            <div class="custom-select ">
                                                <select id="statusFilter" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>Select</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Deactive</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>
                                            <a href="{{ route('report.export.csv', ['type' => 'staffs']) }}"
                                                class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                            </a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table id=""
                                            class="staffPrescritionTable table display custom-table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <th class="min-w-200px">Name</th>
                                                    <th class="min-w-200px">Created By</th>
                                                    <th class="min-w-200px">Date requested</th>
                                                    <th class="min-w-200px">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody class="ps-5">
                                                @forelse($staffs as $user)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $user->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $user->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="w-200px">{{ $user->createdBy->name ?? '' }}</td>
                                                        <td class="w-200px">{{ $user->created_at->format('M d Y') ?? '' }}</td>
                                                        <td class="w-200px">
                                                            <span
                                                                class="badge badge-{{ $user->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $user->statusText ?? '' }}</span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="7" class="text-center">No Recipenists found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $staffs->firstItem() }} to {{ $staffs->lastItem() }} of
                                                {{ $staffs->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $staffs->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Prescriptions" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1">
                                            <div class="dropdown-container d-flex flex-column select-style">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom_tabs">
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <h3>Prescriptions</span>
                                        </h3>
                                        <div
                                            class="patients-tabs-main d-flex justify-content-end column-gap-2 align-items-center flex-wrap">
                                            <div class="custom-select search-bar">
                                                <input type="search" class="search form-control"
                                                    value="{{ old('search', $search) }}" name="search"
                                                    placeholder="Search prescriptions...">
                                            </div>
                                            <div class="custom-select">
                                                <select id="" class="status_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>status</option>
                                                    <option value="0">Pending</option>
                                                    <option value="1">Accepted</option>
                                                    <option value="2">Rejected</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>
                                            <div class="custom-select">
                                                <select id="" class="type_filter" data-control="select2"
                                                    data-hide-search="true" data-dropdown-css-class="w-200px">
                                                    <option value="" selected disabled>All Types</option>
                                                    <option value="Type 1">Type 1</option>
                                                    <option value="Type 2">Type 2</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div>
                                            <a href="{{ route('report.export.csv', ['type' => 'prescriptions']) }}"
                                                class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                            </a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table id="staffPrescritionTable"
                                            class="table display custom-table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <th class="min-w-200px">Name</th>
                                                    <th class="min-w-200px">Prescribed By</th>
                                                    <th class="min-w-200px">Date requested</th>
                                                    <th class="min-w-200px">Type</th>
                                                    <th class="min-w-200px">Status</th>
                                                </tr>
                                            </thead>
                                            <tbody class="ps-5">
                                                @forelse($prescriptions as $prescription)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $prescription->patient->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $prescription->patient->email ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="w-200px">{{ $prescription->prescribedBy->name ?? '' }}</td>
                                                        <td class="w-200px">
                                                            {{ $prescription->created_at->format('M d Y') ?? '' }}
                                                        </td>
                                                        <td class="w-200px">{{ $prescription->type ?? 'Type 1' }}</td>
                                                        <td><span
                                                                class="badge badge-inactive roboto fs-14 fw-400">{{ $prescription->adminApprovalText ?? '' }}</span>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center">No Prescription found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $prescriptions->firstItem() }} to
                                                {{ $prescriptions->lastItem() }} of
                                                {{ $prescriptions->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $prescriptions->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="Medicines" role="tabpanel">
                                <div class="selection">
                                    <div class="method-wrapper d-flex gap-md-5 gap-3 flex-wrap pb-5">
                                        <div class="select-container1">
                                            <div class="dropdown-container d-flex flex-column select-style">
                                                <label for="method-select" class="fs-16">Select Method:</label>
                                                <select class="method-select select_way" name="method"
                                                    data-control="select2" data-dropdown-css-class="w-200px"
                                                    data-hide-search="true">
                                                    <option selected disabled>Select</option>
                                                    <option value="range">By Range</option>
                                                    <option value="month">By Month</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="result-container1">
                                            <div class="range-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column">
                                                    <div
                                                        class="range-container d-flex justify-content-between align-items-center gap-5">
                                                        <div class="start-range">
                                                            <label for="start-range" class="fs-14">Start Range:</label>
                                                            <input type="date" class="start-range-input" name="start-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                        <div class="end-range">
                                                            <label for="end-range" class="fs-14">End Range:</label>
                                                            <input type="date" class="end-range-input" name="end-range"
                                                                max="{{ \Carbon\Carbon::today()->toDateString() }}">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="month-options additional-options" style="display:none;">
                                                <div class=" d-flex flex-column select-style">
                                                    <label for="select-month" class="fs-16">Select Month:</label>
                                                    <select class="select-month" name="month" data-control="select2"
                                                        data-hide-search="true" data-dropdown-css-class="w-200px">
                                                        <option value="january">January</option>
                                                        <option value="february">February</option>
                                                        <option value="march">March</option>
                                                        <option value="april">April</option>
                                                        <option value="may">May</option>
                                                        <option value="june">June</option>
                                                        <option value="july">July</option>
                                                        <option value="aug">August</option>
                                                        <option value="sept">September</option>
                                                        <option value="oct">October</option>
                                                        <option value="nov">November</option>
                                                        <option value="dec">December</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <span class="error-message text-danger"
                                                style="display:none; font-size: 0.875rem; width: 100%; margin-top: 0.25rem;">Please
                                                select Start Range
                                                first.</span>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom_tabs">
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-between align-items-center pb-5 flex-wrap">
                                        <h3>Medicines</span>
                                        </h3>
                                        <div
                                            class="patients-tabs-main d-flex justify-content-end gap-2 align-items-center flex-wrap">
                                            <div class="custom-select search-bar">
                                                <input type="search" class="search form-control "
                                                    value="{{ old('search', $search) }}" name="search"
                                                    placeholder="Search medicines...">
                                            </div>
                                            <a href="{{ route('report.export.csv', ['type' => 'medicines']) }}"
                                                class="badge badge-gradient roboto fs-14 fw-400">Export CSV
                                            </a>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                        <table id="staffPrescritionTable"
                                            class="table display custom-table gy-5 gs-5 sortTable">
                                            <thead>
                                                <tr>
                                                    <th class="min-w-200px">Name</th>
                                                    <th class="min-w-200px">Pack Size</th>
                                                    <th class="min-w-200px">Medicine Used</th>
                                                    <th class="min-w-200px">Cost Price</th>
                                                    <th class="min-w-200px">Sale Price</th>
                                                </tr>
                                            </thead>
                                            <tbody class="ps-5">
                                                @forelse($medicines as $medicine)
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex gap-2 align-items-center">
                                                                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg"
                                                                    alt="" height="40px" width="40px">
                                                                <div class="d-flex flex-column">
                                                                    <h5 class="fw-500 number-gray-black">
                                                                        {{ $medicine->name ?? '' }}
                                                                    </h5>
                                                                    <span
                                                                        class="input-text-gray fs-14 fw-400">{{ $medicine->description ?? '' }}</span>
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="w-200px">{{ $medicine->pack_size ?? '' }}</td>
                                                        <td class="w-200px">{{ $medicine->prescriptions->count() ?? '' }}</td>
                                                        <td class="w-200px">{{ $medicine->cost_price ?? '' }}</td>
                                                        <td class="w-200px">{{ $medicine->sale_price ?? '' }}</td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center">No Medicine found</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <div class="d-flex justify-content-between align-items-center mt-5">
                                            <p>
                                                Showing {{ $medicines->firstItem() }} to {{ $medicines->lastItem() }} of
                                                {{ $medicines->total() }}
                                                entries
                                            </p>
                                            <div class="pagination">
                                                {{ $medicines->links() }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </section>
@endsection
@push('js')
    <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script>
    <script>
        $(document).ready(function () {
            $('.method-select').on('change.select2', function () {
                let selected = $(this).val();
                let wrapper = $(this).closest('.method-wrapper');
                let rangeOptions = wrapper.find('.range-options');
                let monthOptions = wrapper.find('.month-options');

                rangeOptions.hide();
                monthOptions.hide();

                if (selected === 'range') {
                    rangeOptions.show();
                } else if (selected === 'month') {
                    monthOptions.show();
                }
            });
        });
    </script>


    <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->

    <script>

        $(document).ready(function () {
            // $('.end-range-input').prop('disabled', true);

            function handleDateChange() {
                const activeTab = $('.nav-tabs .nav-link.active').attr('href');
                const $startInput = $(activeTab).find('.start-range-input');
                const $endInput = $(activeTab).find('.end-range-input');
                const startDate = $startInput.val() || null;
                let endDate = null;

                if (startDate) {
                    const endDateVal = $endInput.val();
                    endDate = endDateVal ? endDateVal : new Date().toISOString().split('T')[0];
                }
                fetchData(1, startDate, endDate, month = null);
            }

            $('.month-options .select-month').on('change', function () {
                fetchData(1, startDate = null, endDate = null, $(this).val());
            });
            // $('.start-range-input').on('change', function() {
            //     $('.end-range-input').prop('disabled', false);
            //     handleDateChange();
            // });
            // When end date changes
            $('.end-range-input').on('change', function () {
                handleDateChange();
            });

            const today = new Date().toISOString().split('T')[0];
            $('input[type="date"]').attr('max', today);
            $('.status_filter').on('change', function () {
                handleDateChange()
            });
            $('.type_filter').on('change', function () {
                handleDateChange()
            });
            let typingTimer;
            let doneTypingInterval = 500;

            $('.search').on('input', function () {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(function () {
                    handleDateChange();
                }, doneTypingInterval);
            });

            function fetchData(page = 1, startDate = null, endDate = null, month = null) {
                var activeTab = $('.nav-tabs .nav-link.active').attr('href');
                var statusFilter = $(activeTab).find('.status_filter').val();
                var typeFilter = $(activeTab).find('.type_filter').val();
                var search = $(activeTab).find('.search').val();
                $.ajax({
                    url: "{{ route('report.status.filter') }}",
                    type: "GET",
                    data: {
                        filter: statusFilter,
                        type_filter: typeFilter,
                        tab: activeTab,
                        page: page,
                        start_date: startDate,
                        end_date: endDate,
                        month: month,
                        search: search
                    },
                    success: function (data) {
                        // Replace the entire table-responsive div content
                        $(activeTab).find('.table-responsive').replaceWith(data);
                    }
                });
            }

            // Handle pagination click
            $(document).on('click', '.pagination a', function (e) {
                e.preventDefault();
                var page = $(this).attr('href').split('page=')[1];
                // Get the current date range values
                const activeTab = $('.nav-tabs .nav-link.active').attr('href'); // like #patients
                const startDate = $(activeTab).find('.start-range-input').val();
                let endDate = $(activeTab).find('.end-range-input').val();
                if (!endDate) {
                    endDate = new Date().toISOString().split('T')[0];
                }
                console.log('End Date:', endDate);
                console.log('Start Date:', startDate);
                // Check if the month options are visible and get the selected month if so
                let month = null;
                if ($(activeTab).find('.month-options').is(':visible')) {
                    month = $(activeTab).find('.select-month').val();
                }
                console.log('Month:', month);
                fetchData(page, startDate, endDate, month);
            });

        });
        const months = [
            "January - February",
            "March - April",
            "May - June",
            "July - August",
            "September - October",
            "November - December"
        ];
        let index = 5;

        function updateMonth() {
            const elements = document.getElementsByClassName("month-text");
            for (let el of elements) {
                el.textContent = months[index];
            }
        }

        function previousMonth() {
            if (index > 0) index--;
            updateMonth();
        }

        function nextMonth() {
            if (index < months.length - 1) index++;
            updateMonth();
        }

        function toggleDropdown() {
            const dropdowns = document.getElementsByClassName("dropdown");
            for (let dd of dropdowns) {
                dd.classList.toggle("show");
            }
        }


        window.addEventListener("click", function (e) {
            if (!e.target.closest(".filter-container")) {
                const dropdowns = document.getElementsByClassName("dropdown");
                for (let dd of dropdowns) {
                    dd.classList.remove("show");
                }
            }
        });
    </script>
    <!-- alert -->
    <script>
        $(document).ready(function () {
            $('.export-csv-btn').on('click', function () {
                alert('Export Successfully');
            });
        });
    </script>

@endpush