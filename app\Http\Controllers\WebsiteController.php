<?php

namespace App\Http\Controllers;

use App\Models\AboutCms;
use App\Models\complaintsCms;
use App\Models\Contact;
use App\Models\ContactCms;
use App\Models\FeatureCms;
use App\Models\HomeCms;
use App\Models\OrderTermsCms;
use App\Models\OurPatientCms;
use App\Models\OurTeamCms;
use App\Models\PrescribersCms;
use App\Models\PrivacyPolicyCms;
use App\Models\RegulatoryInformationCms;
use App\Models\Setting;
use App\Models\TestimonialCms;
use App\Models\WhyUsCms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Permission;

class WebsiteController extends Controller
{
    public function index()
    {
        $home_cms = HomeCms::first();
        // $section_one = FeatureCms::where('page', 'home')->where('slug', 'section_one')->get();
        // $section_two = FeatureCms::where('page', 'home')->where('slug', 'section_two')->get();
        // $section_tree = FeatureCms::where('page', 'home')->where('slug', 'section_three')->get();
        // $section_four = FeatureCms::where('page', 'home')->where('slug', 'section_four')->get();

        $sections = FeatureCms::where('page', 'home')->get();

        $section_one = $sections->filter(function ($item) {
            return $item->slug === 'section_one';
        });
        $section_two = $sections->filter(function ($item) {
            return $item->slug === 'section_two';
        });
        $section_tree = $sections->filter(function ($item) {
            return $item->slug === 'section_three';
        });
        $section_four = $sections->filter(function ($item) {
            return $item->slug === 'section_four';
        });
        $section_seven = $sections->filter(function ($item) {
            return $item->slug === 'section_seven';
        });

        $testimonials = TestimonialCms::get();
        return view('website.index', compact(
            'testimonials',
            'home_cms',
            'section_four',
            'section_one',
            'section_two',
            'section_tree',
            'section_seven',
            'testimonials'
        ));
    }
    public function aboutUs()
    {
        $about_cms = AboutCms::first();
        $section_one = FeatureCms::where('page', 'about')->where('slug', 'section_one')->get();
        $our_teams = OurTeamCms::all();
        return view('website.about-us', compact('section_one', 'about_cms', 'our_teams'));
    }
    public function whyUs()
    {
        $why_us = WhyUsCms::first();
        $features = FeatureCms::where('page', 'why_us')->get();
        return view('website.why-us', compact('why_us', 'features'));
    }
    public function prescribers()
    {
        $prescribers_cms = PrescribersCms::first();
        $cards = FeatureCms::where('page', 'prescribers')->get();
        return view('website.prescribers', compact('prescribers_cms', 'cards'));
    }
    public function patients()
    {
        $our_patient_cms = OurPatientCms::first();
        $section_one_features = FeatureCms::where('page', 'our_patients')->where('slug', 'section_one')->get();
        $section_two_features = FeatureCms::where('page', 'our_patients')->where('slug', 'section_two')->get();
        return view('website.patients', compact('our_patient_cms', 'section_one_features', 'section_two_features'));
    }
    public function contactUs()
    {
        $contact_cms = ContactCms::first();
        $setting = Setting::first();
        return view('website.contact-us', compact('contact_cms', 'setting'));
    }
    public function contactUsPost(Request $request)
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name'  => 'required|string|max:255',
            'email'      => 'required|email|max:255',
            'phone_number'     => ['required', 'regex:/^(\+\d{1,3}[- ]?)?\d{10,15}$/'], // Ensures a valid phone number format
            'message'    => 'required|string|max:1000',
        ]);
        Contact::create($request->except('_token'));
        return redirect('contact-us')->with(['type' => 'success', 'message' => 'Query sent successfully!']);
    }
    public function termsAndConditions()
    {
        return view('website.terms-and-conditions');
    }
    public function privacyPolicy()
    {
        $privacy_policy = PrivacyPolicyCms::first();
        return view('website.privacy-policy', compact('privacy_policy'));
    }
    public function regulatoryInformation()
    {
        $regulatory_info = RegulatoryInformationCms::first();
        return view('website.regulatory-information', compact('regulatory_info'));
    }
    public function ordersAndReturns()
    {
        $order_terms = OrderTermsCms::first();
        return view('website.orders-and-returns', compact('order_terms'));
    }
    public function complaints()
    {
        $complaints = complaintsCms::first();
        return view('website.complaints', compact('complaints'));
    }
    public function faqs()
    {
        return view('website.faqs');
    }

    public function clearAll()
    {
        //$exitCodeConfig = Artisan::call('storage:link');
        $exitCodeRoute = Artisan::call('route:clear');
        $exitCodeCache = Artisan::call('cache:clear');
        $exitCodeUpdate = Artisan::call('optimize:clear');
        $exitCodeView = Artisan::call('view:clear');
        $exitConfigCache = Artisan::call('config:clear');
        // $exitCodePermissionCache = Artisan::call('permission:cache-reset');
        //$exitCodePermissionCache = Artisan::call('cache:forget laravelspatie.permission.cache');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }
    public function testPermission()
    {
        $permissions = Permission::pluck('id', 'id')->all();
        return $permissions; // Ensure you are returning the variable here
    }
    public function logout()
    {
        Auth::logout();
        return redirect('/'); // Redirect the user after logout
    }
    public function home()
    {
        if (auth()->user()) {
            return redirect('home');
        } else {
            return view('auth.login');
        }
    }
    public function uploadFiles(Request $request)
    {
        $recentCourse = Course::find($request->course_id);
        if ($request->hasFile('video')) {
            $path = $this->storeImage("course-asset", $request->file('video'));
            $recentCourse->update([
                'media' => $path, // Store the path to the media field
            ]);
            return $recentCourse;
        } else {
            return response()->json(['error' => 'No video file uploaded'], 400);
        }
    }
}
