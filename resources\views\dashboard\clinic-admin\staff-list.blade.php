@foreach ($staff as $user)
    <tr>
        {{-- <td><input type="checkbox" class="row-select"></td> --}}
        <td>
            <a  href="{{ route('clinic.staff.detail', $user->id) }}">
            <div class="d-flex gap-2 align-items-center">
                <img src="{{ asset('website') }}/assets/media/images/table-featured-icon.svg" alt="" height="40px"
                    width="40px">
                <div class="d-flex flex-column">
                    <h5 class="fw-500 number-gray-black">
                        {{ $user->name ?? '' }}
                    </h5>
                    <span class="input-text-gray fs-14 fw-400">{{ $user->email ?? '' }}</span>
                </div>
            </div>
            </a>
        </td>
        <td>{{ $user->created_at->format('M d, Y') ?? '' }}</td>
        <td>{{ $user->users->count() ?? '' }}</td>
        <td>{{ $user->ownPrescriptions->count() ?? '' }}</td>
        <td><span
                class="badge badge-{{ $user->status == 1 ? 'active' : 'inactive' }} roboto fs-14 fw-400">{{ $user->status == 1 ? 'Active' : 'Inactive' }}</span>
        </td>
        <td>
            <div class="dropdown">
                <a class="nav-link" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown"
                    aria-expanded="true">
                    <i class="fa-solid fa-ellipsis-vertical"></i>
                </a>
                <ul class="dropdown-menu " aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item Satoshi" href="{{ route('clinic.staff.detail', $user->id) }}">View</a>
                    </li>        
                </ul>
            </div>
        </td>
    </tr>
@endforeach