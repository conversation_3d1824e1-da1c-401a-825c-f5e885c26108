@extends('theme.layout.master')
<!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css"> -->
@push('css')
    {{-- <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" /> --}}
@endpush

@section('content')
    <div id="kt_app_content" class="app-content tabs-sec">
        <div id="kt_app_content_container" class="app-container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="d-flex justify-content-between prescription_modal_btn">
                        <h4 class="roboto heading-gary"> All Patients</h4>
                        {{-- <a href="#!" class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save"> Add
                            Doctors</a> --}}
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-5">
                        <ul class="nav nav-tabs nav-line-tabs mb-5 fs-16">
                            <li class="nav-item roboto fw-500">
                                <a class="nav-link active" data-bs-toggle="tab" href="#clinic_doctors">Patients</a>
                            </li>
                            <li class="nav-item roboto  fw-500">
                                <a class="nav-link" data-bs-toggle="tab" href="#clinic_receptionist">Prescriptions</a>
                            </li>
                        </ul>
                        @if (Auth::user()->hasRole('clinic_admin'))
                            <button type="button" data-bs-toggle="modal" data-bs-target="#csvModal"
                                class="button-gradient white-color roboto fs-14 fw-400">
                                Bulk Import
                            </button>
                        @endif
                    </div>
                    <div class="custom-dropdown">
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="clinic_doctors" role="tabpanel">
                                <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                    <div>
                                        <h3>Patients
                                            <!-- <span class="input-text-gray">({{ $countPatients ?? 0 }})</span> -->
                                        </h3>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                        <div class="search_box">
                                            <input type="search" id="searchMedicines" class="search form-control"
                                                name="search_admin" placeholder="Search By Name...">
                                        </div>

                                        <!-- <div class="custom-select">
                                                <select id="statusFilter">
                                                    <option value="">All Request</option>
                                                    <option value="1">Active</option>
                                                    <option value="0">Inactive</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div> -->

                                        <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Status</option>
                                                <option value="1">Active</option>
                                                <option value="0">Inactive</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table id="" class="table display custom-table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <!-- <th class="ps-5 min-w-20px first-and-last">
                                                    <input type="checkbox" id="select-all" class="select-all ">
                                                </th> -->
                                                <th class="min-w-300px">Patient Name</th>
                                                <th class="min-w-200px">Created By</th>
                                                <th class="min-w-200px">Role</th>
                                                <th class="min-w-200px">Type</th>
                                                <th class="min-w-200px">Date Requested</th>
                                                <th class="min-w-200px">Status</th>
                                                <th class="min-w-50px first-and-last "></th>
                                            </tr>
                                        </thead>
                                        <tbody id="patientTableBody">
                                        </tbody>

                                    </table>
                                    <div id="patientPaginationLinks" class=" custom-dropdown pagination">

                                    </div>

                                </div>
                            </div>

                            <div class="tab-pane fade" id="clinic_receptionist" role="tabpanel">
                                <div class="custom-dropdown roboto d-flex justify-content-between align-items-center py-5">
                                    <div>
                                        <h3>Prescriptions</h3>
                                    </div>
                                    <div
                                        class="custom-dropdown roboto d-flex justify-content-end align-items-center pb-5 gap-3 flex-wrap">
                                        <div class="search_box">
                                            <input type="search" id="searchMedicines" class="search form-control"
                                                name="search_admin" placeholder="Search By Name...">
                                        </div>
                                        <!-- <div class="custom-select">
                                                <select id="statusFilter">
                                                    <option value="">All Status</option>
                                                    <option value="0">Pending</option>
                                                    <option value="1">Approved</option>
                                                    <option value="2">Rejected</option>
                                                </select>
                                                <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                    alt="Filter Icon">
                                            </div> -->
                                        <div class="custom-select">
                                            <select id="" class="status_filter" data-control="select2"
                                                data-hide-search="true" data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Status</option>
                                                <option value="0">Pending</option>
                                                <option value="1">Approved</option>
                                                <option value="2">Rejected</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>
                                        <div class="custom-select">
                                            <select id="" class="status_filter prescriptionTypeFilter"
                                                data-control="select2" data-hide-search="true"
                                                data-dropdown-css-class="w-200px">
                                                <option value="" selected>All Types</option>
                                                <option value="one_time">One Time</option>
                                                <option value="repeat">Repeat</option>
                                            </select>
                                            <img src="{{ asset('website') }}/assets/media/images/filter_alt.svg"
                                                alt="Filter Icon">
                                        </div>


                                        <a type="button" class="button-gradient white-color roboto fs-14 fw-400"
                                            href="{{ route('download_all_prescription') }}">
                                            Download
                                        </a>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table id="clinicTable" class="table display custom-table gy-5 gs-5 sortTable">
                                        <thead>
                                            <tr>
                                                <!-- <th class="ps-5 min-w-20px first-and-last"><input type="checkbox"
                                                        id="select-all" class="select-all ">
                                                </th> -->
                                                <th class="min-w-300px">Patient Name</th>
                                                <th class="min-w-200px">Prescription Type</th>
                                                <th class="min-w-200px">Created By</th>
                                                <th class="min-w-200px">Role</th>
                                                <th class="min-w-200px">Type</th>
                                                <th class="min-w-200px">Approved By</th>
                                                <th class="min-w-200px">Billed Amount</th>
                                                <th class="min-w-200px">Date Requested</th>
                                                <th class="min-w-200px">Status</th>
                                                <th class="min-w-50px first-and-last"></th>
                                            </tr>
                                        </thead>
                                        <tbody id="prescriptionTableBody">
                                        </tbody>
                                    </table>
                                    <div id="prescriptionPaginationLinks">

                                    </div>
                                    {{-- <div class="mt-4 text-end">
                                        <a href="{{ route('generate-prescription-pdf', ['orderId' => 'all']) }}"
                                            class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i>Download All Prescriptions PDF
                                        </a>
                                    </div> --}}
                                </div>
                            </div>
                        </div>
                        <div>
                        </div>
                    </div>
                </div>
            </div>
            @include('dashboard.templates.modals.clinic-modals.prescription_modal')
            <!-- Modal for CSV Upload -->
            <div class="modal fade" id="csvModal" tabindex="-1" aria-labelledby="csvModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="csvModalLabel">Upload CSV</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- CSV Upload Form -->
                            <form id="csvForm" enctype="multipart/form-data">
                                @csrf
                                <div class="mb-3">
                                    <label for="csvFile" class="form-label">Choose CSV File</label>
                                    <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv">
                                </div>
                                <div class="d-flex justify-content-center">
                                <button type="submit" class="button-gradient white-color roboto fs-14 fw-400" id="uploadBtn">Upload</button>
                                </div>
                            </form>

                            <!-- Progress Bar (Hidden Initially) -->
                            <div id="progressContainer" style="display: none; margin-top: 20px;">
                                <p id="progressStatus">0% - Starting Import...</p>
                                <div class="progress" style="height: 20px;">
                                    <div id="progress" class="progress-bar progress-bar-striped" role="progressbar"
                                        style="width: 0%; background-color: #007bff;" aria-valuenow="0" aria-valuemin="0"
                                        aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
@endsection

@push('js')
            <!-- <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script> -->
            {{-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> --}}
            <script>
                $(document).ready(function () {
                    // Function to load patient requests with search and filter
                    function loadPatientRequest(page = 1, perPage = 30) {
                        let searchValue = $('#clinic_doctors .search').val();
                        let statusValue = $('#clinic_doctors .status_filter').val();

                        $.ajax({
                            url: "{{ route('clinic.patient') }}",
                            type: "GET",
                            dataType: "json",
                            data: {
                                page: page,
                                per_page: perPage,
                                search: searchValue,
                                status: statusValue
                            },
                            success: function (data) {
                                let tableBody = $('#patientTableBody');
                                let paginationLinks = $('#patientPaginationLinks');

                                if (!data.rows || !data.rows.trim()) {
                                    tableBody.html(
                                        `<tr><td colspan="8" class="text-center">No Record Found</td></tr>`);
                                    paginationLinks.html("");
                                } else {
                                    tableBody.html(data.rows);
                                    paginationLinks.html(data.pagination);
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error("AJAX Error: ", xhr.responseText);
                            }
                        });
                    }

                    // Function to load prescription requests with search and filter
                    function loadPrescriptionRequest(page = 1, perPage = 30) {
                        let searchValue = $('#clinic_receptionist .search').val();
                        let statusValue = $('#clinic_receptionist .status_filter').val();
                        let prescriptionTypeValue = $('.prescriptionTypeFilter').val();

                        $.ajax({
                            url: "{{ route('clinic.prescription') }}",
                            type: "GET",
                            dataType: "json",
                            data: {
                                page: page,
                                per_page: perPage,
                                search: searchValue,
                                status: statusValue,
                                prescription_type: prescriptionTypeValue
                            },
                            success: function (data) {
                                let tableBody = $('#prescriptionTableBody');
                                let paginationLinks = $('#prescriptionPaginationLinks');

                                if (!data.rows || !data.rows.trim()) {
                                    tableBody.html(
                                        `<tr><td colspan="10" class="text-center">No Record Found</td></tr>`);
                                    paginationLinks.html("");
                                } else {
                                    tableBody.html(data.rows);
                                    paginationLinks.html(data.pagination);
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error("AJAX Error: ", xhr.responseText);
                            }
                        });
                    }

                    // Initial load for both tabs
                    loadPatientRequest();
                    loadPrescriptionRequest();

                    // Handle search input with debounce
                    let searchTimeout;
                    $(document).on('input', '.search', function () {
                        clearTimeout(searchTimeout);
                        let activeTab = $('.nav-tabs .nav-link.active').attr('href');
                        searchTimeout = setTimeout(() => {
                            if (activeTab === '#clinic_doctors') {
                                loadPatientRequest();
                            } else {
                                loadPrescriptionRequest();
                            }
                        }, 500);
                    });

                    // Handle search clear button (X) click
                    $(document).on('search', '.search', function () {
                        let activeTab = $('.nav-tabs .nav-link.active').attr('href');
                        if (activeTab === '#clinic_doctors') {
                            loadPatientRequest();
                        } else {
                            loadPrescriptionRequest();
                        }
                    });

                    // Handle status filter changes
                    $(document).on('change', '.status_filter', function () {
                        let activeTab = $('.nav-tabs .nav-link.active').attr('href');
                        if (activeTab === '#clinic_doctors') {
                            loadPatientRequest();
                        } else {
                            loadPrescriptionRequest();
                        }
                    });

                    // Handle prescription type filter changes
                    $(document).on('change', '.prescriptionTypeFilter', function () {
                        loadPrescriptionRequest();
                    });

                    // Handle tab changes
                    $('.nav-tabs .nav-link').on('shown.bs.tab', function (e) {
                        let target = $(e.target).attr('href');
                        if (target === '#clinic_doctors') {
                            loadPatientRequest();
                        } else {
                            loadPrescriptionRequest();
                        }
                    });

                    // Handle pagination click
                    $(document).on('click', '.custom-pagination-link', function (e) {
                        e.preventDefault();
                        let page = $(this).data('page');
                        let type = $(this).data('type');
                        if (page) {
                            if (type === 'patient') {
                                loadPatientRequest(page);
                            } else if (type === 'prescription') {
                                loadPrescriptionRequest(page);
                            }
                        }
                    });
                });
            </script>
            <script>
                $('#csvForm').on('submit', function (event) {
                    event.preventDefault();

                    var formData = new FormData();
                    formData.append('csvFile', $('#csvFile')[0].files[0]);
                    formData.append('_token', $('input[name="_token"]').val());
                    $('#progressContainer').show();
                    $('#progress').css('width', '0%');
                    $('#progressStatus').text('0% - Starting Import...');
                    $.ajax({
                        url: "{{ route('bulk.import') }}",
                        type: "POST",
                        data: formData,
                        contentType: false,
                        processData: false,
                        xhr: function () {
                            var xhr = new XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function (e) {
                                if (e.lengthComputable) {
                                    var percent = (e.loaded / e.total) * 100;
                                    $('#progress').css('width', percent + '%');
                                    $('#progressStatus').text(Math.round(percent) + '% - Importing...');
                                }
                            });
                            return xhr;
                        },
                        success: function (response) {
                            Swal.fire({
                                title: 'Import Complete!',
                                text: response.message,
                                icon: 'success'
                            });
                            $('#progressContainer').hide();
                            location.reload()
                        },
                        error: function (xhr, status, error) {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Something went wrong, please try again.',
                                icon: 'error'
                            });
                            $('#progressContainer').hide();
                        }
                    });
                });
            </script>
 @endpush
