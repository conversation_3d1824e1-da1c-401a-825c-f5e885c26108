<?php

use PHPUnit\Framework\TestCase;
use SimpleSoftwareIO\QrCode\DataTypes\PhoneNumber;

class PhoneNumberTest extends TestCase
{
    public function test_it_generates_the_proper_format_for_calling_a_phone_number()
    {
        $phoneNumber = new PhoneNumber();
        $phoneNumber->create(['************']);

        $properFormat = 'tel:************';

        $this->assertEquals($properFormat, strval($phoneNumber));
    }
}
