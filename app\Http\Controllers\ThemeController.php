<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\Prescription;
use App\Models\Inventory;
use App\Models\Setting;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\View;

class ThemeController extends Controller
{
    function __construct() {}
    public function dashboard()
    {
        $user = auth()->user();
        $user->clinicStaff;
        if ($user->hasRole('admin')) {
            $totalClinic = User::ClinicAdmins()->count();
            $totalstaff = User::Staffs()->count();
            $totalPateint = User::Patients()->count();
            $totalPrescriptions = Prescription::count();
            $totalIndividuals = User::doctors()->count();

            // Get payment data for graphs
            $now = Carbon::now();

            // 12 months data
            $twelveMonthsData = [];
            for ($i = 11; $i >= 0; $i--) {
                $month = $now->copy()->subMonths($i);
                $startOfMonth = $month->copy()->startOfMonth();
                $endOfMonth = $month->copy()->endOfMonth();

                $monthlyTotal = Prescription::where('status', 1)
                    ->where(function($query) use ($startOfMonth, $endOfMonth) {
                        $query->where(function($q) use ($startOfMonth, $endOfMonth) {
                            // One-time prescriptions
                            $q->where('prescription_type', 'one_time')
                              ->whereBetween('created_at', [$startOfMonth, $endOfMonth]);
                        })->orWhere(function($q) use ($startOfMonth, $endOfMonth) {
                            // Repeat prescriptions with paid payments
                            $q->where('prescription_type', 'repeat')
                              ->whereHas('payments', function($q) use ($startOfMonth, $endOfMonth) {
                                  $q->where('status', 'paid')
                                    ->whereBetween('payment_date', [$startOfMonth, $endOfMonth]);
                              });
                        });
                    })
                    ->get()
                    ->sum(function($prescription) use ($startOfMonth, $endOfMonth) {
                        if ($prescription->prescription_type === 'repeat') {
                            return $prescription->payments()
                                ->where('status', 'paid')
                                ->whereBetween('payment_date', [$startOfMonth, $endOfMonth])
                                ->sum('total');
                        }
                        return $prescription->total;
                    });

                $twelveMonthsData[] = round($monthlyTotal, 2);
            }

            // 6 months data (last 6 months)
            $sixMonthsData = array_slice($twelveMonthsData, -6);

            // 30 days data (weekly)
            $thirtyDaysData = [];
            for ($i = 3; $i >= 0; $i--) {
                $weekStart = $now->copy()->subWeeks($i)->startOfWeek();
                $weekEnd = $now->copy()->subWeeks($i)->endOfWeek();

                $weeklyTotal = Prescription::where('status', 1)
                    ->where(function($query) use ($weekStart, $weekEnd) {
                        $query->where(function($q) use ($weekStart, $weekEnd) {
                            $q->where('prescription_type', 'one_time')
                              ->whereBetween('created_at', [$weekStart, $weekEnd]);
                        })->orWhere(function($q) use ($weekStart, $weekEnd) {
                            $q->where('prescription_type', 'repeat')
                              ->whereHas('payments', function($q) use ($weekStart, $weekEnd) {
                                  $q->where('status', 'paid')
                                    ->whereBetween('payment_date', [$weekStart, $weekEnd]);
                              });
                        });
                    })
                    ->get()
                    ->sum(function($prescription) use ($weekStart, $weekEnd) {
                        if ($prescription->prescription_type === 'repeat') {
                            return $prescription->payments()
                                ->where('status', 'paid')
                                ->whereBetween('payment_date', [$weekStart, $weekEnd])
                                ->sum('total');
                        }
                        return $prescription->total;
                    });

                $thirtyDaysData[] = round($weeklyTotal, 2);
            }

            // 7 days data (daily)
            $sevenDaysData = [];
            for ($i = 6; $i >= 0; $i--) {
                $day = $now->copy()->subDays($i);
                $startOfDay = $day->copy()->startOfDay();
                $endOfDay = $day->copy()->endOfDay();

                $dailyTotal = Prescription::where('status', 1)
                    ->where(function($query) use ($startOfDay, $endOfDay) {
                        $query->where(function($q) use ($startOfDay, $endOfDay) {
                            $q->where('prescription_type', 'one_time')
                              ->whereBetween('created_at', [$startOfDay, $endOfDay]);
                        })->orWhere(function($q) use ($startOfDay, $endOfDay) {
                            $q->where('prescription_type', 'repeat')
                              ->whereHas('payments', function($q) use ($startOfDay, $endOfDay) {
                                  $q->where('status', 'paid')
                                    ->whereBetween('payment_date', [$startOfDay, $endOfDay]);
                              });
                        });
                    })
                    ->get()
                    ->sum(function($prescription) use ($startOfDay, $endOfDay) {
                        if ($prescription->prescription_type === 'repeat') {
                            return $prescription->payments()
                                ->where('status', 'paid')
                                ->whereBetween('payment_date', [$startOfDay, $endOfDay])
                                ->sum('total');
                        }
                        return $prescription->total;
                    });

                $sevenDaysData[] = round($dailyTotal, 2);
            }

            $recenRegsUnapproved = User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['clinic_admin', 'doctor']);
            })
                ->orderBy('created_at', 'DESC')
                ->take(7)
                ->get();
            $recenRegsApproved = User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['clinic_admin', 'doctor'])
                    ->where('status', 1);
            })
                ->orderBy('created_at', 'DESC')
                ->take(8)
                ->get();

            $dashboardData = [
                'totalClinic' => $totalClinic,
                'totalStaff' => $totalstaff,
                'totalPatients' => $totalPateint,
                'totalPrescriptions' => $totalPrescriptions,
                'recenRegsUnapproved' => $recenRegsUnapproved,
                'recenRegsApproved' => $recenRegsApproved,
                'totalIndividuals' => $totalIndividuals ?? '',
                'twelveMonthsData' => $twelveMonthsData,
                'sixMonthsData' => $sixMonthsData,
                'thirtyDaysData' => $thirtyDaysData,
                'sevenDaysData' => $sevenDaysData,
            ];
        } elseif ($user->hasRole('clinic_admin')) {
            $totalstaff = $user->clinicStaff->count();
            $totalPatients = $user->clinicPatinets->count();
            $totalPrescriptions = $user->clinicPrescriptions->count();
            $recentPatients = $user->clinicPatinets()->limit(8)->get();
            $recentRegs = $user->clinicStaff()->limit(8)->get();
            $dashboardData = [
                'totalStaff' => $totalstaff,
                'totalPatients' => $totalPatients,
                'totalPrescriptions' => $totalPrescriptions,
                'recentPatients' => $recentPatients,
                'recentRegs' => $recentRegs,
            ];
        } elseif ($user->hasRole('doctor') || $user->hasRole('staff')) {
            $totalPatients = $user->ownPatient();
            $totalPrescriptions = $user->ownPrescriptions->count();
            $recentPrescriptions = $user->ownPrescriptions()->limit(8)->get();
            $recentRegs = $user->users()->limit(8)->get();

            // $recentPatients = $user->clinicPatinets()->limit(8)->get();
            // $recentRegs = $user->clinicStaff()->limit(8)->get();
            $dashboardData = [
                'totalPatients' => $totalPatients,
                'totalPrescriptions' => $totalPrescriptions,
                'recentRegs' => $recentRegs,
                'recentPrescriptions' => $recentPrescriptions,
            ];
        } elseif ($user->hasRole('patients')) {
            $totalOrders = Prescription::where('patient_id', $user->id)->count();
            $totalOrdersPaid = $user->patientPrescriptions->where('doctor_approval', 1)->where('admin_approval', 1)->where('status', 1)->count();
            $recentOrders = $user->patientPrescriptions()->limit(8)->get();
            $dashboardData = [
                'totalOrders' => $totalOrders,
                'totalOrdersPaid' => $totalOrdersPaid,
                'recentOrders' => $recentOrders,
            ];
        } else {
            $dashboardData = null;
        }
        return view('theme.index', compact('dashboardData'));
    }
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }
    public function report()
    {
        return view('dashboard.reports');
    }
    public function analyticsAdmin()
    {
        $clinic_admins = User::clinicAdmins()->orderBy('created_at', 'DESC')->paginate(10);

        if (Auth::user()->hasRole('doctor')) {
            $patients = User::whereHas('roles', function($q) {
                $q->where('name', 'patients');
            })->where('clinic_id', auth()->id())->orderBy('created_at', 'DESC')->paginate(10);
            $prescriptions = auth()->user()->ownPrescriptions()->orderBy('created_at', 'DESC')->paginate(10);
            $medicines = auth()->user()->getUsedPrescriptionInventories()->orderBy('created_at', 'desc')->paginate(10);
            $staffs = User::staffs()->orderBy('created_at', 'DESC')->paginate(10);
        } elseif (Auth::user()->hasRole('clinic_admin')) {
            $patients = auth()->user()->clinicPatinets()->orderBy('created_at', 'DESC')->paginate(10);
            $prescriptions = auth()->user()->clinicPrescriptions()->orderBy('created_at', 'DESC')->paginate(10);
            $medicines = auth()->user()->getUsedClinicInventories()->orderBy('created_at', 'DESC')->paginate(10);
            $staffs = auth()->user()->clinicStaff()->orderBy('created_at', 'DESC')->paginate(10);
        } elseif (Auth::user()->hasRole('staff')) {
            $clinic_admin = User::find(auth()->user()->clinic_id);
            $patients = $clinic_admin->clinicPatinets()->orderBy('created_at', 'DESC')->paginate(10);
            $prescriptions = $clinic_admin->clinicPrescriptions()->orderBy('created_at', 'DESC')->paginate(10);
            $medicines = $clinic_admin->getUsedClinicInventories()->orderBy('created_at', 'DESC')->paginate(10);
            $staffs = $clinic_admin->clinicStaff()->orderBy('created_at', 'DESC')->paginate(10);
        } else {
            $staffs = User::staffs()->orderBy('created_at', 'DESC')->paginate(10);
            $patients = User::patients()->orderBy('created_at', 'DESC')->paginate(10);
            $prescriptions = Prescription::orderBy('created_at', 'DESC')->paginate(10);
            $medicines = Inventory::orderBy('created_at', 'DESC')->paginate(10);
        }
        $doctors = User::Doctors()->orderBy('created_at', 'DESC')->paginate(10);
        $search = null;
        return view('dashboard.admin.analytics', compact('clinic_admins', 'staffs', 'patients', 'doctors', 'prescriptions', 'medicines', 'search'));
    }
    public function notification()
    {
        return view('dashboard.notification');
    }
    public function patients()
    {
        $user = auth()->user();
        if(auth()->user()->hasRole('doctor')){
            $patientCount = $user->ownPatient();
        }else{
            $patientCount = $user->clinic->clinicPatinets()->count();
        }
        $doctors = null;
        if ($user->hasRole('staff') && $user->profile->role == 'Receptionist') {
            $doctors = User::where('user_id', $user->createdBy->id)->whereHas('profile', function ($q) {
                $q->where('role', 'doctor');
            })->get();
        }
        return view('dashboard.staff.patients', compact('patientCount','doctors'));
    }

    public function prescriptionsDoctor()
    {
        return view('dashboard.staff.prescriptions');
    }
    public function patientsReceptionist()
    {
        return view('dashboard.receptionist.patients');
    }
    public function prescriptionsReceptionist()
    {
        return view('dashboard.receptionist.prescriptions');
    }

    //   Admin-dashboard
    public function user(Request $request)
    {
        $search_admin = $request->input('search_admin', '');
        $search_doctor = $request->input('search_doctor', '');
        $search_staff_doctor = $request->input('search_staff_doctor', '');
        //ranges
        $docStartRange = $request->input('docStartRange', '');
        $docEndRange = $request->input('docEndRange', '');
        $adminStartRange = $request->input('adminStartRange', '');
        $adminEndRange = $request->input('adminEndRange', '');
        $staffDocStartRange = $request->input('staffDocStartRange', '');
        $staffDocEndRange = $request->input('staffDocEndRange', '');
        //end ranges
        $adminStatus = $request->input('adminStatus', '');
        $presStatus = $request->input('presStatus', '');
        $staffDocStatus = $request->input('staffDocStatus', '');

        $admin_query = User::clinicAdmins()->where('status', 1);
        $doctor_query = User::Doctors()->where('status', 1);
        $staff_doctor_query = User::with('createdBy')->StaffDoctors()->where('status', 1);

        // Handle admin filters
        if ($search_admin !== '' || $adminStatus !== '' || $adminStartRange || $adminEndRange) {
            $admin_query->where(function ($q) use ($search_admin, $adminStatus, $adminStartRange, $adminEndRange) {
                // Name search
                if ($search_admin !== '') {
                    $q->where('name', 'like', '%' . $search_admin . '%');
                }

                // Date range filter
                if ($adminStartRange || $adminEndRange) {
                    if ($adminStartRange && $adminEndRange) {
                        $start = Carbon::parse($adminStartRange)->startOfDay();
                        $end = Carbon::parse($adminEndRange)->endOfDay();
                        $q->whereBetween('created_at', [$start, $end]);
                    } elseif ($adminStartRange) {
                        $start = Carbon::parse($adminStartRange)->startOfDay();
                        $q->where('created_at', '>=', $start);
                    } elseif ($adminEndRange) {
                        $end = Carbon::parse($adminEndRange)->endOfDay();
                        $q->where('created_at', '<=', $end);
                    }
                }
            });
        }

        // Handle doctor filters
        if ($search_doctor !== '' || $presStatus !== '' || $docStartRange || $docEndRange) {
            $doctor_query->where(function ($q) use ($search_doctor, $presStatus, $docStartRange, $docEndRange) {
                // Name search
                if ($search_doctor !== '') {
                    $q->where('name', 'like', '%' . $search_doctor . '%');
                }

                // Date range filter
                if ($docStartRange || $docEndRange) {
                    if ($docStartRange && $docEndRange) {
                        $start = Carbon::parse($docStartRange)->startOfDay();
                        $end = Carbon::parse($docEndRange)->endOfDay();
                        $q->whereBetween('created_at', [$start, $end]);
                    } elseif ($docStartRange) {
                        $start = Carbon::parse($docStartRange)->startOfDay();
                        $q->where('created_at', '>=', $start);
                    } elseif ($docEndRange) {
                        $end = Carbon::parse($docEndRange)->endOfDay();
                        $q->where('created_at', '<=', $end);
                    }
                }
            });
        }

        // Handle staff doctor filters
        if ($search_staff_doctor !== '' || $staffDocStatus !== '' || $staffDocStartRange || $staffDocEndRange) {
            $staff_doctor_query->where(function ($q) use ($search_staff_doctor, $staffDocStatus, $staffDocStartRange, $staffDocEndRange) {
                // Name search
                if ($search_staff_doctor !== '') {
                    $q->where('name', 'like', '%' . $search_staff_doctor . '%');
                }

                // Date range filter
                if ($staffDocStartRange || $staffDocEndRange) {
                    if ($staffDocStartRange && $staffDocEndRange) {
                        $start = Carbon::parse($staffDocStartRange)->startOfDay();
                        $end = Carbon::parse($staffDocEndRange)->endOfDay();
                        $q->whereBetween('created_at', [$start, $end]);
                    } elseif ($staffDocStartRange) {
                        $start = Carbon::parse($staffDocStartRange)->startOfDay();
                        $q->where('created_at', '>=', $start);
                    } elseif ($staffDocEndRange) {
                        $end = Carbon::parse($staffDocEndRange)->endOfDay();
                        $q->where('created_at', '<=', $end);
                    }
                }
            });
        }

        $clinic_admins = $admin_query->orderBy('created_at', 'DESC')
            ->paginate(10, ['*'], 'admins')
            ->withQueryString()
            ->fragment('admins');

        $doctors = $doctor_query->orderBy('created_at', 'DESC')
            ->paginate(10, ['*'], 'doctors')
            ->withQueryString()
            ->fragment('doctors');

        $staff_doctors = $staff_doctor_query->orderBy('created_at', 'DESC')
            ->paginate(10, ['*'], 'staff_doctors')
            ->withQueryString()
            ->fragment('staff_doctors');

        return view('dashboard.admin.user', compact(
            'clinic_admins',
            'doctors',
            'staff_doctors',
            'search_admin',
            'search_doctor',
            'search_staff_doctor',
            'adminStatus',
            'presStatus',
            'staffDocStatus',
            'docStartRange',
            'docEndRange',
            'adminStartRange',
            'adminEndRange',
            'staffDocStartRange',
            'staffDocEndRange'
        ));
    }
    public function userRequests()
    {
        $clinic_admins = User::clinicAdmins()->orderBy('created_at', 'DESC')->paginate(10);
        $doctors = User::Doctors()->orderBy('created_at', 'DESC')->paginate(10);
        $staff_doctors = User::with('createdBy')->StaffDoctors()->orderBy('created_at', 'DESC')->paginate(10);
        $staff_receptionists = User::with('createdBy')->StaffReceptionists()->orderBy('created_at', 'DESC')->paginate(10);
        return view('dashboard.admin.user_requests', compact(
            'clinic_admins',
            'doctors',
            'staff_receptionists',
            'staff_doctors',
        ));
    }
    public function userRequestsStatus(Request $request)
    {
        $value = $request->tab;
        $search = $request->search;

        $query = User::query()->with('latestUserType');

        switch ($request->tab) {
            case 'admins':
                $query->clinicAdmins();
                break;
            case 'doctors':
                $query->Doctors();
                break;
            case 'staff_doctors':
                $query->with('createdBy')->StaffDoctors();
                break;
            case 'staff_receptionists':
                $query->with('createdBy')->StaffReceptionists();
                break;
            default:
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid tab provided'
                ]);
        }

        // Add search functionality
        if ($search) {
             $query->where(function($q) use ($search) {
                $q->where('name', 'like', '%' . $search . '%')
                  ->orWhere('email', 'like', '%' . $search . '%');
            });
        }

        if ($request->filter !== null) {
            $query->where('status', $request->filter);
        }
        $data = $query->orderBy('created_at', 'DESC')->paginate(10);
        return response()->json([
            'success' => true,
            'data' => $data,
            'pagination' => (string) $data->links(),
            'value' => $value,
        ]);
    }

    public function userOrders(Request $request)
    {
        $query = Prescription::query();
        if ($request->filled('patient_name')) {
            $query->whereHas('patient', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->patient_name . '%');
            });
        }

        if ($request->filled('status') && in_array($request->status, [0, 1])) {
            $status = $request->status;

            $query->where('type', 'Type 1')
                ->where(function ($q) use ($status) {
                    // one_time: directly use status field
                    $q->where(function ($q1) use ($status) {
                        $q1->where('prescription_type', 'one_time')
                           ->where('status', $status);
                    })
                    // repeat: check latest comingPayments
                    ->orWhere(function ($q2) use ($status) {
                        $q2->where('prescription_type', 'repeat')
                           ->whereHas('comingPayments', function ($q3) use ($status) {
                               $q3->orderByDesc('id')
                                  ->where(function ($subQuery) use ($status) {
                                      if ($status == 1) {
                                          $subQuery->where('status', 'paid');
                                      } else {
                                          $subQuery->where('status', '!=', 'paid');
                                      }
                                  });
                           });
                    });
                });
        }

        // Approval status filter
        if ($request->filled('approval_status') && $request->approval_status !== 'all') {
            list($field, $value) = explode('__', $request->approval_status);

            if ($field === 'admin_approval') {
                $query->where('admin_approval', $value)
                    ->where(function ($q) {
                        $q->where('is_dispensed', 0)
                            ->orWhereNull('is_dispensed');
                    });
            } else {
                $query->where($field, $value);
            }
        }

        // Prescription type filter
        if ($request->filled('prescription_type') && in_array($request->prescription_type, ['repeat', 'one_time'])) {
            $query->where('prescription_type', $request->prescription_type);
        }

        $prescriptions = $query->orderBy('created_at', 'DESC')->where('admin_approval', 0)->where('doctor_approval', 1)->paginate(30);
        return view('dashboard.admin.user_orders', compact('prescriptions'));
    }
    public function subscriptions()
    {
        $typeOne = Prescription::where('type', 'Type 1')->where('doctor_approval', 1)->orderBy('updated_at', 'DESC')->paginate(5, ['*'], 'typeOne')->fragment('type_one');

        $typeTwo = Prescription::where('type', 'Type 2')->where('doctor_approval', 1)->orderBy('updated_at', 'DESC')->paginate(5, ['*'], 'typeTwo')->fragment('type_two');
        // $typeOne = Prescription::whereHas('patient', function ($query) {
        //     $query->where('type', 'Type 1');
        // })->orderBy('created_at', 'ASC')->paginate(5, ['*'], 'typeOne')->fragment('type_one');

        // $typeTwo = Prescription::whereHas('patient', function ($query) {
        //     $query->where('type', 'Type 2');
        // })->orderBy('created_at', 'DESC')->paginate(5, ['*'], 'typeTwo')->fragment('type_two');
        $search = null;
        return view('dashboard.admin.subscriptions', compact('typeOne', 'typeTwo', 'search'));
    }
    public function userPatients()
    {
        return view('dashboard.admin.user_patients');
    }
    public function userDoctor()
    {
        return view('dashboard.admin.user_doctor');
    }


    //    Clinic Dashboard routes

    public function clinicUser()
    {
        $user = auth()->user();
        $countDoctors = $user->countDoctors();
        $countReceptionists = $user->countReceptionists();
        return view('dashboard.clinic-admin.user', compact('countDoctors', 'countReceptionists'));
    }



    public function userDetails(Request $request)
    {
        $user = User::findOrFail($request->id); // Ensure user exists or return 404

        $patient_query = $user->clinicPatinets();
        $prescription_query = $user->clinicPrescriptions()->where('admin_approval', 1);
        $doctors_query = $user->clinicStaff()->StaffDoctors();
        $staffs_query = $user->clinicStaff()->StaffReceptionists();

        $patients = $patient_query->paginate(10, ['*'], 'patients')->fragment('patients');
        $prescriptions = $prescription_query->paginate(10, ['*'], 'prescriptions')->fragment('prescriptions');
        $staffs = $staffs_query->paginate(10, ['*'], 'staff')->fragment('staff');
        $doctors = $doctors_query->paginate(10, ['*'], 'prescriber')->fragment('prescriber');
        return view('dashboard.clinic-admin.user_details', compact(
            'patients',
            'prescriptions',
            'user',
            'doctors',
            'staffs'
        ));
    }
    public function patientPrescription(Request $request)
    {
        $user = User::findOrFail($request->id); // Ensure user exists or return 404

        $prescription_query = $user->patientPrescriptions();
        $prescriptions = $prescription_query->paginate(10, ['*'], 'prescriptions')->fragment('prescriptions');
        return view('prescription-requests.patient_prescriptions', compact(
            'prescriptions',
            'user',
        ));
    }

    public function clinicRequest()
    {
        $user = auth()->user();
        $countPatients = $user->clinicPatinets->count();
        $countPrescriptions = $user->clinicPrescriptions->count();
        return view('dashboard.clinic-admin.clinic_request', compact('countPatients', 'countPrescriptions'));
    }


    public function changeStatus(Request $request, $status)
    {
        $userId = $request->query('user_id');
        if ($userId && $status != null) {
            $user = User::find($userId);
            $user->status = (int)$status;
            $user->save();
            return redirect()->back()->with(['type' => 'success', 'message' => 'Status Updated Succesfully!']);
        } else {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Something wents wrong!']);
        }
    }

    public function prescriptionRequestIndex()
    {
        return view('prescription-requests.index');
    }

    public function prescriptionRequestDetails()
    {
        return view('prescription-requests.prescription-details');
    }
    public function patientsViewDetails($id)
    {
        $patient = User::find($id);
        return view('prescription-requests.patients-view-details', compact('patient'));
    }


    public function registrationInstruction()
    {
        return view('auth.registration-instruction');
    }
    public function adminInvoicesDetails($id)
    {
        $invoice = Invoice::find($id);
        $patient = null;

        // Get the month from request or use invoice month
        $month = request()->get('month') ? Carbon::parse(request()->get('month')) : Carbon::parse($invoice->month);

        // Get all invoices for the same month and user
        $monthlyInvoices = Invoice::where('user_id', $invoice->user_id)
            ->whereMonth('month', $month->month)
            ->whereYear('month', $month->year)
            ->with(['user', 'prescriptions.patient'])
            ->get();

        // Get all prescriptions for the month through invoice_prescriptions
        $monthlyPrescriptions = Prescription::whereHas('invoices', function ($query) use ($month) {
            $query->whereMonth('month', $month->month)
                ->whereYear('month', $month->year);
        })
            ->with(['prescriptionInventories', 'patient'])
            ->get();

        // Collect all medicine names using flatMap and group by medicine name to remove duplicates
        $allMedicines = $monthlyPrescriptions
            ->flatMap(function ($prescription) {
                return $prescription->prescriptionInventories;
            });

        // Group medicines by name and aggregate quantities and prices
        $uniqueMedicines = $allMedicines
            ->groupBy('name')
            ->map(function ($medicineGroup, $medicineName) {
                $totalUnits = $medicineGroup->sum('prescribed_units');
                $totalPrice = $medicineGroup->sum('total_price');

                // Create a new object with aggregated data
                return (object) [
                    'name' => $medicineName,
                    'prescribed_units' => $totalUnits,
                    'total_price' => $totalPrice,
                ];
            })
            ->values(); // Reset keys to maintain collection structure

        return view('dashboard.admin.admin-invoices-details', compact(
            'invoice',
            'patient',
            'uniqueMedicines',
            'monthlyInvoices',
            'monthlyPrescriptions',
            'month'
        ));
    }

    public function searchSubscription(Request $request)
    {
        $data = Prescription::with(['patient', 'prescribedBy', 'comingPayments' => function($query) {
                $query->orderBy('id', 'DESC')->latest();
            }])
            ->whereHas('patient', function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%');
            })
            ->where('doctor_approval', 1)  // Added condition for doctor_approval
            ->when(!is_null($request->tabType), function ($query) use ($request) {
                $query->where('type', $request->tabType);
            })
            ->when(!is_null($request->status), function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when(!is_null($request->prescription_type) && $request->prescription_type !== '', function ($query) use ($request) {
                $query->where('prescription_type', $request->prescription_type);
            })
            ->orderBy('created_at', 'DESC')
            ->paginate(10);

        // Transform the data to include payment_status and coming_payment
        $transformedData = $data->getCollection()->map(function ($prescription) {
            $prescription->payment_status = $prescription->payment_status;
            $prescription->coming_payment = $prescription->comingPayments->first();
            return $prescription;
        });

        // Replace the collection with transformed data
        $data->setCollection($transformedData);

        return response()->json([
            'success' => true,
            'data' => $data->items(),
            'pagination' => (string) $data->links(),
        ]);
    }
    public function downloadMedicinesCSV($id)
    {
        $invoice = Invoice::findOrFail($id);
        $month = request()->get('month') ? Carbon::parse(request()->get('month')) : Carbon::parse($invoice->month);

        // Get all prescriptions for the month through invoice_prescriptions
        $monthlyPrescriptions = Prescription::whereHas('invoices', function ($query) use ($month) {
            $query->whereMonth('month', $month->month)
                ->whereYear('month', $month->year);
        })
            ->with(['prescriptionInventories'])
            ->get();

        // Collect all medicine names using flatMap and group by medicine name to remove duplicates
        $allMedicines = $monthlyPrescriptions
            ->flatMap(function ($prescription) {
                return $prescription->prescriptionInventories;
            });

        // Group medicines by name and aggregate quantities and prices
        $uniqueMedicines = $allMedicines
            ->groupBy('name')
            ->map(function ($medicineGroup, $medicineName) {
                $totalUnits = $medicineGroup->sum('prescribed_units');
                $totalPrice = $medicineGroup->sum('total_price');

                // Create a new object with aggregated data
                return (object) [
                    'name' => $medicineName,
                    'prescribed_units' => $totalUnits,
                    'total_price' => $totalPrice,
                ];
            })
            ->values(); // Reset keys to maintain collection structure

        // CSV headers
        $csvHeader = ['Medicines', 'Used', 'Price'];
        $filename = 'invoice_medicines_' . $id . '.csv';

        // Create CSV content
        $handle = fopen('php://temp', 'r+');

        // Add month heading
        fputcsv($handle, ['Month: ' . $month->format('F Y')]);
        fputcsv($handle, []); // Empty row for spacing

        fputcsv($handle, $csvHeader);

        foreach ($uniqueMedicines as $medicine) {
            fputcsv($handle, [
                $medicine->name,
                $medicine->prescribed_units,
                '£' . number_format($medicine->total_price, 2)
            ]);
        }

        // Add total row
        fputcsv($handle, [
            'Total',
            '',
            '£' . number_format($uniqueMedicines->sum('total_price'), 2)
        ]);

        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        // Return response as CSV
        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$filename}",
        ]);
    }
    public function downloadPrescriptionsCSV($id)
    {
        $invoice = Invoice::findOrFail($id);
        $month = request()->get('month') ? Carbon::parse(request()->get('month')) : Carbon::parse($invoice->month);

        // Get all prescriptions for the month through invoice_prescriptions
        $prescriptions = Prescription::whereHas('invoices', function ($query) use ($month) {
            $query->whereMonth('month', $month->month)
                ->whereYear('month', $month->year);
        })
            ->with(['patient'])
            ->get();

        // CSV headers
        $csvHeader = ['Order ID', 'Patient Name', 'Prescription Type', 'Total Amount'];
        $filename = 'invoice_prescriptions_' . $id . '.csv';

        // Create CSV content
        $handle = fopen('php://temp', 'r+');

        // Add month heading
        fputcsv($handle, ['Month: ' . $month->format('F Y')]);
        fputcsv($handle, []); // Empty row for spacing

        fputcsv($handle, $csvHeader);

        foreach ($prescriptions as $prescription) {
            fputcsv($handle, [
                $prescription->order_id,
                $prescription->patient->name ?? 'Outsourced',
                ucfirst($prescription->prescription_type),
                '£' . number_format($prescription->total, 2)
            ]);
        }

        // Add total row
        fputcsv($handle, [
            'Total',
            '',
            '',
            '£' . number_format($prescriptions->sum('total'), 2)
        ]);

        // Add total cashback row
        fputcsv($handle, [
            'Total Cashback',
            '',
            '',
            '£' . number_format($invoice->cashback_amount ?? 0, 2)
        ]);

        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        // Return response as CSV
        return Response::make($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename={$filename}",
        ]);
    }
    public function clinicPatientFilter(Request $request)
    {
        $user = auth()->user();
        $query = $user->clinicPatinets()->with(['createdBy.profile', 'patientPrescriptions']);
        if ($request->filled('start_date')) {
            $endDate = $request->filled('end_date') ? $request->end_date : now()->toDateString();
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $endDate . ' 23:59:59'
            ]);
        }
        $results = $query->orderBy('created_at', 'DESC')->get();
        return response()->json([
            'success' => true,
            'results' => $results,
        ]);
    }
    public function clinicStaffFilter(Request $request)
    {
        $user = auth()->user();
        $query = $user->clinicStaff(); // Assuming this returns a HasMany relationship
        if ($request->status) {
            $query = $query->whereHas('profile', function ($q) use ($request) {
                $q->where('role', $request->status);
            });
        }
        $results = $query->orderBy('created_at', 'DESC')->with(['profile', 'users', 'ownPrescriptions'])->get();
        return response()->json([
            'success' => true,
            'results' => $results,
        ]);
    }
    public function adminClinicUserDetails($id)
    {
        $user = User::findOrFail($id); // Ensure user exists or return 404
        $patientsCount = $user->ownPatient();
        $prescriptionsCount = $user->ownPrescriptions()->count();
        $patients = $user->users()
            ->paginate(10)
            ->fragment('patients');
        $prescriptions = $user->ownPrescriptions()->paginate(10)->fragment('prescriptions');
        return view('dashboard.clinic-admin.staff_user_details', compact('patients', 'prescriptions', 'user', 'patientsCount', 'prescriptionsCount'));
    }
    public function searchClinicStaff(Request $request)
    {
        $tab = ltrim($request->tab, '#');
        $user = User::findOrFail($request->user_id);

        if ($tab == 'patients') {
            $query = $user->users();
            if ($request->filled('search')) {
                $query->where('name', 'like', '%' . $request->search . '%');
            }
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }
            $results = $query->orderBy('created_at', 'DESC')->paginate(10);
            return view('ajax.clinic_staff_details', [
                'results' => $results,
                'activeTab' => $tab,
            ])->render();
        } else if ($tab == 'prescriptions') {
            $query = $user->ownPrescriptions()->with(['patient' => function ($q) {
                $q->select('id', 'name', 'email');
            }]);

            // Search by patient name
            if ($request->filled('search')) {
                $query->whereHas('patient', function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('email', 'like', '%' . $request->search . '%');
                });
            }

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Filter by type
            if ($request->filled('type')) {
                $query->where('prescription_type', $request->type);
            }

            $results = $query->orderBy('created_at', 'DESC')->paginate(10);
            return view('ajax.clinic_staff_details', [
                'results' => $results,
                'activeTab' => $tab,
            ])->render();
        }

        return response()->json(['error' => 'Invalid tab'], 400);
    }
    public function socialMedia()
    {
        $setting = Setting::where('id', 1)->first();
        return view('cms.social_media', compact('setting'));
    }
    public function editSocialMedia(Request $request)
    {
        $setting = Setting::where('id', 1)->first();
        $setting->update($request->all());
        return redirect()->route('cms')->with(['type' => 'success', 'message' => 'Social Media Updated Succesfully!']);
    }

    public function addPrescription(Request $request, $id = null)
    {
        $user = auth()->user();
        $doctors = null;
        $prescription = null;

        if ($user->hasRole('staff') && $user->profile->role == 'Receptionist') {
            $doctors = User::where('user_id', $user->createdBy->id)
                          ->whereHas('profile', function ($q) {
                              $q->where('role', 'doctor');
                          })->get();
        }

        // If prescription ID is provided, fetch the prescription data
        if ($id) {
            $prescription = Prescription::with(['patient', 'doctor', 'inventories'])
                                     ->where('order_id', $id)
                                     ->firstOrFail();
        }

        return view('dashboard.staff.add_prescription', compact('doctors', 'prescription'));
    }
}
