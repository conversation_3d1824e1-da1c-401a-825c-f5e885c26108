<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrescriptionInventory extends Model
{
    use HasFactory;

    protected $fillable = [
        'prescription_id',
        'inventory_id',
        'prescribed_units',
        'total_price',
        'name',
        'dosage'
    ];

    // Define the relationship to the Prescription model
    public function prescription()
    {
        return $this->belongsTo(Prescription::class);
    }

    // Define the relationship to the Inventory model
    public function inventory()
    {
        return $this->belongsTo(Inventory::class);
    }
}
