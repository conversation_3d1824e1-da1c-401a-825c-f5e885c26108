@extends('layouts.app')

@section('content')
    <div class="d-flex flex-column flex-column-fluid flex-lg-row register_page">
        <div class="d-flex flex-center w-lg-50 px-10">
            <div class="w-100 px-10">
                <div class="mb-20">
                    <a href="{{asset('/')}}" class="mb-7">
                        <img alt="Logo" src="{{asset('')}}{{ App\Models\Setting::first()->logo??'' }}"/>
                    </a>
                </div>

                <div class="new-staff-modal login-header pb-0 w-100">
                    <div class="register_page_header">
                        <h4 class="modal-title white-color inter w-100 text-center">Staff Login</h4>
                    </div>

                    <div class="row py-10 mx-5">
                        <div class="col-lg-12">
                            <form class="form w-100" novalidate="novalidate" method="POST" action="{{ route('staff.login.submit') }}">
                                @csrf
                                <div class="patients-email fv-row mb-8">
                                    <h5 class="deep-charcoal-blue inter">Email <span style="color:var(input-text-gray);">*</span></h5>
                                    <input id="email" type="email" placeholder="Email"
                                           class="form-control bg-transparent px-0 @error('email') is-invalid @enderror"
                                           name="email" value="{{ old('email') }}" required autocomplete="email" autofocus>

                                    @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                    @enderror
                                </div>

                                <div class="position-relative mb-3">
                                    <div class="pas">
                                        <label for="password">Password <span style="color:var(input-text-gray)">*</span></label>
                                        <input id="password" type="password" placeholder="Password" maxlength="20"
                                               class="form-control bg-transparent px-0 @error('password') is-invalid @enderror"
                                               name="password" required autocomplete="current-password">
                                        <span id="toggle-password" class="btn-sm btn-icon position-absolute translate-middle top-50 end-0 right-10 pt-9">
                                            <i class="fa-solid fa-eye"></i>
                                            <i class="fa-solid fa-eye-slash d-none"></i>
                                        </span>
                                        @if (!session('error'))
                                            @error('password')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                            @enderror
                                        @endif
                                    </div>
                                </div>

                                <div class="mb-8">
                                    <div></div>
                                    <a href="{{ route('password.request') }}" class="gray-text">Forgot Password ?</a>
                                </div>

                                <div class="d-grid mb-10">
                                    <button type="submit" id="kt_sign_in_submit" class="gradient_modal_approve white-color roboto fs-14 fw-400 modal-save w-md-50 m-auto">
                                        <span class="indicator-label">Sign In</span>
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex w-md-50 flex-column-fluid flex-lg-row-auto flex-column align-items-center register-bg-img justify-content-center">
            <div>
                <img class="login_image w-100 h-100 img_fluid"
                     src="{{ asset('website') }}/assets/media/images/banner-img.png" alt="image" />
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const passwordType = passwordField.type;

            if (passwordType === 'password') {
                passwordField.type = 'text';
                this.querySelector('.fa-eye-slash').classList.remove('d-none');
                this.querySelector('.fa-eye').classList.add('d-none');
            } else {
                passwordField.type = 'password';
                this.querySelector('.fa-eye').classList.remove('d-none');
                this.querySelector('.fa-eye-slash').classList.add('d-none');
            }
        });
    </script>
@endpush
