@extends('theme.layout.master')
@section('content')
<form id="prescription-form" method="POST" action="{{ route('prescriptions.store') }}">
    <div class="row mt-5 p-0 m-0">
        @csrf
        @if($prescription)
            <input type="hidden" name="order_id" value="{{ $prescription->order_id }}">
        @endif
        <div class="col-md-4">
            <div class="purple-card h-100">
                @if (auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Receptionist')
                <div class="col-lg-12">
                    <div class="patients-name mb-7">
                        <h5 class="deep-charcoal-blue inter pb-5">Doctor</h5>
                        <select class="form-select" id="doctor_id" name="doctor_id" required>
                            <option value="">Select Doctor</option>
                            @foreach ($doctors as $doctor)
                            <option value="{{ $doctor->id }}" {{ $prescription && $prescription->doctor_id == $doctor->id ? 'selected' : '' }}>
                                {{ $doctor->name }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                @endif
                <div class="patients-name mb-7">
                    <label for="patient_id" class="form-field-title fs-18 fw-500 py-2">Patient</label>
                    <select name="patient_id" id="patient_id" class="form-select patient-select">
                        @if($prescription && $prescription->patient)
                            <option value="{{ $prescription->patient->id }}" selected>
                                {{ $prescription->patient->name }} ({{ $prescription->patient->email }})
                            </option>
                        @endif
                    </select>
                </div>

                <div id="patient-details" class="accordion" {{ $prescription && $prescription->patient ? '' : 'style=display:none;' }}>
                    <div class="accordion-item mb-5">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                Patient Details
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse show">
                            <div class="accordion-body" id="patient-info-content">
                                @if($prescription && $prescription->patient)
                                    <p><strong>Name:</strong> {{ $prescription->patient->name }}</p>
                                    <p><strong>Email:</strong> {{ $prescription->patient->email }}</p>
                                    <p><strong>Age:</strong> {{ $prescription->patient->profile->dob ? \Carbon\Carbon::parse($prescription->patient->profile->dob)->age : 'Outsourced' }}</p>
                                    <p><strong>Gender:</strong> {{ $prescription->patient->profile->gender ?? 'Outsourced' }}</p>
                                    <p><strong>Phone:</strong> {{ $prescription->patient->profile->mobile_number ?? 'Outsourced' }}</p>
                                    <p><strong>Address:</strong> {{ $prescription->patient->profile->address ?? 'Outsourced' }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <div class="prescription-type mb-7">
                    <h5 class="deep-charcoal-blue inter fs-18 fw-500 py-2">Prescription Type</h5>
                    <select class="form-select p-2" data-control="select2" name="prescription_type" id="prescription_type" data-placeholder="Select an option">
                        <option value="one_time" {{ $prescription && $prescription->prescription_type == 'one_time' ? 'selected' : '' }}>one time</option>
                        <option value="repeat" {{ $prescription && $prescription->prescription_type == 'repeat' ? 'selected' : '' }}>Repeat</option>
                    </select>
                </div>
                <div class="prescription-duration mb-7" id="prescriptionDuration" style="display: {{ $prescription && $prescription->prescription_type == 'repeat' ? 'block' : 'none' }};">
                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Prescription Duration</h5>
                    <select class="form-select p-2"  data-control="select2" name="repetitions" id="duration" data-placeholder="Select an option">
                        <option disabled {{ !$prescription ? 'selected' : '' }}>Select Duration</option>
                        @for($i = 1; $i <= 12; $i++)
                            <option value="{{ $i }}" {{ $prescription && $prescription->repetitions == $i ? 'selected' : '' }}>{{ $i }} Month</option>
                        @endfor
                    </select>
                </div>
            </div>
        </div>

        <!-- Column 2: Medicine and Dosage -->
        <div class="col-md-4">
            <div class="purple-card h-100">
                <div id="medicines-container">
                    @if($prescription && $prescription->inventories)
                        @foreach($prescription->inventories as $index => $medicine)
                            <div class="medicine-entry">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="patients-name mb-7">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="deep-charcoal-blue inter py-5 fs-18 fw-500">Medicine</h5>
                                                @if($index > 0)
                                                    <button type="button" class="remove-medicine-btn btn btn-link text-danger p-0">
                                                        <i class="fas fa-trash-alt text-danger"></i>
                                                    </button>
                                                @endif
                                            </div>
                                            <select class="form-select px-0 pt-0 select2Medicine" name="medicines[{{ $index }}][inventory_id]" required>
                                                <option value="{{ $medicine->id }}" selected>{{ $medicine->name }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="patients-name mb-7">
                                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Dosage Instruction</h5>
                                            <input type="text" placeholder="Type Dosage"
                                                name="medicines[{{ $index }}][dosage]"
                                                value="{{ $medicine->pivot->dosage }}"
                                                class="w-100 quantity-value fs-14 black-color ps-3" required />
                                        </div>
                                    </div>
                                    <div class="col-lg-12">
                                        <div class="patients-name mb-7 d-flex justify-content-between align-items-center">
                                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                            <div class="quantity-container d-flex align-items-center justify-content-end gap-3 border-bottom-0">
                                                <button type="button" class="quantity-btn decrease">−</button>
                                                <input type="text" name="medicines[{{ $index }}][prescribed_units]"
                                                    value="{{ $medicine->pivot->prescribed_units }}"
                                                    min="1" class="text-center quantity-value fs-18 black-color quantityInput p-0" required />
                                                <button type="button" class="quantity-btn increase">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="medicine-entry">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Medicine</h5>
                                <select class="form-select select2Medicine" name="medicines[0][inventory_id]" required></select>
                            </div>

                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Dosage Instruction</h5>
                                <input type="text" placeholder="Type Dosage" name="medicines[0][dosage]"
                                    class="w-100 quantity-value fs-14 black-color ps-3" required />
                            </div>

                            <div class="patients-name mb-7 d-flex justify-content-between align-items-center">
                                <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Number of Packs</h5>
                                <div class="quantity-container d-flex align-items-center justify-content-end gap-3 border-bottom-0">
                                    <button type="button" class="quantity-btn decrease">−</button>
                                    <input type="text" name="medicines[0][prescribed_units]" min="1"
                                        value="1" class="text-center quantity-value fs-18 black-color quantityInput p-0" required />
                                    <button type="button" class="quantity-btn increase">+</button>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="text-end">
                    <button type="button" id="add-medicine"
                        class="gradient_modal_approve white-color roboto fs-14 fw-400">Add Medicine</button>
                </div>
            </div>
        </div>

        <!-- Column 3: Notes, Type, Duration -->
        <div class="col-md-4">
            <div class="purple-card h-100">
                <div class="patients-name mb-7">
                    <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Prescription Notes</h5>
                    <textarea type="text" id="prescription" name="prescription" placeholder="Type Instructions"
                        class="w-100 quantity-value fs-14 black-color ps-3">{{ $prescription ? $prescription->prescription : '' }}</textarea>
                </div>
                <div class="col-lg-12 clinic-name mb-7">
                    <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Billed Amount</h5>
                    <div class="cashback input-group mb-5">
                        <div class="w-100 d-flex flex-row">
                            <span class="input-group-text round-0" id="basic-addon1">£</span>
                            <input type="text" class="form-control round-0" id="totalPrice"
                                value="{{ $prescription ? $prescription->total : '' }}"
                                placeholder="Enter the percentage you want to pay the prescriber"
                                aria-label="Username" aria-describedby="basic-addon1" disabled />
                        </div>
                    </div>
                </div>

                <div class="text-center mt-5">
                    <button type="submit" class="gradient_modal_approve white-color roboto fs-14 fw-400">
                        {{ $prescription ? 'Update Prescription' : 'Save Prescription' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('js')
<script>
    $(document).ready(function() {
        // Initialize patient select2
        function initializePatientSelect() {
            $('.patient-select').select2({
                placeholder: 'Search for patients',
                focusInput: true,
                ajax: {
                    url: "{{ route('patients.search') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data.map(patient => ({
                                id: patient.id,
                                text: `${patient.name} (${patient.email})`,
                                name: patient.name,
                                email: patient.email,
                                age: patient.age,
                                gender: patient.gender,
                                phone: patient.phone,
                                address: patient.address
                            }))
                        };
                    },
                    cache: true
                }
            });
        }

        let medicineCount = 1000;

        // Function to get all currently selected medicine IDs
        function getAllSelectedMedicineIds() {
            const selectedIds = [];
            $('.select2Medicine').each(function() {
                const $select = $(this);
                if ($select.data('select2')) {
                    const selectedData = $select.select2('data');
                    if (selectedData && selectedData.length > 0 && selectedData[0].id) {
                        selectedIds.push(selectedData[0].id);
                    }
                }
            });
            return selectedIds;
        }

        // Initialize a single select2 element
        function initializeSingleSelect2($select) {
            if ($select.data('select2')) {
                $select.select2('destroy');
            }

            $select.select2({
                placeholder: 'Search for medicines',
                minimumInputLength: 0,
                focusInput: true,
                width: '100%',
                ajax: {
                    url: "{{ route('medicines.search') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        const allSelectedIds = getAllSelectedMedicineIds();
                        const currentSelectedId = $select.val();
                        const excludeIds = allSelectedIds.filter(id => id !== currentSelectedId);

                        return {
                            q: params.term,
                            exclude_ids: excludeIds
                        };
                    },
                    processResults: function(data) {
                        const allSelectedIds = getAllSelectedMedicineIds();
                        const currentSelectedId = $select.val();
                        const excludeIds = allSelectedIds.filter(id => id !== currentSelectedId);
                        const filteredData = data.filter(medicine => !excludeIds.includes(medicine.id));

                        return {
                            results: filteredData.map(medicine => ({
                                id: medicine.id,
                                text: medicine.name,
                                name: medicine.name,
                                sale_price: medicine.sale_price,
                                cost_price: medicine.cost_price,
                                pack_size: medicine.pack_size,
                                pip_code: medicine.pip_code,
                            }))
                        };
                    },
                    cache: true
                }
            }).on('select2:select', function(e) {
                const selectedId = e.params.data.id;
                const allSelectedIds = getAllSelectedMedicineIds();
                const currentSelectedId = $select.val();

                if (allSelectedIds.filter(id => id !== currentSelectedId).includes(selectedId)) {
                    e.preventDefault();
                    $select.val(null).trigger('change');
                    Swal.fire({
                        title: 'Error!',
                        text: 'This medicine is already selected in another entry.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                updateTotalPrice();
            }).on('select2:unselect', function() {
                updateTotalPrice();
            });

            // Prevent programmatic value setting of duplicates
            const originalVal = $select.val;
            $select.val = function(value) {
                if (value) {
                    const allSelectedIds = getAllSelectedMedicineIds();
                    const currentSelectedId = $select.val();
                    if (allSelectedIds.filter(id => id !== currentSelectedId).includes(value)) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'This medicine is already selected in another entry.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        return $select;
                    }
                }
                return originalVal.apply(this, arguments);
            };
        }

        // Initialize all medicine selects
        function initializeMedicineSelects() {
            $('.select2Medicine').each(function() {
                initializeSingleSelect2($(this));
            });
        }

        // Initialize both select2s
        initializePatientSelect();
        initializeMedicineSelects();

        // Handle patient selection
        $('#patient_id').on('select2:select', function(e) {
            const patient = e.params.data;
            $('#patient-info-content').html(`
                <p><strong>Name:</strong> ${patient.name}</p>
                <p><strong>Email:</strong> ${patient.email}</p>
                <p><strong>Age:</strong> ${patient.age ?? 'Outsourced'}</p>
                <p><strong>Gender:</strong> ${patient.gender ?? 'Outsourced'}</p>
                <p><strong>Phone:</strong> ${patient.phone ?? 'Outsourced'}</p>
                <p><strong>Address:</strong> ${patient.address ?? 'Outsourced'}</p>
            `);
            $('#patient-details').show();
        });

        // Handle prescription type change
        $('#prescription_type').on('change', function() {
            if ($(this).val() === 'repeat') {
                $('#prescriptionDuration').show();
                $('#duration').prop('disabled', false);
            } else {
                $('#prescriptionDuration').hide();
                $('#duration').prop('disabled', true);
            }
        });

        // Handle form submission
        $('#prescription-form').on('submit', function(event) {
            event.preventDefault();
            var $button = $('#save_prescription');
            $button.prop('disabled', true);

            var formData = $(this).serialize();

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                success: function(response) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Prescription created successfully',
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href =
                                "{{ route('prescriptions.index') }}";
                        }
                    });
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        var errors = xhr.responseJSON.errors;
                        showValidationErrors(errors);
                        showSweetAlertErrors(errors);
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                complete: function() {
                    $button.prop('disabled', false);
                }
            });
        });

        // Add medicine button click handler
        $('#add-medicine').on('click', function() {
            const medicineContainer = $('#medicines-container');
            const newMedicineEntry = `
                <div class="medicine-entry">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="patients-name mb-7">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="deep-charcoal-blue inter py-5 fs-18 fw-500">Medicine</h5>
                                    <button type="button" class="remove-medicine-btn btn btn-link text-danger p-0">
                                        <i class="fas fa-trash-alt text-danger"></i>
                                    </button>
                                </div>
                                <select class="form-select px-0 pt-0 select2Medicine" name="medicines[${medicineCount}][inventory_id]" required>
                                    <option value="">Select Medicine</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Dosage Instruction</h5>
                                <input type="text" placeholder="Type Dosage"
                                    name="medicines[${medicineCount}][dosage]" required
                                    class="w-100 quantity-value fs-14 black-color ps-3" />
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <div class="patients-name mb-7 d-flex justify-content-between align-items-center">
                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                <div class="quantity-container d-flex align-items-center justify-content-end gap-3 border-bottom-0">
                                    <button type="button" class="quantity-btn decrease">−</button>
                                    <input type="text" name="medicines[${medicineCount}][prescribed_units]"
                                        value="1" min="1" required
                                        class="text-center quantity-value fs-18 black-color quantityInput p-0" />
                                    <button type="button" class="quantity-btn increase">+</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            medicineContainer.append(newMedicineEntry);

            // Initialize the new select2 after a small delay to ensure DOM is ready
            setTimeout(() => {
                const newSelect = $('.select2Medicine').last();
                initializeSingleSelect2(newSelect);
            }, 0);

            medicineCount++;
        });

        // Quantity button handlers
        $(document).on('click', '.quantity-btn.increase', function(e) {
            e.preventDefault();
            const input = $(this).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;
            input.val(value + 1).trigger('change');
            updateTotalPrice();
        });

        $(document).on('click', '.quantity-btn.decrease', function(e) {
            e.preventDefault();
            const input = $(this).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;
            input.val(value > 1 ? value - 1 : 1).trigger('change');
            updateTotalPrice();
        });

        // Remove medicine button handler
        $(document).on('click', '.remove-medicine-btn', function() {
            const medicineEntry = $(this).closest('.medicine-entry');
            const selectElement = medicineEntry.find('.select2Medicine');

            // Destroy select2 before removing
            if (selectElement.data('select2')) {
                selectElement.select2('destroy');
            }

            medicineEntry.remove();
            updateTotalPrice();
        });

        // Update total price function
        function updateTotalPrice() {
            let totalPrice = 0;
            let processedCount = 0;
            const totalMedicines = $('.select2Medicine').length;

            $('#totalPrice').val('0.00');

            $('.select2Medicine').each(function() {
                const $select = $(this);

                // Check if select2 is initialized and has data
                if (!$select.data('select2') || !$select.hasClass('select2-hidden-accessible')) {
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                    return;
                }

                try {
                    const selectedData = $select.select2('data');
                    if (selectedData && selectedData.length > 0) {
                        const selectedMedicine = selectedData[0];
                        const quantity = $select.closest('.medicine-entry').find('.quantityInput').val();

                        if (selectedMedicine && selectedMedicine.id) {
                            const url = "{{ url('/get-medicine-details') }}/" + selectedMedicine.id;

                            $.ajax({
                                url: url,
                                type: 'GET',
                                success: function(response) {
                                    if (response.success) {
                                        const medicineData = response.data;
                                        const pack_size = medicineData.pack_size;
                                        const cost_price = medicineData.cost_price;

                                        const sale_price_per_pack = cost_price * 2;
                                        const cost_per_tablet = sale_price_per_pack / pack_size;
                                        const prescribedUnits = parseInt(quantity, 10) || 1;
                                        const minimumCharge = 12.95;
                                        const medicinePrice = Math.max(cost_per_tablet * prescribedUnits, minimumCharge);

                                        totalPrice += medicinePrice;
                                    }

                                    processedCount++;
                                    if (processedCount === totalMedicines) {
                                        $('#totalPrice').val(totalPrice.toFixed(2));
                                    }
                                },
                                error: function(err) {
                                    console.error('Error fetching medicine details: ', err);
                                    processedCount++;
                                    if (processedCount === totalMedicines) {
                                        $('#totalPrice').val(totalPrice.toFixed(2));
                                    }
                                }
                            });
                        } else {
                            processedCount++;
                            if (processedCount === totalMedicines) {
                                $('#totalPrice').val(totalPrice.toFixed(2));
                            }
                        }
                    } else {
                        processedCount++;
                        if (processedCount === totalMedicines) {
                            $('#totalPrice').val(totalPrice.toFixed(2));
                        }
                    }
                } catch (error) {
                    console.warn('Select2 data access error:', error);
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                }
            });

            if (totalMedicines === 0) {
                $('#totalPrice').val('0.00');
            }
        }
    });
</script>
<script>
    // Function to display validation errors in the modal
    function showValidationErrors(errors) {
        // Loop through the errors and display them
        $.each(errors, function(field, messages) {
            // Find the corresponding input field and show the error message
            var inputField = $('[name="' + field + '"]');
            var errorMessage = '<div class="alert alert-danger">' + messages.join('<br>') + '</div>';

            // Show the error message below the input field
            inputField.closest('.form-group').append(errorMessage);
        });
    }

    function sanitizeInput(event) {
        const input = event.target;
        let value = parseInt(input.value, 10);

        if (isNaN(value) || value < 0) {
            value = 0;
        }

        input.value = value;
    }

    function increaseQuantity(button) {
        const input = button.closest(".quantity-container").querySelector(".quantityInput");
        let value = parseInt(input.value, 10);

        value = isNaN(value) ? 1 : value; // If NaN, set it to 1 (default value)
        input.value = value + 1; // Increase quantity by 1
        updateTotalPrice();
    }

    function decreaseQuantity(button) {
        const input = button.closest(".quantity-container").querySelector(".quantityInput");
        let value = parseInt(input.value, 10);

        value = isNaN(value) ? 1 : value; // If NaN, set it to 1 (default value)

        // Prevent value from going below 1
        input.value = value > 1 ? value - 1 : 1; // Decrease quantity by 1 but not below 1
        updateTotalPrice();
    }

    document.addEventListener("DOMContentLoaded", function() {
        const inputs = document.querySelectorAll(".quantityInput");
        inputs.forEach(input => {
            input.addEventListener("input", sanitizeInput);
        });
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function() {
        const typeSelect = document.getElementById("prescription_type");
        const durationDiv = document.getElementById("prescriptionDuration");
        const durationInput = document.getElementById("duration");

        function toggleDuration() {
            if (typeSelect.value === "repeat") {
                durationDiv.style.display = "block";
                durationInput.disabled = false;
            } else {
                durationDiv.style.display = "none";
                durationInput.disabled = true;
            }
        }

        typeSelect.addEventListener("change", toggleDuration);

        // Run once on load in case default is recurring
        toggleDuration();
    });
</script>
@endpush
