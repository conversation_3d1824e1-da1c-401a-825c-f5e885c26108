@extends('theme.layout.master')
@section('content')
    <form id="prescription-form" method="POST" action="{{ route('prescriptions.store') }}">
        <div class="row mt-5 p-0 m-0">
            @csrf
            @if ($prescription)
                <input type="hidden" name="order_id" value="{{ $prescription->order_id }}">
            @endif
            <div class="col-md-4">
                <div class="purple-card h-100">
                    @if (auth()->user()->hasRole('staff') && auth()->user()->profile->role == 'Receptionist')
                        <div class="col-lg-12">
                            <div class="patients-name mb-7">
                                <h5 class="deep-charcoal-blue inter pb-5">Doctor</h5>
                                <select class="form-select" id="doctor_id" name="doctor_id" required>
                                    <option value="">Select Doctor</option>
                                    @foreach ($doctors as $doctor)
                                        <option value="{{ $doctor->id }}"
                                            {{ $prescription && $prescription->doctor_id == $doctor->id ? 'selected' : '' }}>
                                            {{ $doctor->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="patients-name mb-7">
                        <label for="patient_id" class="form-field-title fs-18 fw-500 py-2">Patient</label>
                        <select name="patient_id" id="patient_id" class="form-select patient-select">
                            @if ($prescription && $prescription->patient)
                                <option value="{{ $prescription->patient->id }}" selected>
                                    {{ $prescription->patient->name }} ({{ $prescription->patient->email }})
                                </option>
                            @endif
                        </select>
                    </div>

                    <div id="patient-details" class="accordion"
                        {{ $prescription && $prescription->patient ? '' : 'style=display:none;' }}>
                        <div class="accordion-item mb-5">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                    Patient Details
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show">
                                <div class="accordion-body" id="patient-info-content">
                                    @if ($prescription && $prescription->patient)
                                        <p><strong>Name:</strong> {{ $prescription->patient->name }}</p>
                                        <p><strong>Email:</strong> {{ $prescription->patient->email }}</p>
                                        <p><strong>Age:</strong>
                                            {{ $prescription->patient->profile->dob ? \Carbon\Carbon::parse($prescription->patient->profile->dob)->age : 'Outsourced' }}
                                        </p>
                                        <p><strong>Gender:</strong>
                                            {{ $prescription->patient->profile->gender ?? 'Outsourced' }}</p>
                                        <p><strong>Phone:</strong>
                                            {{ $prescription->patient->profile->mobile_number ?? 'Outsourced' }}</p>
                                        <p><strong>Address:</strong>
                                            {{ $prescription->patient->profile->address ?? 'Outsourced' }}</p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="prescription-type mb-7">
                        <h5 class="deep-charcoal-blue inter fs-18 fw-500 py-2">Prescription Type</h5>
                        <select class="form-select p-2" data-control="select2" name="prescription_type"
                            id="prescription_type" data-placeholder="Select an option">
                            <option value="one_time"
                                {{ $prescription && $prescription->prescription_type == 'one_time' ? 'selected' : '' }}>one
                                time</option>
                            <option value="repeat"
                                {{ $prescription && $prescription->prescription_type == 'repeat' ? 'selected' : '' }}>
                                Repeat</option>
                        </select>
                    </div>
                    <div class="prescription-duration mb-7" id="prescriptionDuration"
                        style="display: {{ $prescription && $prescription->prescription_type == 'repeat' ? 'block' : 'none' }};">
                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Prescription Duration</h5>
                        <select class="form-select p-2" data-control="select2" name="repetitions" id="duration"
                            data-placeholder="Select an option">
                            <option disabled {{ !$prescription ? 'selected' : '' }}>Select Duration</option>
                            @for ($i = 1; $i <= 12; $i++)
                                <option value="{{ $i }}"
                                    {{ $prescription && $prescription->repetitions == $i ? 'selected' : '' }}>
                                    {{ $i }} Month</option>
                            @endfor
                        </select>
                    </div>
                </div>
            </div>

            <!-- Column 2: Medicine and Dosage -->
            <div class="col-md-4">
                <div class="purple-card h-100">
                    <div id="medicines-container">
                        @if ($prescription && $prescription->inventories)
                            @foreach ($prescription->inventories as $index => $medicine)
                                <div class="medicine-entry">
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <div class="patients-name mb-7">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <h5 class="deep-charcoal-blue inter py-5 fs-18 fw-500">Medicine</h5>
                                                    @if ($index > 0)
                                                        <button type="button"
                                                            class="remove-medicine-btn btn btn-link text-danger p-0">
                                                            <i class="fas fa-trash-alt text-danger"></i>
                                                        </button>
                                                    @endif
                                                </div>
                                                <select class="form-select px-0 pt-0 select2Medicine"
                                                    name="medicines[{{ $index }}][inventory_id]" required>
                                                    <option value="{{ $medicine->id }}" selected>{{ $medicine->name }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-12">
                                            <div class="patients-name mb-7">
                                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Dosage Instruction
                                                </h5>
                                                <input type="text" placeholder="Type Dosage"
                                                    name="medicines[{{ $index }}][dosage]"
                                                    value="{{ $medicine->pivot->dosage }}"
                                                    class="w-100 quantity-value fs-14 black-color ps-3" required />
                                            </div>
                                        </div>
                                        <div class="col-lg-12 pack-size-display">
                                            <div class="patients-name mb-7">
                                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                                <div class="pack-size-info p-3 bg-light rounded">
                                                    <span class="pack-size-text fs-16 fw-500">{{ $medicine->pack_size }} tablets per pack</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-12">
                                            <div
                                                class="patients-name mb-7 d-flex justify-content-between align-items-center">
                                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                                <div
                                                    class="quantity-container d-flex align-items-center justify-content-end gap-3 border-bottom-0">
                                                    <button type="button" class="quantity-btn decrease">−</button>
                                                    <input type="text"
                                                        name="medicines[{{ $index }}][prescribed_units]"
                                                        value="{{ $medicine->pivot->prescribed_units }}" min="1"
                                                        class="text-center quantity-value fs-18 black-color quantityInput p-0"
                                                        required />
                                                    <button type="button" class="quantity-btn increase">+</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="medicine-entry">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Medicine</h5>
                                    <select class="form-select select2Medicine" name="medicines[0][inventory_id]"
                                        required></select>
                                </div>

                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Dosage Instruction</h5>
                                    <input type="text" placeholder="Type Dosage" name="medicines[0][dosage]"
                                        class="w-100 quantity-value fs-14 black-color ps-3" required />
                                </div>

                                <div class="col-lg-12 pack-size-display" style="display: none;">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                        <div class="pack-size-info p-3 bg-light rounded">
                                            <span class="pack-size-text fs-16 fw-500"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="patients-name mb-7 d-flex justify-content-between align-items-center">
                                    <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Number of Packs</h5>
                                    <div
                                        class="quantity-container d-flex align-items-center justify-content-end gap-3 border-bottom-0">
                                        <button type="button" class="quantity-btn decrease">−</button>
                                        <input type="text" name="medicines[0][prescribed_units]" min="1"
                                            value="1"
                                            class="text-center quantity-value fs-18 black-color quantityInput p-0"
                                            required />
                                        <button type="button" class="quantity-btn increase">+</button>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <div class="text-end">
                        <button type="button" id="add-medicine"
                            class="gradient_modal_approve white-color roboto fs-14 fw-400">Add Medicine</button>
                    </div>
                </div>
            </div>

            <!-- Column 3: Notes, Type, Duration -->
            <div class="col-md-4">
                <div class="purple-card h-100">
                    <div class="patients-name mb-7">
                        <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Prescription Notes</h5>
                        <textarea type="text" id="prescription" name="prescription" placeholder="Type Instructions"
                            class="w-100 quantity-value fs-14 black-color ps-3">{{ $prescription ? $prescription->prescription : '' }}</textarea>
                    </div>
                    <div class="col-lg-12 clinic-name mb-7">
                        <h5 class="deep-charcoal-blue inter pb-2 fs-18 fw-500">Billed Amount</h5>
                        <div class="cashback input-group mb-5">
                            <div class="w-100 d-flex flex-row">
                                <span class="input-group-text round-0" id="basic-addon1">£</span>
                                <input type="text" class="form-control round-0" id="totalPrice"
                                    value="{{ $prescription ? $prescription->total : '' }}"
                                    placeholder="Enter the percentage you want to pay the prescriber"
                                    aria-label="Username" aria-describedby="basic-addon1" disabled />
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-5">
                        <button type="submit" class="gradient_modal_approve white-color roboto fs-14 fw-400">
                            {{ $prescription ? 'Update Prescription' : 'Save Prescription' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@push('js')
    <!-- <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script> -->

    <script>
        $(document).ready(function() {
            // Attach the click event to the submit button
            $('#save_prescription').click(function(event) {
                event.preventDefault(); // Prevent the default form submission
                var $button = $(this); // Reference to the button
                $button.prop('disabled', true); // Disable the button

                var formData = $('#prescription-form').serialize(); // Serialize the form data

                // Make the AJAX request
                $.ajax({
                    url: $('#prescription-form').attr('action'),
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response.success) {
                            // Redirect to prescription summary page
                            window.location.href = "{{ route('add_prescription') }}/" +
                                response
                                .order_id;
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: response.message ||
                                    'Something went wrong. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function(xhr) {
                        // If validation errors are returned, show them in the modal and in SweetAlert
                        if (xhr.status === 422) {
                            var errors = xhr.responseJSON.errors;
                            showValidationErrors(
                                errors); // Show errors below the input fields in the form
                            showSweetAlertErrors(errors); // Show errors in SweetAlert
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Something went wrong. Please try again.',
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    complete: function() {
                        $button.prop('disabled',
                            false); // Re-enable the button after request completes
                    }
                });
            });

            // Function to display validation errors in the form
            function showValidationErrors(errors) {
                // Clear any previous error messages
                $('.form-group .alert').remove();

                // Loop through the errors and display them in the form
                $.each(errors, function(field, messages) {
                    // Find the corresponding input field and show the error message
                    var inputField = $('[name="' + field + '"]');
                    var errorMessage = '<div class="alert alert-danger">' + messages.join('<br>') +
                        '</div>';

                    // Show the error message below the input field
                    inputField.closest('.form-group').append(errorMessage);
                });
            }

            // Function to display validation errors in SweetAlert
            function showSweetAlertErrors(errors) {
                var errorMessages = [];

                // Loop through the errors and create a list of error messages for SweetAlert
                $.each(errors, function(field, messages) {
                    errorMessages.push(messages.join('<br>'));
                });

                // Show SweetAlert with the error messages
                Swal.fire({
                    title: 'Validation Errors',
                    icon: 'error',
                    html: errorMessages.join('<br><br>'),
                    confirmButtonText: 'OK'
                });
            }
        });
    </script>
    <script>
        // Function to display validation errors in the modal
        function showValidationErrors(errors) {
            // Loop through the errors and display them
            $.each(errors, function(field, messages) {
                // Find the corresponding input field and show the error message
                var inputField = $('[name="' + field + '"]');
                var errorMessage = '<div class="alert alert-danger">' + messages.join('<br>') + '</div>';

                // Show the error message below the input field
                inputField.closest('.form-group').append(errorMessage);
            });
        }

        function sanitizeInput(event) {
            const input = event.target;
            let value = parseInt(input.value, 10);

            if (isNaN(value) || value < 0) {
                value = 0;
            }

            input.value = value;
        }

        function increaseQuantity(button) {

            const input = $(button).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;

            input.val(value + 1).trigger('change');
            updateTotalPrice();
        }

        function decreaseQuantity(button) {
            const input = $(button).siblings('.quantityInput');
            let value = parseInt(input.val(), 10);
            value = isNaN(value) ? 1 : value;
            input.val(value > 1 ? value - 1 : 1).trigger('change');
            updateTotalPrice();
        }

        document.addEventListener("DOMContentLoaded", function() {
            const inputs = document.querySelectorAll(".quantityInput");
            inputs.forEach(input => {
                input.addEventListener("input", sanitizeInput);
            });

            // Update initial medicine entry buttons
            const initialMedicineEntry = document.querySelector('.medicine-entry');
            if (initialMedicineEntry) {
                const quantityContainer = initialMedicineEntry.querySelector('.quantity-container');
                if (quantityContainer) {
                    quantityContainer.innerHTML = `
                        <button type="button" class="quantity-btn decrease">−</button>
                        <input type="text" placeholder="Add Quantity for the item"
                            name="medicines[0][prescribed_units]" min="1" required
                            value="1"
                            class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                        <button type="button" class="quantity-btn increase">+</button>
                    `;
                }
            }
        });
    </script>
    <script>
        // let medicineCount = document.getElementById('medicines-container').children.length + 1;
        let medicineCount = 1000;

        document.getElementById('add-medicine').addEventListener('click', function() {
            const medicineContainer = document.getElementById('medicines-container');
            const newMedicineEntry = document.createElement('div');
            newMedicineEntry.classList.add('medicine-entry');

            newMedicineEntry.innerHTML = `
                <div class="row">
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <div class="d-flex justify-content-between mx-3">
                                <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Medicine</h5>
                                <button type="button" class="remove-medicine-btn mb-3 text-danger border-0 bg-transparent p-0" title="Remove"><i class="fas fa-trash-alt" style="color:red;"></i></button>
                            </div>
                            <select class="form-select px-0 pt-0 select2Medicine" name="medicines[${medicineCount}][inventory_id]" required>
                                <option value="">Select Medicine</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Dosage Instruction</h5>
                            <div class="quantity-container d-flex align-items-center gap-5">
                                <input type="text" placeholder="Type Dosage"
                                    name="medicines[${medicineCount}][dosage]" required
                                    class="w-100 quantity-value fs-18 black-color" />
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 pack-size-display" style="display: none;">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                            <div class="pack-size-info p-3 bg-light rounded">
                                <span class="pack-size-text fs-16 fw-500"></span>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12">
                        <div class="patients-name mb-7">
                            <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                            <div class="quantity-container d-flex align-items-center gap-5 border-bottom-0">
                                <button type="button" class="quantity-btn decrease">−</button>
                                <input type="text" placeholder="Add Quantity for the item" value='1'
                                    name="medicines[${medicineCount}][prescribed_units]" min="1" required
                                    class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                <button type="button" class="quantity-btn increase">+</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            medicineContainer.appendChild(newMedicineEntry);
            medicineCount++;

            // Initialize select2 after adding to DOM
            setTimeout(function() {
                const newSelect = $('.select2Medicine').last();
                initializeSingleSelect2(newSelect);
            }, 0);
        });

        // Function to initialize a single select2 element
        function initializeSingleSelect2($select) {
            // Function to get all currently selected medicine IDs except the current select
            function getSelectedMedicineIds() {
                const selectedIds = [];
                $('.select2Medicine').each(function() {
                    const $currentSelect = $(this);
                    // Skip the current select element
                    if ($currentSelect[0] === $select[0]) return;

                    if ($currentSelect.data('select2')) {
                        const selectedData = $currentSelect.select2('data');
                        if (selectedData && selectedData.length > 0 && selectedData[0].id) {
                            selectedIds.push(selectedData[0].id);
                        }
                    }
                });
                return selectedIds;
            }

            // Initialize select2
            $select.select2({
                placeholder: 'Search for medicines',
                minimumInputLength: 0,
                focusInput: false,
                ajax: {
                    url: "{{ route('medicines.search') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        // Get all currently selected medicine IDs except the current select
                        const selectedIds = getSelectedMedicineIds();

                        return {
                            q: params.term, // search term
                            exclude_ids: selectedIds // send selected IDs to exclude
                        };
                    },
                    processResults: function(data) {
                        // Double check on client side to ensure no duplicates
                        const selectedIds = getSelectedMedicineIds();
                        const filteredData = data.filter(medicine => !selectedIds.includes(medicine.id));

                        return {
                            results: filteredData.map(medicine => ({
                                id: medicine.id,
                                text: medicine.name,
                                name: medicine.name,
                                sale_price: medicine.sale_price,
                                cost_price: medicine.cost_price,
                                pack_size: medicine.pack_size,
                                pip_code: medicine.pip_code,
                            }))
                        };
                    },
                    cache: true
                }
            });

            // Handle selection
            $select.on('select2:select', function(e) {
                const selectedId = e.params.data.id;
                const selectedIds = getSelectedMedicineIds();

                // Check if this medicine is already selected in another dropdown
                if (selectedIds.includes(selectedId)) {
                    e.preventDefault();
                    $select.val(null).trigger('change');
                    Swal.fire({
                        title: 'Error!',
                        text: 'This medicine is already selected in another entry.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                // Show pack size information
                const medicineEntry = $select.closest('.medicine-entry');
                const packSizeDisplay = medicineEntry.find('.pack-size-display');
                const packSizeText = medicineEntry.find('.pack-size-text');

                if (e.params.data.pack_size) {
                    packSizeText.text(`${e.params.data.pack_size}`);
                    packSizeDisplay.show();
                } else {
                    packSizeDisplay.hide();
                }

                updateTotalPrice();
            });

            // Handle unselect
            $select.on('select2:unselect', function() {
                // Hide pack size information
                const medicineEntry = $select.closest('.medicine-entry');
                const packSizeDisplay = medicineEntry.find('.pack-size-display');
                packSizeDisplay.hide();

                updateTotalPrice();
            });
        }

        function initializeMedicineSelect(index, medicine) {
            const selectElement = $('.select2Medicine').last();
            initializeSingleSelect2(selectElement);

        }

        function updateTotalPrice() {
            let totalPrice = 0;
            let processedCount = 0;
            const totalMedicines = $('.select2Medicine').length;
            const minimumCharge = @json(App\Models\Setting::first()->minimum_charge ?? 14.95);

            // If no medicines exist, set total to zero
            if (totalMedicines === 0) {
                $('#totalPrice').val('0.00');
                return;
            }

            $('.select2Medicine').each(function() {
                const $select = $(this);

                // Check if select2 is initialized
                if (!$select.data('select2') || !$select.hasClass('select2-hidden-accessible')) {
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if medicines exist and have valid selections
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                    return;
                }

                try {
                    const selectedData = $select.select2('data')[0];
                    if (selectedData && selectedData.id) {
                        const quantity = parseInt($select.closest('.medicine-entry').find(
                            'input[name$="[prescribed_units]"]').val(), 10) || 1;
                        const cost_price = parseFloat(selectedData.cost_price) || 0;

                        // Calculate price per pack (cost_price * 2 for markup)
                        const sale_price_per_pack = cost_price * 2;
                        const medicinePrice = sale_price_per_pack * quantity;

                        totalPrice += medicinePrice;
                    }

                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if there are actual medicine selections with prices
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                } catch (error) {
                    console.warn('Select2 data access error:', error);
                    processedCount++;
                    if (processedCount === totalMedicines) {
                        // Only apply minimum charge if there are actual medicine selections with prices
                        if (totalPrice > 0) {
                            totalPrice = Math.max(totalPrice, minimumCharge);
                        }
                        $('#totalPrice').val(totalPrice.toFixed(2));
                    }
                }
            });
        }
        // Event listener for removing a medicine row
        $(document).on('click', '.remove-medicine-btn', function() {
            const medicineEntry = $(this).closest('.medicine-entry');
            const selectElement = medicineEntry.find('.select2Medicine');

            // Clear the selection before removing
            if (selectElement.data('select2')) {
                selectElement.val(null).trigger('change');
            }

            medicineEntry.remove();
            updateTotalPrice();
        });
    </script>
    <script>
        $(document).ready(function() {
            // Initialize patient select2
            populatePatientDropdown();

            // Initialize existing medicine select2 elements
            $('.select2Medicine').each(function() {
                initializeSingleSelect2($(this));
            });

            // Prescription type and duration handling
            const $typeSelect = $('#prescription_type');
            const $durationDiv = $('#prescriptionDuration');
            const $durationInput = $('#duration');

            function toggleDuration() {
                if ($typeSelect.val() === "repeat") {
                    $durationDiv.show();
                    $durationInput.prop('disabled', false);
                } else {
                    $durationDiv.hide();
                    $durationInput.prop('disabled', true);
                }
            }

            $typeSelect.on('change', toggleDuration);

            // Run once on load in case default is recurring
            toggleDuration();

            // Function to add initial medicine entry
            function addInitialMedicineEntry() {
                const medicineRow = `
                    <div class="medicine-entry">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Medicine</h5>
                                    <select data-control="select2" data-dropdown-css-class="w-200px"
                                        class="form-select px-0 pt-0 select2Medicine"
                                        name="medicines[0][inventory_id]" required>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5">Dosage Instruction</h5>
                                    <div class="quantity-container d-flex align-items-center gap-5">
                                        <input type="text" placeholder="Type Dosage" name="medicines[0][dosage]"
                                            required class="w-100 quantity-value fs-18 black-color" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12 pack-size-display" style="display: none;">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                    <div class="pack-size-info p-3 bg-light rounded">
                                        <span class="pack-size-text fs-16 fw-500"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <div class="patients-name mb-7">
                                    <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                    <div class="quantity-container d-flex align-items-center gap-5 border-bottom-0">
                                        <button type="button" class="quantity-btn decrease">−</button>
                                        <input type="text" placeholder="Add Quantity for the item"
                                            name="medicines[0][prescribed_units]" min="1" required
                                            value="1"
                                            class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                        <button type="button" class="quantity-btn increase">+</button>
                                    </div>
                                </div>
                            </div>
                            <button type="button"
                                class="remove-medicine-btn mb-3 text-danger border-0 bg-transparent p-0"
                                title="Remove"><i class="fas fa-trash-alt"></i></button>
                        </div>
                    </div>
                `;
                $('#medicines-container').append(medicineRow);

                // Initialize select2 for the initial medicine entry
                setTimeout(function() {
                    const newSelect = $('.select2Medicine').last();
                    initializeSingleSelect2(newSelect);
                }, 0);
            }

            function populatePatientDropdown() {
                setTimeout(function() {
                    $('.patient-select').select2({
                        placeholder: 'Search for patients', // Placeholder text
                        minimumInputLength: 0, // Minimum input length to start searching
                        focusInput: false, // Disable focus to prevent aria-hidden issues
                        ajax: {
                            url: "{{ route('patients.search') }}", // URL to fetch data
                            dataType: 'json', // Expected data format
                            delay: 250, // Delay before requesting the data
                            data: function(params) {
                                return {
                                    q: params.term // Send search term to the backend
                                };
                            },
                            processResults: function(data) {
                                return {
                                    results: data.map(patient => ({
                                        id: patient.id,
                                        text: `${patient.name} (${patient.email})`, // Show name and email in dropdown
                                        name: patient.name,
                                        email: patient.email,
                                        age: patient.age,
                                        gender: patient.gender,
                                        phone: patient.phone,
                                        address: patient.address
                                    }))
                                };
                            },
                            cache: true // Cache results for better performance
                        }
                    }); // Ensure the selected patient is set and reflected
                }, 100);
            }

            $('#patient_id').on('select2:select', function(e) {
                const patient = e.params.data; // Get the selected patient's data
                $('#patient-info-content').html(`
                    <p><strong>Name:</strong> ${patient.name}</p>
                    <p><strong>Email:</strong> ${patient.email}</p>
                    <p><strong>Age:</strong> ${patient.age ?? 'Outsourced'}</p>
                    <p><strong>Gender:</strong> ${patient.gender ?? 'Outsourced'}</p>
                    <p><strong>Phone:</strong> ${patient.phone ?? 'Outsourced'}</p>
                    <p><strong>Address:</strong> ${patient.address ?? 'Outsourced'}</p>
                `);
                $('#patient-details').show(); // Show patient details section
            });

            // Function to clear the medicine rows
            function clearMedicineRows() {
                $('#medicines-container').empty(); // Clear all medicine rows
            }

            // Function to populate the medicine rows for editing
            function populateMedicineRows(medicines) {
                medicines.forEach((medicine, index) => {
                    const medicineRow = `
                        <div class="medicine-entry">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5">Medicine</h5>
                                        <select class="form-select px-0 pt-0 select2Medicine" name="medicines[${index}][inventory_id]" required>
                                            <option value="${medicine.inventory_id}" selected="selected">${medicine.name}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5">Dosage Instruction</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5">
                                            <input type="text" placeholder="Type Dosage"
                                                name="medicines[${index}][dosage]" value="${medicine.dosage}" required
                                                class="w-100 quantity-value fs-18 black-color" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12 pack-size-display">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Pack Size</h5>
                                        <div class="pack-size-info p-3 bg-light rounded">
                                            <span class="pack-size-text fs-16 fw-500">${medicine.pack_size}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="patients-name mb-7">
                                        <h5 class="deep-charcoal-blue inter pb-5 fs-18 fw-500">Number of Packs</h5>
                                        <div class="quantity-container d-flex align-items-center gap-5">
                                            <button type="button" class="quantity-btn decrease">−</button>
                                            <input type="text" placeholder="Add Quantity for the item" value='${medicine.prescribed_units}'
                                                name="medicines[${index}][prescribed_units]" min="1" required
                                                class="w-25 text-center quantity-value fs-18 black-color quantityInput p-0" />
                                            <button type="button" class="quantity-btn increase">+</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    $('#medicines-container').append(medicineRow);
                    const newSelect = $('.select2Medicine').last();
                    initializeSingleSelect2(newSelect);

                    // Pre-select the medicine in select2 and populate other fields
                    const selectedOption = {
                        id: medicine.inventory_id, // Assigning the inventory_id as id
                        text: medicine.name, // Name of the medicine
                        name: medicine.name,
                        sale_price: medicine.sale_price,
                        cost_price: medicine.cost_price,
                        pack_size: medicine.pack_size,
                        pip_code: medicine.pip_code,
                    };

                    // Find the newly created select2 element and set its value
                    const medicineSelect = $('.select2Medicine').last();
                    medicineSelect.val(selectedOption.id).trigger('change');
                });
            }

            // Event delegation for quantity buttons
            $(document).on('click', '.quantity-btn.increase', function(e) {
                e.preventDefault();
                increaseQuantity(this);
            });

            $(document).on('click', '.quantity-btn.decrease', function(e) {
                e.preventDefault();
                decreaseQuantity(this);
            });

            // Event delegation for quantity input changes
            $(document).on('change', '.quantityInput', function() {
                updateTotalPrice();
            });
        });
    </script>
@endpush
