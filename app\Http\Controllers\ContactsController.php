<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Contact;
use App\Http\Requests\ContactRequest;
use Illuminate\Support\Facades\Response;

class ContactsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $contacts = Contact::orderBy('created_at', 'DESC')->paginate(10);
        return view('contacts.index', ['contacts' => $contacts]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('contacts.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  ContactRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(ContactRequest $request)
    {
        $contact = new Contact;
        $contact->first_name = $request->input('first_name');
        $contact->last_name = $request->input('last_name');
        $contact->email = $request->input('email');
        $contact->phone_number = $request->input('phone_number');
        $contact->message = $request->input('message');
        $contact->save();

        return to_route('contacts.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $contact = Contact::findOrFail($id);
        return view('contacts.show', ['contact' => $contact]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $contact = Contact::findOrFail($id);
        return view('contacts.edit', ['contact' => $contact]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  ContactRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(ContactRequest $request, $id)
    {
        $contact = Contact::findOrFail($id);
        $contact->first_name = $request->input('first_name');
        $contact->last_name = $request->input('last_name');
        $contact->email = $request->input('email');
        $contact->phone_number = $request->input('phone_number');
        $contact->message = $request->input('message');
        $contact->save();

        return to_route('contacts.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->delete();

        return to_route('contacts.index');
    }
    public function download()
    {
        $contacts = Contact::all(['first_name', 'last_name', 'email', 'phone_number', 'message', 'status']);
        $csvHeader = ['First Name', 'Last Name', 'Email', 'Phone Number', 'Message', 'Status'];
        $filename = 'contacts.csv';
        $callback = function () use ($contacts, $csvHeader) {
            $file = fopen('php://output', 'w');
            fputcsv($file, $csvHeader);

            foreach ($contacts as $contact) {
                fputcsv($file, [
                    $contact->first_name,
                    $contact->last_name,
                    $contact->email,
                    $contact->phone_number,
                    $contact->message,
                    $contact->status == 0 ? 'Pending' : 'Resolved'
                ]);
            }

            fclose($file);
        };
        return Response::stream($callback, 200, [
            "Content-Type" => "text/csv",
            "Content-Disposition" => "attachment; filename=$filename",
        ]);
    }
    public function contactStatusUpdate($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->status = 1;
        $contact->save();

        return redirect()->route('contacts.index')->with([
            'type' => 'success',
            'message' => 'Contact status updated successfully',
        ]);
    }
    public function contactSearchFilter(Request $request)
    {
        $query = Contact::query();
        if ($request->start_date && $request->end_date) {
            $query->whereBetween('created_at', [
                $request->start_date . ' 00:00:00',
                $request->end_date . ' 23:59:59'
            ]);
        }
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        $contacts = $query->orderBy('created_at', 'desc')->get();
        return view('ajax.contact', compact('contacts'))->render();
    }
}
